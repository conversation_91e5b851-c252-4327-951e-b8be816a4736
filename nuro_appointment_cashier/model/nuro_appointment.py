# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
from datetime import datetime

from dateutil.relativedelta import relativedelta
from odoo.exceptions import UserError

from odoo import api, fields, models, _


class NuroAppointment(models.Model):
    _inherit = 'nuro.appointment'

    PAYMENT_METHOD = [
        ('cash', 'Cash'),
        ('credit', 'Credit')
    ]

    BILL_TO_TYPE = [
        ('patient', 'Patient'),
        ('sponsor', 'Sponsor'),
        ('employee', 'Employee')
    ]

    # product and charges details
    appointment_charges = fields.Float('Appointment Charge', tracking=True)
    discount_amount = fields.Float('Discount Amount', tracking=True, copy=False)
    total_amount = fields.Float('Total Amount', compute='get_total_amount_value', store=True)
    refund_amount = fields.Float('Refund Amount', copy=False)
    payment_method = fields.Selection(PAYMENT_METHOD, copy=False)
    memo = fields.Char('Memo')
    invoice_count = fields.Integer('Invoice', compute='_invoice_count_appointment')
    description_hospital = fields.Char('Description')
    expense_to_hospital = fields.Boolean('Expense To Hospital')
    employee_id = fields.Many2one('hr.employee', string='Employee')
    accounting_date = fields.Date('Accounting Date')
    refund_date = fields.Date('Refund Date')
    appointment_info_id = fields.Many2one('appointment.cancellation.reason', string='Cancellation Reason')
    invoice_id = fields.Many2one('account.move', string='Invoice')
    refund_invoice_id = fields.Many2one('account.move', string='Refund Invoice')
    cashier_user_id = fields.Many2one('res.users', string='User')
    non_refundable = fields.Boolean('Non Refundable', default=False)
    no_queue = fields.Boolean(default=False)

    payment_type = fields.Selection([
        ('cash', 'Cash'),
        ('credit', 'Credit'),
        ('expense', 'Expense')
    ], string='Payment Type')
    bill_to_type = fields.Selection(BILL_TO_TYPE, string='Bill To Type')
    bill_to_user_id = fields.Many2one('res.partner', string='Bill To Partner')
    expense_record_id = fields.Many2one('hospital.expense.record', string='Hospital Expense Record')
    allow_follow_up_days = fields.Integer(string='Allow Follow Up Days')
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    ignore_ar = fields.Boolean('Ignore A/R', tracking=True)

    def confirm_appointment_model(self):
        """Confirm Appointment"""
        return True

    def _follow_up_validation(self):
        """Check the Follow up for the patient and allow to make changes."""
        if self.env.company.allow_any_followup:
            last_appointment = self.search([
                ('patient_id', '=', self.patient_id.id),
                ('id', '!=', self.id),
                ('type', '=', 'new'),
                ('state', 'not in', ('draft', 'cancelled'))
            ], order='appointment_date desc', limit=1)
            if last_appointment:
                return self.action_appointment_schedule()
        query = """ select id from nuro_appointment where type = 'new' and speciality_id = %s and patient_id = %s
                    and id != %s and state not in ('draft', 'cancelled') order by id DESC limit 1""" % \
                (self.speciality_id.id, self.patient_id.id, self.id)
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        appointment_ids = result and result[0][0] or False
        appointment_id = self.env['nuro.appointment'].browse(appointment_ids)
        current_date = fields.Date.today()
        if self.env.company.is_follow_up_allow:
            if appointment_id:
                appointment_date = appointment_id.allow_follow_up_days \
                                   and appointment_id.appointment_date.date() + \
                                   relativedelta(days=appointment_id.allow_follow_up_days) or False
                if appointment_date:
                    if current_date <= appointment_date:
                        self.action_appointment_schedule()
                    else:
                        raise UserError(
                            _('You Can not create Follow Up for the Patient As we can not find any previous '
                              'Appointment!!!'))
                else:
                    raise UserError(_('You Can not create Follow Up for the Patient As we can not find any previous '
                                      'Appointment!!!'))
            else:
                raise UserError(_('You Can not create Follow Up for the Patient As we can not find any previous '
                                  'Appointment!!!'))
        else:
            raise UserError(_('Follow Up Appointment Creation is not allowed'))

    def create(self, vals):
        """doctor follow up days"""
        res = super(NuroAppointment, self).create(vals)
        if not res.company_id.is_follow_up_allow and res.type == 'new':
            if res.doctor_id.complementary_days > 0:
                res.allow_follow_up_days = res.doctor_id.complementary_days
            else:
                res.allow_follow_up_days = 0
        if res.company_id.is_follow_up_allow and res.type == 'new':
            if res.doctor_id.complementary_days > 0:
                res.allow_follow_up_days = res.doctor_id.complementary_days
            else:
                res.allow_follow_up_days = res.company_id.allow_follow_up_base
        return res

    def get_action_context_vals_cash(self):
        """Get action Context"""
        vals = {
            'default_appointment_id': self.id,
            'default_total_amount': self.appointment_charges,
            'default_amount': self.appointment_charges
        }
        return vals

    def get_action_context_vals_credit(self):
        """Get action Context"""
        vals = {
            'default_appointment_id': self.id,
            'default_payment_type': 'credit',
            'default_bill_to_type': self.bill_to_type,
            'default_employee_id': self.employee_id and self.employee_id.id or False,
            'default_bill_to_user_id': self.bill_to_user_id.id,
            'default_amount': self.appointment_charges
        }
        return vals

    def payment_processing_validation(self):
        """Payment Processing Validation"""
        if self.state != 'draft':
            raise UserError(_('Record has been processed Already.!!!!'))

    def create_appointment_credit_payment(self):
        """Credit Appointment Payment"""
        self.payment_processing_validation()
        ctx = self.get_action_context_vals_credit()
        action = self.env.ref('nuro_appointment_cashier.action_appointment_credit_payment').read()[0]
        action['context'] = ctx
        return action

    def create_appointment_cash_payment(self):
        """Cash Appointment Payment"""
        self.payment_processing_validation()
        ctx = self.get_action_context_vals_cash()
        action = self.env.ref('nuro_appointment_cashier.action_nuro_appointment_cash_wizard').read()[0]
        action['context'] = ctx
        return action

    def process_request_action_view_payment(self):
        """Process Request Action View"""
        self.payment_processing_validation()
        action_cash = self.env.ref('nuro_appointment_cashier.action_nuro_appointment_cash_wizard').read()[0]
        action_credit = self.env.ref('nuro_appointment_cashier.action_appointment_credit_payment').read()[0]
        if self.payment_type == 'cash':
            ctx = self.get_action_context_vals_cash()
            action_cash['context'] = ctx
            return action_cash
        if self.payment_type in ('credit', 'expense'):
            ctx = self.get_action_context_vals_credit()
            action_credit['context'] = ctx
            return action_credit

    def reschedule_appointment_record(self):
        """Reschedule Appointment Record"""
        if self.state != 'patient_in':
            raise UserError(_('Record Has been processed already.!!!'))
        self.state = 'scheduled'

    def print_receipt(self):
        """Appointment Receipt printing"""
        return self.env.ref('nuro_appointment_cashier.action_nuro_appointment_thermal_receipt').report_action(self)

    def get_current_appointment_ref(self):
        """Get Current Appointment Ref"""
        return self.name

    def print_receipt_revisit(self):
        """Appointment Receipt printing"""
        return self.env.ref('nuro_appointment_cashier.action_nuro_appointment_thermal_receipt_revisit').report_action(
            self)

    def get_appointment_note(self):
        """Method to get the Note for the Appointment"""
        appointment_note = self.env.company.appointment_note
        return appointment_note

    def _invoice_count_appointment(self):
        """Compute method for Invoice Count"""
        # for rec in self:
        self.ensure_one()
        search_invoice = self.env['account.move'].search_count([('appointment_id', '=', self.id)])
        self.invoice_count = search_invoice

    def return_appointment_invoice(self):
        """Related invoice return menu"""
        return {
            'name': _('Appointment Invoice'),
            'view_mode': 'tree,form',
            'res_model': 'account.move',
            'view_id': False,
            'type': 'ir.actions.act_window',
            'domain': [('appointment_id', '=', self.id)],
            'context': {'create': False, 'delete': False, 'edit': False},
        }

    @api.depends('appointment_charges', 'discount_amount')
    def get_total_amount_value(self):
        """Compute Method for total Amount"""
        for rec in self:
            if rec.appointment_charges:
                rec.total_amount = rec.appointment_charges - rec.discount_amount

    @api.onchange('appointment_type', 'doctor_id', 'type')
    def onchange_appointment_type(self):
        """Appointment Charges Details"""
        if self.appointment_type:
            emergency_charges = self.env['product.product'].browse(self.env.company.appointment_emergency_product_id.id)
            if self.type != 'follow_up':
                if self.appointment_type == 'emergency':
                    self.appointment_charges = emergency_charges.list_price
                if self.appointment_type == 'normal' and self.doctor_id:
                    self.appointment_charges = self.doctor_id.consultancy_fee
            else:
                self.appointment_charges = 0.0
                self.total_amount = 0.0

    def get_product_ids(self):
        """Get Product Details"""
        product_id = False
        if self.appointment_type == 'emergency':
            product_id = self.env['product.product'].browse(self.env.company.appointment_emergency_product_id.id)
        if self.appointment_type == 'normal':
            product_id = self.env['product.product'].browse(self.env.company.appointment_normal_product_id.id)
        return product_id

    def _create_move_lines_appointment(self, amount):
        """creating appointment invoice line without discount"""
        product_id = self.env.company.appointment_normal_product_id
        if not product_id:
            raise UserError(_('Please configure the Product for the Appointment!!!'))
        vals = {
            'product_id': product_id.id,
            'name': "Appointment Charges for " + self.name,
            'price_unit': amount,
            'quantity': 1,
            'account_id': product_id.property_account_income_id.id or
                          product_id.categ_id.property_account_income_categ_id.id,
        }
        return vals

    def _create_move_lines_appointment_discount(self, amount):
        """creating discount invoice line for appointment"""
        product_id = self.env.company.appointment_normal_product_id
        account_id = self.env.company.appointment_discount_account_id
        if not account_id:
            raise UserError(_('Please configure the Discount account for appointment!!!'))
        if not product_id:
            raise UserError(_('Please configure the Product for the Appointment!!!'))
        self.discount_amount = amount
        vals = {
            'product_id': product_id.id,
            'name': "Appointment Charges for " + self.name,
            'price_unit': -amount,
            'quantity': 1,
            'account_id': account_id.id,
        }
        return vals

    def _create_invoice_dict(self, partner_id, invoice_line_ids, invoice_type):
        """preparing invoice Dictionary"""
        vals = {
            'partner_id': partner_id.id,
            'state': 'draft',
            'type': invoice_type,
            'appointment_invoice': True,
            'invoice_date': fields.datetime.now().date(),
            'ref': "Appointment # : " + self.name,
            'invoice_origin': self.name,
            'appointment_id': self.id,
            'invoice_line_ids': invoice_line_ids,
        }
        return vals

    def create_invoice_lines_appointment(self, amount, discount_amount):
        """Create invoice Lines"""
        curr_invoice_line = []
        amount = amount
        income_line = self._create_move_lines_appointment(amount=amount)
        if discount_amount > 0.0:
            income_line.update({
                'hms_discount': discount_amount
            })
        curr_invoice_line.append((0, 0, income_line))
        if discount_amount > 0.0:
            discount_line = self._create_move_lines_appointment_discount(
                amount=discount_amount)
            curr_invoice_line.append((0, 0, discount_line))
        return curr_invoice_line

    def create_invoice(self, amount, discount_amount, partner_id, invoice_type, user_id=False):
        """Method to create Invoice and return invoice ids in vals"""
        curr_invoice_line = self.create_invoice_lines_appointment(amount=amount, discount_amount=discount_amount)
        curr_invoice = self._create_invoice_dict(
            partner_id=partner_id,
            invoice_line_ids=curr_invoice_line,
            invoice_type=invoice_type
        )
        ctx = {'active_model': self._name, 'active_id': self.id}
        inv_ids = self.env['account.move'].with_context(ctx).create(curr_invoice)
        self.accounting_date = fields.datetime.now().date()
        return inv_ids

    def _create_invoice_payment(self, partner_id, amount, journal_id, payment_method_id, invoice_ids, payment_type):
        """Private Method to create Invoice Payment"""
        vals = {
            'amount': abs(amount),
            'journal_id': journal_id.id,
            'patient_id': self.patient_id.id,
            'payment_method_id': payment_method_id,
            'invoice_payment_type': 'automated',
            'currency_id': invoice_ids.currency_id.id,
            'payment_type': payment_type,
            'partner_id': partner_id.id,
            'partner_type': 'customer',
            'communication': ' '.join([ref for ref in invoice_ids.mapped('ref') if ref]),
            'invoice_ids': [(6, 0, invoice_ids.ids)]
        }
        return vals

    def create_payment(self, partner_id, amount, journal_id, payment_method_id, invoice_ids, payment_type):
        """Create Payment For With the Given attributes"""
        for rec in self:
            payment_ids = rec._create_invoice_payment(
                partner_id=partner_id,
                amount=amount,
                journal_id=journal_id,
                payment_method_id=payment_method_id,
                payment_type=payment_type,
                invoice_ids=invoice_ids
            )
            payments = rec.env['account.payment'].create(payment_ids)
            return payments

    def _create_debit_move_line_vals(self, partner_id, amount, account_id):
        """Debit Line Dictionary for the account move line"""
        debit_vals = {
            'name': self.name,
            'debit': abs(amount),
            'credit': 0.0,
            'partner_id': partner_id.id,
            'account_id': account_id.id,
            'date': datetime.now().date()
        }
        return debit_vals

    def _create_credit_move_line_vals(self, partner_id, amount, account_id):
        """Credit Line Dictionary for the account move line"""
        credit_vals = {
            'name': self.name,
            'debit': 0.0,
            'credit': amount,
            'partner_id': partner_id.id,
            'account_id': account_id.id,
            'date': datetime.now().date()
        }
        return credit_vals

    def _create_account_move_dict(self, journal_id, line_ids):
        """Creating Journal Entry for hospital Expense"""
        vals = {
            'type': 'entry',
            'journal_id': journal_id.id,
            'date': fields.Date.today(),
            'appointment_invoice': True,
            'ref': "Appointment # : " + self.name,
            'appointment_id': self.id,
            'patient_id': self.patient_id.id,
            'state': 'draft',
            'line_ids': line_ids
        }
        return vals

    def create_account_move(self, journal_id, partner_id, amount, employee_id):
        """Creating account move entry"""
        account_move_obj = self.env['account.move']
        for rec in self:
            if rec.appointment_type == 'emergency':
                product_id = self.env['product.product'].browse(self.env.company.appointment_emergency_product_id.id)
            else:
                product_id = self.env['product.product'].browse(self.env.company.appointment_normal_product_id.id)
            credit_account_id = product_id.property_account_income_id or \
                                product_id.categ_id.property_account_income_categ_id
            if not employee_id:
                account_id = self.env['account.account'].browse(self.env.company.hospital_expense_account_id.id)
            else:
                account_id = self.env['account.account'].browse(
                    self.env.company.hospital_employee_expense_account_id.id)
            if not product_id:
                raise UserError(_('Please configure the Appointment Product!!!'))
            if not account_id:
                raise UserError(_('Please configure Hospital Expense Account and Employee Expense Account!!!'))
            debit_line = rec._create_debit_move_line_vals(
                partner_id=partner_id, amount=amount, account_id=account_id)
            credit_line = rec._create_credit_move_line_vals(
                partner_id=partner_id, amount=amount, account_id=credit_account_id)
            line_ids = [(0, 0, debit_line), (0, 0, credit_line)]
            move = rec._create_account_move_dict(
                journal_id=journal_id, line_ids=line_ids)
            ctx = {'active_model': self._name, 'active_id': self.id}
            move_ids = account_move_obj.with_context(ctx).create(move)
            self.accounting_date = fields.datetime.now().date()
            return move_ids
