from odoo import api, fields, models


class HrExpense(models.Model):
    _inherit = 'hr.expense'

    business_unit_ids = fields.Many2many('business.unit', string='Business Unit')

    def _prepare_customer_vals(self, type):
        """
            Prepare customer values for expense-related invoices.
        :param type: The type of invoice to prepare (e.g., 'out_invoice', 'out_refund')
        :return: Dictionary containing prepared customer values
        """
        vals = super(HrExpense, self)._prepare_customer_vals(type)
        if 'invoice_line_ids' in vals:
            for line in vals['invoice_line_ids']:
                if isinstance(line, (tuple, list)) and len(line) == 3:
                    line[2].update({'business_unit_ids': [(6, 0, self.business_unit_ids.ids)]})
        return vals

    def _prepare_vendor_bill_vals(self, bill_type):
        """
        Prepare vendor bill values for expense-related invoices.
        :param bill_type: The type of vendor bill to prepare (e.g., 'in_invoice', 'in_refund')
        :return: Dictionary containing prepared vendor bill values
        """
        vals = super(HrExpense, self)._prepare_vendor_bill_vals(bill_type)
        if 'invoice_line_ids' in vals:
            for line in vals['invoice_line_ids']:
                if isinstance(line, (tuple, list)) and len(line) == 3:
                    line[2].update({'business_unit_ids': [(6, 0, self.business_unit_ids.ids)]})
        return vals
        