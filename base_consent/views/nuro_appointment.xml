<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="nuro_appointment_form_view_sheet_doctor_panel_consent_form" model="ir.ui.view">
            <field name="name">Appointment</field>
            <field name="model">nuro.appointment</field>
            <field name="inherit_id" ref="nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='appointment_type_id']" position="after">
                    <field name="consent_applicable"/>
                </xpath>
                <xpath expr="//page[@name='panel_request']" position="after">
                    <page name="consent_approve" string="Consent"
                          attrs="{'invisible':[('consent_applicable','=',False)]}">
                        <group>
                            <field name="consent_id" force_save="1" options="{'no_create':True,'no_open':True}"/>
                            <field name="responsible_person_line">
                                <tree editable="bottom">
                                    <field name="name" required="1"/>
                                    <field name="relation" required="1"/>
                                    <field name="mobile"/>
                                </tree>
                            </field>
                            <field name="description" force_save="1" readonly="1" widget="html"/>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>
    </data>
</odoo>