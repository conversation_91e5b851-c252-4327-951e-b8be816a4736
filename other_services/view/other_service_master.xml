<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="other_service_master_form_view" model="ir.ui.view">
            <field name="name">Other Service</field>
            <field name="model">other.service.master</field>
            <field name="arch" type="xml">
                <form string="Other Service Master">
                    <sheet>
                        <group>
                            <group>
                                <field name="active" invisible="1"/>
                                <widget name="web_ribbon" title="Archived" bg_color="bg-danger"
                                        attrs="{'invisible': [('active', '=', True)]}"/>
                                <field name="name" required="1"/>
                            </group>
                            <group>
                                <field name="unit_price"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="other_service_master_list_view" model="ir.ui.view">
            <field name="name">Other Service</field>
            <field name="model">other.service.master</field>
            <field name="arch" type="xml">
                <tree string="Other Service Master">
                    <field name="name"/>
                    <field name="unit_price"/>
                </tree>
            </field>
        </record>

        <record id="other_service_master_search_view" model="ir.ui.view">
            <field name="name">Other Service</field>
            <field name="model">other.service.master</field>
            <field name="arch" type="xml">
                <search string="Other Service Master">
                    <field name="name" string="Name"/>
                </search>
            </field>
        </record>

        <record id="other_service_action_view" model="ir.actions.act_window">
            <field name="name">Other Service</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">other.service.master</field>
            <field name="view_mode">tree,form</field>
        </record>

    </data>
</odoo>