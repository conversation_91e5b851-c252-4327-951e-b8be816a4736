# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import _, api, models
from odoo.exceptions import UserError


class AccountPayment(models.Model):
    _inherit = "account.payment"

    @api.constrains("partner_amount_due", "discount")
    def constrains_partner_amount_due_and_discount(self):
        """this is a constrains which give the discount and amount validation"""
        res = super().constrains_partner_amount_due_and_discount()
        amount = round(self.partner_amount_due - (self.amount + self.discount), 1)
        if (
            self.partner_amount_due <= 0.0 < self.discount
            and self.payment_type == "inbound"
        ):
            raise UserError(_("Discount can not be applied for advance payments.!!"))
        if (
            amount < 0.0 < self.partner_amount_due
            and self.payment_type == "inbound"
            and self.discount > 0.0
            and self.partner_amount_due > 0.0
        ):
            raise UserError(
                _(
                    "Payment amount and discount amount should "
                    "not be greater than the amount due.!!!"
                )
            )
        if (
            self.partner_amount_due > 0.0
            and self.discount > 0.0
            and amount != 0.0
            and self.payment_type == "inbound"
        ):
            raise UserError(
                _(
                    "You are not allowed to give a discount unless customer "
                    "is making full payment.!!!"
                )
            )
        if (
            self.partner_amount_due == 0.0
            and self.discount > 0.0
            and self.payment_type == "inbound"
        ):
            raise UserError(
                _("Partner Discount on receiving Advance payment is not allowed.!!!")
            )
        return res
