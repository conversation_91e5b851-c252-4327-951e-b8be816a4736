from odoo import fields, models


class ResPartner(models.Model):
    _inherit = 'res.partner'

    emergency_referral_configuration = fields.One2many('res.partner.referral.discount', 'partner_id',
                                                       string='Emergency Referral Configuration',
                                                       domain=[('panel', '=', 'emergency')])


class ResPartnerCorporatesDiscount(models.Model):
    _inherit = 'res.partner.referral.discount'

    panel = fields.Selection(selection_add=[('emergency', 'Emergency')], string='Panel')


class EmergencyAdmission(models.Model):
    _inherit = 'emergency.admission'

    referral_id = fields.Many2one('res.partner', string='Referred By', domain=[('is_referral', '=', True)])
    referral = fields.Char(string='Referral', related='referral_id.name', store=True)
    referral_code = fields.Char('Referral Code')

    def action_make_request(self):
        """Action Make Request"""
        res = super().action_make_request()
        domain = [('emergency_admission_id', '=', self.id)]
        panel = self.env['panel.wise.request'].search(domain)
        if panel:
            panel.referral_id = self.referral_id and self.referral_id.id or False
        return res

    def update_referral_details(self):
        """Update Referral Details"""
        action = self.env.ref('referral_inpatient.action_update_referral_form').read()[0]
        action['context'] = {'default_emergency_admission_id': self.id}
        return action

    def _create_move_lines_appointment_emergency_discount_line_ref(self, product_id, amount, account):
        """creating appointment invoice line without discount"""
        vals = {
            'product_id': product_id.id,
            'name': "Emergency Appointment Discount for " + self.name,
            'price_unit': -1 * round(amount, 2),
            'quantity': 1,
            'account_id': account and account.id or False,
        }
        return vals

    def create_invoice_line_vals_lines(self, product_id, amount):
        """Create Invoice Line Vals Discount"""
        res = super().create_invoice_line_vals_lines(product_id=product_id, amount=amount)
        referral = self.referral_id
        if not referral:
            referral = self.appointment_id.referral_id
        if referral and referral.emergency_referral_configuration and self.patient_id.patient_type != 'insurance':
            discount_account_id = self.env.company.emergency_discount_id
            discount_line = [item for item in res if item[2].get('account_id') == discount_account_id.id]
            discount_amount = amount / 100.0 * referral.emergency_referral_configuration[0].discount
            if discount_line and discount_amount > abs(discount_line[0][2].get('price_unit')):
                discount_line[0][2].update({
                    'price_unit': -1 * round(discount_amount, 2)
                })
            if not discount_line:
                discount_line = self._create_move_lines_appointment_emergency_discount_line_ref(
                    product_id=product_id,
                    amount=round(discount_amount, 2),
                    account=discount_account_id
                )
                res.append((0, 0, discount_line))
        return res


class UpdateReferralWiz(models.TransientModel):
    _inherit = 'update.referral.wizard'

    emergency_admission_id = fields.Many2one('emergency.admission')

    def update_referral_details(self):
        """Update Referral Details"""
        res = super().update_referral_details()
        if self.emergency_admission_id:
            self.emergency_admission_id.write({
                'referral_id': self.referral_id.id,
                'referral_code': self.referral_id.referral_code,
                'referral': self.referral_id.name,
            })
        return res


class NuroAppointment(models.Model):
    _inherit = 'nuro.appointment'

    def create_emergency_record(self):
        """Create Emergency Record"""
        res = super().create_emergency_record()
        if self.referral_id:
            self.emergency_id.write({
                'referral_id': self.referral_id.id,
                'referral_code': self.referral_id.referral_code,
            })
        return res


class NuroAppointmentCashPayment(models.TransientModel):
    _inherit = 'nuro.appointment.cash.payment'

    def get_referral_discount_details_lines(self, partner):
        res = super().get_referral_discount_details_lines(partner)
        if self.appointment_id.appointment_type == 'emergency':
            res = partner.emergency_referral_configuration
        return res


class NuroAppointmentCreditPayment(models.TransientModel):
    _inherit = 'nuro.appointment.credit.payment'

    def get_referral_discount_details_lines(self, partner):
        res = super().get_referral_discount_details_lines(partner)
        if self.appointment_id.appointment_type == 'emergency':
            res = partner.emergency_referral_configuration
        return res
