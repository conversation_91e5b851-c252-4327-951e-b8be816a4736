<odoo>
    <data>

        <record model="ir.ui.view" id="list_view_account_asset_asset_purchase_tree">
            <field name="name">account.asset.asset.purchase.tree</field>
            <field name="model">account.asset.asset</field>
            <field name="priority">22</field>
            <field name="arch" type="xml">
                <tree string="Assets" decoration-success="asset_status=='unallocated'"
                      decoration-danger="asset_status == 'allocated'"
                      decoration-warning="asset_status == 'repair'"
                      decoration-info="asset_status == 'transit'"
                      decoration-muted="asset_status == 'damaged'">
                    <field name="type" invisible="1"/>
                    <field name="asset_number" invisible="1"/>
                    <field name="serial_number" string="Tag Number"/>
                    <field name="model_name"/>
                    <field name="name"/>
                    <field name="category_id"/>
                    <field name="date" string="PO Date" optional="hide"/>
                    <field name="partner_id" string="Vendor" optional="hide"/>
                    <field name="value"/>
                    <field name="accumulated_depriciation_amount"/>
                    <field name="value_residual"/>
                    <field name="salvage_value" string="Salvage Value" optional="hide"/>
                    <field name="state"/>
                    <field name="asset_status"/>
                    <field name="show_full_dep" invisible="1"/>
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="view_account_asset_asset_form_new_form">
            <field name="name">account.asset.asset.form</field>
            <field name="model">account.asset.asset</field>
            <field name="priority">22</field>
            <field name="arch" type="xml">
                <form string="Asset">
                    <header>
                        <button name="request_asset_change" type="object" state="draft,open" string="Tag Change Request"
                                class="oe_highlight"/>
                        <button name="validate" states="draft,cancel" string="Confirm" type="object"
                                class="oe_highlight"/>
                        <button name="action_cancel" string="Cancel" type="object" class="oe_highlight"
                                attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <button type="object" name="compute_depreciation_board" string="Compute Depreciation"
                                states="draft"/>
                        <button name="%(asset_management_module.action_asset_disposal)d" states="open" string="Dispose Asset" type="action"
                                class="oe_highlight" groups="asset_management_module.group_wh_manager"/>
                        <button name="set_to_draft" string="Set to Draft" type="object"
                                attrs="{'invisible': ['|', ('entry_count', '!=', 0), ('state', '!=', 'open')]}"/>
                        <button name="%(account_asset.action_asset_modify)d" states="open" string="Modify Depreciation"
                                type="action"/>

                        <button name="%(asset_management_module.action_asset_sale)d" states="open"
                                string="Sale Asset" class="oe_highlight"
                                type="action"/>

                        <button name="%(asset_management_module.button_asset_request_action)d" type="action"
                                string="Make Request" state="running" context="{
                                'default_asset_location':initial_location,
                                'default_search_asset':asset_number,
                                'default_asset_category':category_id,
                                'default_asset_model':category_id,
                                'default_asset_id':id}" class="oe_highlight"
                                attrs="{'invisible':['|',('asset_status','!=', 'unallocated'),('state','in',('cancel','close'))]}"/>

                        <button name="%(asset_management_module.button_asset_return_action)d" type="action"
                                string="Return Asset"
                                state="running" context="{'default_employee':assigned_to,
                            'default_asset_id':id}" class="oe_highlight"
                                attrs="{'invisible':['|',('asset_status','!=', 'allocated'),('state','=','cancel')]}"/>
                        <field name="type" invisible="1"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,open"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" name="open_entries" type="object" icon="fa-pencil">
                                <field string="Items" name="entry_count" widget="statinfo"/>
                            </button>
                            <button class="oe_stat_button" name="open_booking_history" type="object" icon="fa-folder">
                                Booking History
                            </button>
                        </div>
                        <field name="image_medium" widget="image" class="oe_avatar"/>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name" placeholder="e.g. Laptop iBook"/>
                                <div class="oe_title">
                                    <field name="do_manage"/>
                                    <label for="do_manage"/>
                                </div>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="category_id" domain="[('type', '=', 'purchase')]"
                                       context="{'default_type': 'purchase'}"/>
                                <field name="asset_model" attrs="{'readonly':[('state','in',('open', 'close'))]}"/>
                                <field name="initial_location" attrs="{'readonly':[('state','in',('open', 'close'))]}"/>
                                <field name="code"/>
                                <field name="date" string="PO Date"/>
                                <field name="asset_number" readonly="1"/>
                                <field name="model_name" attrs="{'readonly':[('state','in',('open','close'))]}"/>
                                <field name="assigned_to"
                                       attrs="{'readonly':[('state','in',('open','close'))], 'invisible':[('do_manage','=', True)]}"/>
                                <field name="managed_by"
                                       attrs="{'readonly':[('state','in',('open','close'))], 'invisible':[('do_manage','=', False)]}"/>
                                <field name="location" readonly="0"/>
                                <field name="barcode" invisible="1"
                                       attrs="{'readonly':[('state','in',('open','close'))]}"/>
                                <field name="ser_no" attrs="{'readonly':[('state','in',('open','close'))]}"/>
                                <field name="start_warranty_date"
                                       attrs="{'readonly':[('state','in',('open','close'))]}"/>
                                <field name="sale_move_id" readonly="1"
                                       attrs="{'invisible': [('sale_move_id', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="currency_id" groups="base.group_multi_currency"/>
                                <field name="company_id" options="{'no_create': True}"
                                       groups="base.group_multi_company"/>
                                <field name="is_opening"/>
                                <field name="opening_date"
                                       attrs="{'required': [('is_opening', '=', True)], 'invisible': [('is_opening', '!=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="first_dep_entry_date"
                                       attrs="{'invisible': [('is_opening', '!=', True)], 'readonly': [('state', '!=', 'draft')]}"/>

                                <field name="opening_balance"
                                       attrs="{'invisible': [('is_opening', '!=', True)], 'readonly': [('state', '!=', 'draft')]}"/>

                                <field name="dep_value_opening" widget="monetary"
                                       options="{'currency_field': 'currency_id'}"
                                       attrs="{'invisible': [('is_opening', '!=', True)], 'readonly': [('state', '!=', 'draft')]}"/>

                                <field name="value" required="1" widget="monetary"
                                       options="{'currency_field': 'currency_id'}" string="Original Value"/>
                                <field name="salvage_value" widget="monetary" string="Salvage Value"
                                       options="{'currency_field': 'currency_id'}"
                                       attrs="{'invisible': [('type', '=', 'sale')]}"/>
                                <field name="value_residual" widget="monetary"
                                       options="{'currency_field': 'currency_id'}"/>
                                <field name="accumulated_depriciation_amount"/>
                                <field name="partner_id" string="Vendor"/>
                                <field name="invoice_id" string="Invoice"
                                       attrs="{'readonly': [('state', '!=', 'draft')]}"
                                       options="{'no_create': True}"/>
                                <!--                                <field name="serial_number" attrs="{'readonly':[('state','in',('open','close'))]}"/>-->
                                <field name="serial_number" attrs="{'readonly':[('state','!=','draft')]}"/>
                                <field name="date_received" attrs="{'readonly':[('state','in',('open','close'))]}"/>
                                <field name="asset_status"/>
                                <field name="end_warranty_date" attrs="{'readonly':[('state','=','close')]}"/>
                                <field name="account_move_id" readonly="1"
                                       attrs="{'invisible': [('account_move_id', '=', False)]}"
                                       string="Opening Account Entry"/>
                            </group>
                        </group>
                        <notebook colspan="4">
                            <page name="depreciation_board" string="Depreciation Board">
                                <field name="depreciation_line_ids" mode="tree"
                                       options="{'reload_whole_on_button': true}">
                                    <tree string="Depreciation Lines" decoration-info="(move_check == False)"
                                          create="false">
                                        <field name="depreciation_date"/>
                                        <field name="depreciated_value" readonly="1"/>
                                        <field name="amount" widget="monetary" string="Depreciation"/>
                                        <field name="remaining_value" readonly="1" widget="monetary" string="Residual"/>
                                        <field name="move_check" widget="deprec_lines_toggler"/>
                                        <field name="move_posted_check" invisible="1"/>
                                        <field name="parent_state" invisible="1"/>
                                        <button name="create_move" type="object" icon="fa-circle"/>
                                    </tree>
                                    <form string="Depreciation Lines">
                                        <group>
                                            <group>
                                                <field name="parent_state" invisible="1"/>
                                                <field name="name"/>
                                                <field name="sequence"/>
                                                <field name="move_id"/>
                                                <field name="move_check"/>
                                                <field name="parent_state" invisible="1"/>
                                            </group>
                                            <group>
                                                <field name="amount" widget="monetary"/>
                                                <field name="depreciation_date"/>
                                                <field name="depreciated_value"/>
                                                <field name="remaining_value"/>
                                            </group>
                                        </group>
                                    </form>
                                </field>
                            </page>
                            <page name="depreciation_info" string="Depreciation Information">
                                <group>
                                    <field name="method" widget="radio" attrs="{'invisible': [('type','=','sale')]}"/>
                                    <field name="method_progress_factor"
                                           attrs="{'invisible':[('method','=','linear')], 'required':[('method','=','degressive')]}"/>
                                    <field name="method_time" string="Time Method Based On" widget="radio"
                                           attrs="{'invisible': [('type','!=','purchase')]}"/>
                                    <field name="prorata" attrs="{'invisible': [('method_time','=','end')]}"/>
                                </group>
                                <group>
                                    <field name="method_number"
                                           attrs="{'invisible':[('method_time','=','end')], 'required':[('method_time','=','number')]}"/>
                                    <field name="method_period"/>
                                    <field name="method_end"
                                           attrs="{'required': [('method_time','=','end')], 'invisible':[('method_time','=','number')]}"/>
                                </group>
                            </page>
                            <page name="asset_history" string="Asset History">
                                <field name="asset_history_id" attrs="{'readonly':[('state','in',('open', 'close'))]}">
                                    <tree>
                                        <field name="name"/>
                                        <field name="location_from"/>
                                        <field name="location_to"/>
                                        <field name="asset_status"/>
                                        <field name="movement_date"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="new_view_account_asset_asset_kanban" model="ir.ui.view">
            <field name="name">account.asset.asset.kanban</field>
            <field name="model">account.asset.asset</field>
            <field name="priority">22</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="category_id"/>
                    <field name="date"/>
                    <field name="type" invisible="1"/>
                    <field name="state"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <div class="col-xs-6">
                                        <strong>
                                            <span>
                                                <t t-esc="record.name.value"/>
                                            </span>
                                        </strong>
                                    </div>
                                    <div class="col-xs-6 pull-right text-right">
                                        <strong>
                                            <t t-esc="record.date.value"/>
                                        </strong>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-6 text-muted">
                                        <span>
                                            <t t-esc="record.category_id.value"/>
                                        </span>
                                    </div>
                                    <div class="col-xs-6">
                                        <span class="pull-right text-right">
                                            <field name="state" widget="label_selection"
                                                   options="{'classes': {'draft': 'primary', 'open': 'success', 'close': 'default'}}"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>


        <record model="ir.ui.view" id="add_graph_view_asset">
            <field name="name">account.asset.graph</field>
            <field name="model">account.asset.asset</field>
            <field name="priority">22</field>
            <field name="arch" type="xml">
                <graph string="Assets Graph">
                    <field name="name" type="row"/>
                    <field name="value" type="measure"/>
                    <field name="date" type="measure"/>
                </graph>
            </field>
        </record>


        <record model="ir.ui.view" id="add_pivot_view_asset">
            <field name="name">account.asset.pivot</field>
            <field name="model">account.asset.asset</field>
            <field name="priority">22</field>
            <field name="arch" type="xml">
                <pivot string="Assets Pivot">
                    <field name="name" type="row"/>
                    <field name="value" type="measure"/>
                    <field name="date" type="measure"/>
                </pivot>
            </field>
        </record>


        <record model="ir.actions.act_window" id="create_asset_action">
            <field name="name">Create Asset</field>
            <field name="res_model">account.asset.asset</field>
            <!--            <field name="context" eval="{'group_by':'asset_status'}"/>-->
            <field name="context" eval="{'group_by':'state'}"/>
            <field name="view_mode">tree,form,graph,pivot</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('list_view_account_asset_asset_purchase_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_account_asset_asset_form_new_form')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('add_graph_view_asset')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('add_pivot_view_asset')})]"/>
        </record>

        <!--===================confirm Asset============-->
        <record id="view_asset_master_filter" model="ir.ui.view">
            <field name="name">account.asset.asset</field>
            <field name="model">account.asset.asset</field>
            <field name="priority">22</field>
            <field name="arch" type="xml">
                <search>
                    <field name="serial_number"/>
                    <field name="asset_number"/>
                    <field name="name"/>
                    <field name="state"/>
                    <field name="asset_status"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="state_filter" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Asset Status" name="asset_status" domain="[]"
                                context="{'group_by': 'asset_status'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record model="ir.actions.act_window" id="create_asset_action_confirm">
            <field name="name">Assets</field>
            <field name="res_model">account.asset.asset</field>
            <field name="search_view_id" ref="view_asset_master_filter"/>
            <field name="context">{'create': False}</field>
            <field name="domain">[('state', 'in', ['open'])]</field>
            <field name="view_mode">tree,kanban,form,graph,pivot</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('list_view_account_asset_asset_purchase_tree')}),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('new_view_account_asset_asset_kanban')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_account_asset_asset_form_new_form')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('add_graph_view_asset')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('add_pivot_view_asset')})]"/>
        </record>

        <menuitem name="Create Assets" id="create_asset_asset_menu" parent="IT_operation_menu_root"
                  action="create_asset_action" groups="asset_management_module.group_wh_manager" sequence="5"/>

        <menuitem name="Assets Master" id="confirm_asset_master_menu" parent="IT_asset_menu_root"
                  action="create_asset_action_confirm" groups="asset_management_module.group_wh_manager,asset_management_module.group_asset_master_user"
                  sequence="-1"/>

        <!--        =======================deprecation===========================================================-->

        <record model="ir.actions.act_window" id="action_depreciation_asset">
            <field name="name">Depreciation Assets</field>
            <field name="res_model">account.asset.asset</field>
            <field name="search_view_id" ref="view_asset_master_filter"/>
            <field name="context">{'create': False}</field>
            <field name="domain">[('show_full_dep', '=', True)]</field>
            <field name="view_mode">tree,kanban,form,graph,pivot</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('new_view_account_asset_asset_kanban')}),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('list_view_account_asset_asset_purchase_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_account_asset_asset_form_new_form')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('add_graph_view_asset')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('add_pivot_view_asset')})]"/>
        </record>

        <menuitem name="Fully Deprecated Assets" id="menu_deprecation_asset" parent="IT_asset_menu_root"
                  action="action_depreciation_asset" sequence="11"/>

    </data>
</odoo>