# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED

from odoo.exceptions import UserError

from odoo import api, fields, models, _


class MedicalTestCashPayment(models.TransientModel):
    
    _name = 'medical.test.cash.payment'
    _description = 'Medical Test Cash Payment'

    @api.model
    def default_get(self, vals):
        """
        Default Get for the Cash Payment Method for the medical Test
        :param vals:
        :return:
        """
        res = super(MedicalTestCashPayment, self).default_get(vals)
        medical_test_id = self.env['nuro.medical.test'].browse(self.env.context.get('active_id'))
        if medical_test_id:
            res.update({
                'medical_test_id': medical_test_id.id,
                'amount': medical_test_id.subtotal
            })
        return res

    medical_test_id = fields.Many2one('nuro.medical.test', string='Medical Test')
    payment_method_id = fields.Many2one('account.journal', string='Payment Method',
                                        domain=[('type', '=', 'cash'), ('cashier_journal', '=', True)],
                                        default=lambda self:
                                        self.env['account.journal'].search([
                                            ('type', '=', 'cash'),
                                            ('cashier_journal', '=', True)
                                        ], limit=1).id, required=True, readonly=True)
    date = fields.Datetime(string='Date', default=fields.Datetime.now, readonly=True)
    amount = fields.Float("Amount To Pay", readonly=True)
    total_amount = fields.Float('Amount', related='medical_test_id.subtotal', store=True)
    user_id = fields.Many2one('res.users', 'User', default=lambda self: self.env.user, store=True, readonly=True)
    discount_amount = fields.Float('Discount')

    @api.onchange('discount_amount')
    def onchange_discount_amount(self):
        """
        Onchange of discount on Blood Transfusion Cash wizard
        :return:
        """
        if 0.0 < self.discount_amount <= self.medical_test_id.subtotal:
            self.amount = self.medical_test_id.subtotal - self.discount_amount
        elif self.discount_amount < 0.0:
            self.discount_amount = False
            self.amount = self.medical_test_id.subtotal
            message = 'You can not Assign Discount in Negative!!!'
            return {'warning': {'title': 'Discount Amount Error', "message": message}}
        elif self.medical_test_id.subtotal <= self.discount_amount:
            self.discount_amount = False
            self.amount = self.medical_test_id.subtotal
            message = 'You can not Assign Discount More than or equal to Total Amount!!!'
            return {'warning': {'title': 'Discount Amount Error', "message": message}}
        else:
            self.amount = self.medical_test_id.subtotal

    def action_medical_test_cash_payment(self):
        """
        Method for the cash Payment Creation
        :return:
        """
        default_methods = self.env['hms.methods.library']
        current_user = self.env.user
        if default_methods.amount_check(amount=self.amount):
            raise UserError(_('No Amount for Cash Payment!'))
        if default_methods.state_check(state=self.medical_test_id.state, name='draft'):
            raise UserError(_('Payment already has been made!!'))
        if default_methods.account_check(account=current_user.account_id):
            raise UserError(_('Please define Account for Selected User!'))
        invoice_line = []
        medical = self.medical_test_id
        for lab in medical.lab_test_line:
            invoice_line.append((0,0, lab._create_move_lines_medical_test()))
        for imaging in medical.imaging_test_line:
            invoice_line.append((0, 0, imaging._create_move_lines_imaging()))
        for os in medical.other_service_line:
            invoice_line.append((0, 0, os._create_move_lines_os()))
        if self.discount_amount:
            invoice_line.append((0, 0, medical._create_move_lines_medical_test_discount(amount=self.discount_amount)))
        if invoice_line:
            inv_vals = self.medical_test_id._create_invoice_dict(
                partner_id=self.medical_test_id.patient_id.partner_id,
                invoice_line_ids=invoice_line,
                invoice_type='out_invoice'
            )
            ctx = {'active_model': self.medical_test_id._name, 'active_id': self.medical_test_id.id}
            inv_ids = self.env['account.move'].with_context(ctx).create(inv_vals)
            inv_ids.cash_payment = True
            inv_ids.action_post()
            inv_ids.line_ids.write({
                'panel': 'medical_checkup'
            })
            if self.amount > 0.0:
                payment_method_id = default_methods.get_payment_method(payment_method_id=self.payment_method_id)
                # payment_account = self.payment_method_id.default_credit_account_id.id
                # self.payment_method_id.sudo().write({'default_credit_account_id': self.env.user.account_id.id})
                payments = self.medical_test_id.create_payment(
                    partner_id=self.medical_test_id.patient_id.partner_id,
                    amount=self.amount,
                    journal_id=self.payment_method_id,
                    payment_method_id=payment_method_id,
                    payment_type='inbound',
                    invoice_ids=inv_ids
                )
                payments.cash_payment = True
                payments.post()
                # payment_line_receivable_id = payments.move_line_ids.filtered(
                #     lambda x: x.account_internal_type == 'receivable' and not x.reconciled)
                # invoice_line_receivable_id = inv_ids[0].line_ids.filtered(
                #     lambda x: x.account_internal_type == 'receivable' and not x.reconciled)
                # moves = payment_line_receivable_id + invoice_line_receivable_id
                # moves.reconcile()
                payments.move_line_ids.write({
                    'panel': 'medical_checkup'
                })
                # self.payment_method_id.sudo().write({'default_credit_account_id': payment_account})
                self.medical_test_id.write({
                    'payment_method': 'cash',
                    'state': 'send_to_test',
                    'discount_amount': self.discount_amount,
                    'paid_amount': self.amount
                })
                self.medical_test_id.process_medical_test()
        return self.medical_test_id.print_medical_test_thermal_receipt()
