<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="pharmacy_sale_order_list_view_returned" model="ir.ui.view">
            <field name="name">Pharmacy Sale</field>
            <field name="model">sale.order</field>
            <field name="priority">202</field>
            <field name="arch" type="xml">
                <tree string="Pharmacy Sale">
                    <field name="message_needaction" invisible="1"/>
                    <field name="name" string="Prescription #"/>
                    <field name="patient_id" string="Patient #"/>
                    <field name="partner_id" string="Patient Name"/>
                    <field name="return_date"/>
                    <field name="doctor_id" string="Doctor"/>
                    <field name="user_id"/>
                    <field name="payment_method"/>
                    <field name="amount_total" sum="Total Tax Included" widget="monetary" groups="base.group_system"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="state"/>
                    <field name="date_order" string="Date"/>
                </tree>
            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="pharmacy_sale_order_form_view_inherit" model="ir.ui.view">
            <field name="name">Pharmacy</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="nuro_pharmacy.pharmacy_sale_order_form_view"/>
            <field name="priority">200</field>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <field name="free_patient" invisible="1"/>
                    <button name="%(nuro_pharmacy_cashier.action_pharmacy_cash_payment)d" type="action"
                            class="oe_highlight" string="Cash" states="draft"
                            groups="nuro_pharmacy.group_pharmacy_user"/>
                    <button name="%(nuro_pharmacy_cashier.action_pharmacy_credit_wizard_appointment)d" type="action"
                            class="oe_highlight" string="Credit" states="draft"
                            groups="nuro_pharmacy.group_pharmacy_user"/>

                    <button name="%(nuro_pharmacy_cashier.action_so_free_patient_wizard_appointment)d"
                            type="action" string="Free Patient" class="oe_highlight"
                            attrs="{'invisible': ['|', ('free_patient', '=', False), ('state', 'not in', ('draft'))]}"
                            groups="nuro_pharmacy.group_pharmacy_manager_free"/>

                    <!--                    <button name="action_create_pharmacy_cancel" type="object" string="Cancel" states="sale"-->
                    <!--                            groups="nuro_pharmacy.group_pharmacy_product_change"/>-->
                    <field name="ref_so_id" invisible="1"/>
                    <button name="create_duplicate_sale_order" type="object" class="oe_highlight"
                            string="Change Product"
                            attrs="{'invisible': ['|', ('state', 'not in', ('sale', 'done')), ('ref_so_id', '!=', False)]}"
                            groups="nuro_pharmacy.group_pharmacy_product_change"/>
                </xpath>

                <xpath expr="//field[@name='warehouse_id']" position="after">
                    <field name="free_patient" attrs="{'readonly': [('state', '!=', 'draft')]}"
                           groups="nuro_pharmacy.group_pharmacy_manager_free"/>
                    <field name="refund_reason" attrs="{'invisible': [('refund_reason', '=', False)]}"/>
                    <field name="return_date" attrs="{'invisible': [('return_date', '=', False)]}"/>
                    <field name="ref_so_id" attrs="{'invisible': [('ref_so_id', '=', False)]}" readonly="1"
                           options="{'no_open': True, 'no_create': True}"/>
                </xpath>
            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="pharmacy_sale_order_form_return_view_inherit" model="ir.ui.view">
            <field name="name">Pharmacy</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="nuro_pharmacy.pharmacy_sale_order_return_form_view"/>
            <field name="priority">201</field>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="%(nuro_pharmacy_cashier.action_pharmacy_return_wizard)d" type="action"
                            class="oe_highlight" string="Return" states="sale,done"
                            groups="nuro_pharmacy.group_pharmacy_return_user"/>
                </xpath>
                <xpath expr="//field[@name='warehouse_id']" position="after">
                    <field name="refund_reason" attrs="{'invisible': [('refund_reason', '=', False)]}"/>
                    <field name="return_date" attrs="{'invisible': [('return_date', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <record id="stock_move_sale_conflict_view" model="ir.ui.view">
            <field name="name">Stock Move</field>
            <field name="model">stock.move</field>
            <field name="priority">209</field>
            <field name="arch" type="xml">
                <tree string="Pharmacy Conflict">
                    <field name="product_id"/>
                    <field name="order_id"/>
                    <field name="product_uom_qty"/>
                    <field name="state"/>
                    <field name="conflict_reason"/>
                </tree>
            </field>
        </record>

        <record id="action_picking_sale_conflict_tree_all_move" model="ir.actions.act_window">
            <field name="name">Transfers</field>
            <field name="res_model">stock.move</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree</field>
            <field name="domain">[
                ('picking_id.picking_type_id.code', '=', 'outgoing'),
                ('order_id', '!=', False),
                ('picking_id.state', 'not in', ('done', 'cancel'))
                ]
            </field>
            <field name="context">{
                'contact_display': 'partner_address',
                'default_company_id': allowed_company_ids[0],
                'create': False,
                'edit': False,
                }
            </field>
            <field name="search_view_id" ref="stock.view_move_search"/>
            <field name="view_id" ref="stock_move_sale_conflict_view"/>
        </record>

        <record id="nuro_pharmacy.action_pharmacy_sale_order_return_receive_action" model="ir.actions.act_window">
            <field name="name">Pharmacy Return Receive</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph,activity</field>
            <field name="search_view_id" ref="nuro_pharmacy.pharmacy_sale_inherit_search_view"/>
            <field name="domain">[('pharmacy_sale', '=', True), ('received_status', '=', 'create')]</field>
            <field name="context">{'default_pharmacy_sale': True, 'create': False}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('pharmacy_sale_order_list_view_returned')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_pharmacy.pharmacy_sale_order_return_receive_form_view')})]"/>
        </record>
    </data>
</odoo>