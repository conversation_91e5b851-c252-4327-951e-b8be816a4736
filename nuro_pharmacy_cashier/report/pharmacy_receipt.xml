<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <template id="pharmacy_receipt_thermal">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="web.internal_layout">
                        <div class="page">
                            <center>
                                <!-- START TABLE 1 : DATE - ORDER NO -->
                                <table style="width: 100%;">
                                    <tr>
                                        <td style="text-align: center;border-bottom:1px solid;">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <span t-field="o.date_order"/> &#160;
                                                <span t-field="o.name"/>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                                <!-- END TABLE 1 : DATE - ORDER NO -->
                                <h6 string="margin-top:-20px;">
                                    <p>
                                        <b>
                                            <span t-field="o.company_id" style="text-transform:uppercase;"/>
                                        </b>
                                        <br/>
                                        <span t-field="o.warehouse_id.phone"/>
                                        <br/>
                                        <span t-field="o.company_id.city"/>,
                                        <span t-field="o.company_id.state_id"/>,
                                        <span t-field="o.company_id.country_id"/>
                                        <br/>
                                        <span t-field="o.company_id.website"/>
                                        <br/>
                                        <span t-field="o.company_id.email"/>
                                    </p>
                                </h6>

                                <!-- START TABLE 2 : CUSTOMER DETAILS , USER DETAILS -->
                                <table style="width: 100%;">
                                    <tr>
                                        <td>
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <b>Name:</b>
                                                <span t-field="o.patient_id.name"/>
                                                <br/>
                                                <b>ID:</b>
                                                <span t-field="o.patient_id.identification_code"/>
                                                <br/>
                                                <b>Pharmacy#</b>
                                                <span t-field="o.name"/>
                                                <br/>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                                <!-- END TABLE 2 : CUSTOMER DETAILS , USER DETAILS -->
                                <br/>

                                <!-- START TABLE 3 : PRODUCTS DETAILS -->
                                <table style="width: 100%;">
                                    <tr>
                                        <td style="text-align:left;">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <strong>Product</strong>
                                            </p>
                                        </td>
                                        <td style="text-align:left;">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <strong>Qty</strong>
                                            </p>
                                        </td>
                                        <td style="text-align:left;">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <strong>Price</strong>
                                            </p>
                                        </td>
                                        <td style="text-align:right">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <strong>Total</strong>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr t-foreach="o.order_line" t-as="line">
                                        <td style="text-align:left;">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <span t-field="line.product_id.name"/>
                                            </p>
                                        </td>
                                        <td style="text-align:left;">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <span t-esc="round(line.product_uom_qty, 1)"/>
                                            </p>
                                        </td>
                                        <td style="text-align:left;">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <span t-esc="round(line.price_unit, 2)"/>
                                            </p>
                                        </td>
                                        <td style="text-align:right;">
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <span t-esc="round(line.price_total, 2)"
                                                      t-options='{"widget": "monetary",
                                                      "display_currency": res_company.currency_id}'/>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                                <!-- END TABLE 3 : PRODUCTS DETAILS -->
                                <!-- START TABLE 4 : AMOUNT DETAILS -->
                                <table style="width: 100%;">
                                    <tbody>
                                        <tr>
                                            <td style="width:45%">
                                                <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                                    Total:
                                                </p>
                                            </td>
                                            <td style="width:55%" class="text-right">
                                                <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                                    <span t-esc="sum([line.price_total
                                                    for line in o.order_line])"
                                                          t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width:45%">
                                                <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                                    Discount:
                                                </p>
                                            </td>
                                            <td style="width:55%" class="text-right">
                                                <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                                    <span t-esc="abs(sum([line.discount_amount
                                                    for line in o.order_line]))"
                                                          t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width:45%">
                                                <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                                    Payment:
                                                </p>
                                            </td>
                                            <td style="width:55%" class="text-right">
                                                <t t-if="o.payment_method == 'cash'">
                                                    <p style="font-size: medium;font-family: 'Monospace', Helvetica,
                                                    Arial, sans-serif;">
                                                        <span t-esc="o.paid_amount+o.credit_amount"
                                                              t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                                    </p>
                                                </t>
                                                <t t-if="o.payment_method == 'credit'">
                                                    <p style="font-size: medium;font-family: 'Monospace', Helvetica,
                                                    Arial, sans-serif;">
                                                        0.0
                                                    </p>
                                                </t>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width:45%">
                                                <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                                sans-serif;">
                                                    Balance:
                                                </p>
                                            </td>
                                            <td style="width:55%" class="text-right">
                                                <t t-if="o.payment_method == 'cash'">
                                                    <p style="font-size: medium;font-family: 'Monospace', Helvetica,
                                                    Arial, sans-serif;">
                                                        <span t-esc="abs(round(o.balance_amount, 2))"
                                                              t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                                    </p>
                                                </t>
                                                <t t-if="o.payment_method == 'credit'">
                                                    <p style="font-size: medium;font-family: 'Monospace', Helvetica,
                                                    Arial, sans-serif;">
                                                        <span t-field="o.credit_amount"
                                                              t-options="{'widget': 'monetary',
                                                          'display_currency': res_company.currency_id}"/>
                                                    </p>
                                                </t>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <!-- END TABLE 2 :  USER DETAILS -->
                                <br/>
                                <table style="width: 100%;">
                                    <tr>
                                        <td>
                                            <br/>
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <b>User:</b>
                                                <span t-field="o.requested_by_id.name"
                                                      style="text-transform:uppercase;"/>
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <br/>
                                            <p style="font-size: medium;font-family: 'Monospace', Helvetica, Arial,
                                            sans-serif;">
                                                <b>Payment Method:</b>
                                                <span t-field="o.payment_method"
                                                      style="text-transform:uppercase;"/>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </center>
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>