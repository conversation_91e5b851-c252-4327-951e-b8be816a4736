# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
"""
Module Docstring
"""
from datetime import datetime

from odoo.exceptions import UserError

from odoo import fields, models, _


# noinspection PyProtectedMember
class SaleOrder(models.Model):
    """
    Class Docstring
    """
    _inherit = 'sale.order'

    BILL_TO_TYPE = [
        ('patient', 'Patient'),
        ('employee', 'Employee'),
        ('company', 'Company'),
        ('regular', 'Regular'),
        ('hospital', 'Hospital'),
        ('hospital_employee', 'Hospital Employee Expense')
    ]

    free_patient = fields.Boolean('Free Patient', default=False)
    state = fields.Selection(selection_add=[('returned', 'Returned')])
    refund_reason = fields.Char('Refund Reason')
    converted = fields.Boolean('New', default=False)
    bill_to_type = fields.Selection(BILL_TO_TYPE, default='patient')
    ref_so_id = fields.Many2one('sale.order')
    return_date = fields.Date('Return Date', readonly=1, copy=False)
    expense_to_hospital = fields.Boolean('Expense To Hospital')
    description_hospital = fields.Char('Description')
    employee_id = fields.Many2one('hr.employee', string='Employee')
    payment_id = fields.Many2one('account.payment')
    approve_by = fields.Many2one('hr.employee', 'Approved By', domain=lambda self: [
        ('user_id', '!=', False),
        ('user_id.groups_id', 'in', self.env.ref('nuro_cashier_payment_setting.discount_request_approve').id)])

    def picking_returns(self):
        """
        Picking Returns
        :return:
        """
        for picking in self.picking_ids:
            if picking.state not in ('done', 'cancel'):
                picking.action_cancel()
            if picking.state == 'done':
                picking_dict = {
                    'picking_id': picking.id
                }
                context = {
                    "active_id": picking.id,
                }
                picking_return_wizard = self.env['stock.return.picking'].with_context(context).create(
                    picking_dict
                )
                picking_return_wizard._onchange_picking_id()
                new_picking = picking_return_wizard.create_returns()
                new_picking_id = self.env['stock.picking'].browse(new_picking['res_id'])
                for rec in picking.move_line_ids_without_package:
                    move = new_picking_id.move_ids_without_package.filtered(
                        lambda x: x.product_id.id == rec.product_id.id)
                    if move:
                        if rec.lot_id:
                            lot_id = rec.lot_id.id
                        else:
                            lot_id = False
                        self.env['stock.move.line'].create({
                            'move_id': move.id,
                            'picking_id': move.picking_id.id,
                            'product_id': move.product_id.id,
                            'date': move.date,
                            'lot_id': lot_id,
                            'qty_done': move.product_uom_qty,
                            'product_uom_id': rec.product_uom_id.id,
                            'location_id': move.location_id.id,
                            'location_dest_id': move.location_dest_id.id,
                        })
                new_picking_id.button_validate()

    def invoice_cancel(self):
        """
        Invoice
        :return:
        """
        for rec in self.invoice_ids:
            if rec.state == 'posted':
                rec.button_draft()
                rec.button_cancel()

    def action_create_pharmacy_cancel(self):
        """
        Pharmacy Cancel
        :return:
        """

        if self.ref_so_id:
            raise UserError(_('This order is created from sale order product change. You can not cancel it'))
        return_picking = self.picking_ids.filtered(lambda x: x.picking_type_id.code == 'incoming')
        if return_picking:
            raise UserError(_('Product Change is not possible as return of the product has been made already.!!'))
        else:
            self.picking_returns()
            self.invoice_cancel()
            self.state = 'cancel'

    def create_duplicate_sale_order(self):
        """
        Creating duplicate meeting
        :return:
        """
        ctx = self._context.copy()
        line = []
        for rec in self.order_line:
            line.append((0, 0, {
                'product_id': rec.product_id.id,
                'sale_line_id': rec.id,
            }))
        for record in self:
            ctx.update({
                'default_sale_id': record.id,
                'default_product_change_line': line
            })
        return {
            'name': 'Product Change Wizard',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'sale.product.change',
            'context': ctx,
            'view_id': self.env.ref('nuro_pharmacy_cashier.nuro_sale_product_change_form_view').id,
            'type': 'ir.actions.act_window',
            'target': 'new',
        }

    def perform_picking_operation(self):
        """
        Picking Operation
        :return:
        """
        self.action_confirm()
        if self.picking_ids:
            for picking in self.picking_ids:
                picking.sale_id = self.id
                picking.action_confirm()
                picking.action_assign()
                for ml in picking.move_line_ids_without_package:
                    ml.qty_done = ml.product_uom_qty
                try:
                    picking.action_done()
                except:
                    pass

    def print_receipt(self):
        """
        Pharmacy Receipt printing
        :return:
        """
        return self.env.ref('nuro_pharmacy_cashier.action_nuro_pharmacy_cash_thermal_receipt').report_action(self)

    def discount_amount_distribution(self, discount_amount):
        """
        Onchange of Discount Amount Value Applied on lines
        :return:
        """
        for rec in self.order_line:
            discount = rec.discount_amount = discount_amount / self.amount_untaxed * rec.price_total
            rec.discount_amount = round(discount, 3)

    def _create_move_lines_pharmacy_line_discount(self, discount_amount):
        """
        creating discount invoice line for Pharmacy
        :param discount_amount:
        :return:
        """
        account_id = self.env['account.account'].browse(
            int(self.env['ir.config_parameter'].sudo().get_param('pharmacy_discount_account_id')))
        if not account_id:
            raise UserError(_('Please configure the Discount account for Pharmacy!!!'))
        vals = {
            'name': 'Pharmacy Discount for Sale ' + self.name,
            'price_unit': -discount_amount,
            'quantity': 1,
            'account_id': account_id.id,
        }
        return vals

    def _create_payment_lines(self, amount, journal_id, payment_method_id, payment_type, partner_id):
        """
        Create Payment for the Pharmacy Sale Menu
        :param amount:
        :param journal_id:
        :param payment_method_id:
        :param payment_type:
        :return:
        """
        inv_ids = self.invoice_ids.filtered(lambda inv: inv.state not in ('cancel', 'draft'))
        payment_lines = {
            'payment_date': self.date_order,
            'amount': amount,
            'journal_id': journal_id.id,
            'payment_method_id': payment_method_id,
            'sale_id': self.id,
            'payment_type': payment_type,
            'partner_id': partner_id.id,
            'partner_type': 'customer',
            'communication': ' '.join([ref for ref in self.invoice_ids.mapped('ref') if ref]),
            'invoice_ids': [(6, 0, inv_ids.ids)],
        }
        return payment_lines

    def _create_debit_move_line_vals(self, partner_id, amount, account_id):
        """
        Debit Line Dictionary for the account move line
        :param partner_id:
        :param amount:
        :return:
        """
        debit_vals = {
            'name': self.name,
            'debit': abs(amount),
            'credit': 0.0,
            'partner_id': partner_id.id,
            'account_id': account_id.id,
            'date': datetime.now().date()
        }
        return debit_vals

    def _create_credit_move_line_vals(self, partner_id, amount, account_id):
        """
        Credit Line Dictionary for the account move line
        :param partner_id:
        :param amount:
        :return:
        """
        credit_vals = {
            'name': self.name,
            'debit': 0.0,
            'credit': amount,
            'partner_id': partner_id.id,
            'account_id': account_id.id,
            'date': datetime.now().date()
        }
        return credit_vals

    def _create_account_move_dict(self, journal_id, line_ids):
        """
        Creating Journal Entry for hospital Expense
        :param journal_id:
        :param line_ids:
        :return:
        """
        vals = {
            'type': 'entry',
            'journal_id': journal_id.id,
            'date': datetime.now().date(),
            'ref': "Sale # : " + self.name,
            'sale_id': self.id,
            'patient_id': self.patient_id.id,
            'state': 'draft',
            'line_ids': line_ids
        }
        return vals

    def create_account_move(self, journal_id, partner_id, amount, employee_id):
        """
        Creating account move entry
        :param employee_id:
        :param amount:
        :param partner_id:
        :param journal_id:
        :return:
        """
        account_move_obj = self.env['account.move']
        for rec in self:
            product_id = self.order_line[0].product_id
            credit_account_id = product_id.property_account_income_id or \
                                product_id.categ_id.property_account_income_categ_id
            if not employee_id:
                account_id = self.env['account.account'].browse(
                    int(self.env['ir.config_parameter'].sudo().get_param('hospital_expense_account_id')))
            else:
                account_id = self.env['account.account'].browse(
                    int(self.env['ir.config_parameter'].sudo().get_param('hospital_employee_expense_account_id')))
            if not product_id:
                raise UserError(_('There is not any Order Line!!!'))
            if not account_id:
                raise UserError(_('Please configure Hospital Expense Account and Employee Expense Account!!!'))
            # noinspection PyProtectedMember
            debit_line = rec._create_debit_move_line_vals(
                partner_id=partner_id, amount=amount, account_id=account_id)
            # noinspection PyProtectedMember
            credit_line = rec._create_credit_move_line_vals(
                partner_id=partner_id, amount=amount, account_id=credit_account_id)
            line_ids = [(0, 0, debit_line), (0, 0, credit_line)]
            # noinspection PyProtectedMember
            move = rec._create_account_move_dict(
                journal_id=journal_id, line_ids=line_ids)
            move_ids = account_move_obj.create(move)
            return move_ids


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    sale_id = fields.Many2one('sale.order', 'Sale #')
