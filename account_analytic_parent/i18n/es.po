# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_analytic_parent
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-02-21 03:54+0000\n"
"PO-Revision-Date: 2019-07-16 19:43+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (https://www.transifex.com/oca/teams/23907/es/)\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 3.7.1\n"

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "%(name)s - %(partner)s"
msgstr "%(name)s - %(partner)s"

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "%(parent)s / %(own)s"
msgstr "%(parent)s / %(own)s"

#. module: account_analytic_parent
#: model:ir.model,name:account_analytic_parent.model_account_analytic_account
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: account_analytic_parent
#: model:ir.model.fields,field_description:account_analytic_parent.field_account_analytic_account__child_ids
msgid "Child Accounts"
msgstr "Cuentas hijas"

#. module: account_analytic_parent
#: model:ir.model.fields,field_description:account_analytic_parent.field_account_analytic_account__complete_name
msgid "Complete Name"
msgstr "Nombre Completo"

#. module: account_analytic_parent
#: model_terms:ir.ui.view,arch_db:account_analytic_parent.view_account_analytic_account_list
msgid "Name"
msgstr "Nombre"

#. module: account_analytic_parent
#: model:ir.model.fields,field_description:account_analytic_parent.field_account_analytic_account__parent_id
#: model_terms:ir.ui.view,arch_db:account_analytic_parent.view_account_analytic_account_form
msgid "Parent Analytic Account"
msgstr "Cuenta analítica madre"

#. module: account_analytic_parent
#: model:ir.model.fields,field_description:account_analytic_parent.field_account_analytic_account__parent_path
msgid "Parent Path"
msgstr "Cuenta del Padre"

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "Please activate first parent account %s"
msgstr "Por favor primero activa la cuenta padre %s"

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "You can not create recursive analytic accounts."
msgstr "No puede crear cuentas analíticas recursivas."

#. module: account_analytic_parent
#: code:addons/account_analytic_parent/models/account_analytic_account.py:0
#, python-format
msgid "[%(code)s] %(name)s"
msgstr "[%(code)s] %(name)s"

#~ msgid "(Keep empty to open the current situation)"
#~ msgstr "(Dejar vacío para abrir la situación actual)"

#~ msgid "Account Analytic Chart"
#~ msgstr "Gráfico de cuenta analítica"

#~ msgid "Analytic Account Charts"
#~ msgstr "Gráficos de cuenta analítica"

#~ msgid "Analytic Accounts"
#~ msgstr "Cuentas analíticas"

#~ msgid "Cancel"
#~ msgstr "Cancelar"

#~ msgid "Chart of Analytic Accounts"
#~ msgstr "Gráfico de cuentas analíticas"

#~ msgid "Click to add an analytic account."
#~ msgstr "Pulse para añadir una cuenta analítica."

#~ msgid "Created by"
#~ msgstr "Creado por"

#~ msgid "Created on"
#~ msgstr "Creado el"

#~ msgid "Display Name"
#~ msgstr "Nombre mostrado"

#~ msgid "From"
#~ msgstr "De"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Última modificación el"

#~ msgid "Last Updated by"
#~ msgstr "Última actualización por"

#~ msgid "Last Updated on"
#~ msgstr "Última actualización el"

#~ msgid "Open Charts"
#~ msgstr "Abrir gráficos"

#~ msgid "Select the Period for Analysis"
#~ msgstr "Seleccione el periodo de análisis"

#~ msgid "To"
#~ msgstr "Para"
