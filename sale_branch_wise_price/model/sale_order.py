from odoo.exceptions import UserError

from odoo import api, fields, models, _


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    @api.onchange('product_id')
    def product_id_change(self):
        """Onchange Product Change"""
        res = super().product_id_change()
        if not self.product_id:
            return
        order = self.order_id
        if not order.branch_id:
            raise UserError(_('Please Add Branch before Updating the Line.!!!'))
        else:
            product_id = self.product_id
            charge = product_id.list_price
            if product_id.pr_branch_rate_line:
                price_line = product_id.pr_branch_rate_line.filtered(
                    lambda line: line.branch_id.id == order.branch_id.id)
                if price_line:
                    charge = price_line[0].charge if len(price_line) > 1 else price_line.charge
                else:
                    charge = product_id.test_charge
            self.update({
                'price_unit': charge
            })
        return res

    @api.onchange('product_uom', 'product_uom_qty')
    def product_uom_change(self):
        res = super().product_uom_change()
        if not self.product_uom or not self.product_id:
            self.price_unit = 0.0
            return
        order = self.order_id
        if not order.branch_id:
            raise UserError(_('Please Add Branch before Updating the Line.!!!'))
        else:
            product_id = self.product_id
            charge = product_id.list_price
            if product_id.pr_branch_rate_line:
                price_line = product_id.pr_branch_rate_line.filtered(
                    lambda line: line.branch_id.id == order.branch_id.id)
                if price_line:
                    charge = price_line[0].charge if len(price_line) > 1 else price_line.charge
                else:
                    charge = product_id.test_charge
            self.update({
                'price_unit': charge
            })
        return res
