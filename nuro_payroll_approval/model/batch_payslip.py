# -- coding: utf-8 --
# Part of Nurosolution Pvt Ltd.
import calendar
from datetime import datetime, date
from dateutil import relativedelta
from odoo import api, fields, models
import time
from datetime import datetime


class BatchPayroll(models.Model):
    _inherit = 'hr.payslip.run'

    payroll_calculation_id = fields.Many2one('payroll.calculation')
    date_start = fields.Date(string='Date From', required=True, readonly=False,
                             default=time.strftime('%Y-%m-01'))
    date_end = fields.Date(string='Date To', required=True, readonly=False,
                           default=str(datetime.now() + relativedelta.relativedelta(months=+1, day=1, days=-1))[:10])

    @api.model
    def default_get(self, fields):
        """
        Default get to update the from and to date in the payslip
        :param fields:
        :return:
        """
        res = super(BatchPayroll, self).default_get(fields)
        payroll_conf_id = self.env['payroll.conf'].search([], limit=1)
        if payroll_conf_id:
            res['date_start'] = datetime.now().date().replace(
                day=payroll_conf_id.from_date) - relativedelta.relativedelta(
                months=payroll_conf_id.month_diff)
            res['date_end'] = datetime.now().date().replace(day=payroll_conf_id.to_date)
            res['name'] = 'Batch Payslip from ' + str(
                datetime.now().date().replace(day=payroll_conf_id.from_date) - relativedelta.relativedelta(
                    months=payroll_conf_id.month_diff)) + ' To ' + str(
                datetime.now().date().replace(day=payroll_conf_id.to_date))
        else:
            current_date = datetime.now().date()
            _, num_days = calendar.monthrange(current_date.year, current_date.month)
            first_day = date(current_date.year, current_date.month, 1)
            last_day = date(current_date.year, current_date.month, num_days)
            res['date_start'] = first_day
            res['date_end'] = last_day
            res['name'] = 'Batch Payslip Sheet from ' + str(first_day) + ' To ' + str(last_day)
        return res
