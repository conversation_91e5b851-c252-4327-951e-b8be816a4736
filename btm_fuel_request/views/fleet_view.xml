<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="inherit_fleet_for_driver" model="ir.ui.view">
            <field name="name">fleet.vehicle.form</field>
            <field name="model">fleet.vehicle</field>
            <field name="inherit_id" ref="fleet.fleet_vehicle_view_form"/>
            <field name="arch" type="xml">
                <field name="driver_id" position="attributes">
                    <attribute name="invisible">1</attribute>
                </field>
                <field name="driver_id" position="after">
                    <field name="company_id" invisible="1"/>
                    <field name="driver_name_id"
                           domain="['|', ('company_id', '=', company_id),('company_id', '=', False)]"
                           options="{'no_create': True}"/>
                </field>
            </field>
        </record>
        <record id="inherit_fleet_vehicle_odometer_tree_view" model="ir.ui.view">
            <field name="name">fleet.vehicle.odometer.tree.inherit</field>
            <field name="model">fleet.vehicle.odometer</field>
            <field name="inherit_id" ref="fleet.fleet_vehicle_odometer_view_tree"/>
            <field name="arch" type="xml">
                <tree position="attributes">
                    <attribute name="editable">top</attribute>
                </tree>
                <field name="vehicle_id" position="after">
                    <field name="fuel_request_id" string="Fuel Reference" width="120px"/>
                    <field name="name" width="100" invisible="1"/>

                </field>
            </field>
        </record>

    </data>
</odoo>