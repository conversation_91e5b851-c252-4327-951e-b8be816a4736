<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="nuro_surgery_master_form_view" model="ir.ui.view">
            <field name="name">Surgery Master</field>
            <field name="model">nuro.surgery.master</field>
            <field name="arch" type="xml">
                <form string="Surgery Master">
                    <sheet>
                        <field name="active" invisible="1"/>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger"
                                attrs="{'invisible': [('active', '=', True)]}"/>
                        <group>
                            <group>
                                <field name="name" required="1"/>
                                <field name="c_section" groups="base.group_no_one"/>
                                <field name="estimated_completion_time" widget="float_time"/>
                                <field name="surgery_type" required="1"/>
                            </group>
                            <group>
                                <field name="speciality_ids" required="1" widget="many2many_tags"
                                       options="{'no_open': True, 'no_create': True}"/>
                                <field name="charges" required="1" groups="nuro_cashier_closing.group_cashier_user"/>
                                <field name="user_id" force_save="1" invisible="1"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="surgery_master_list_view" model="ir.ui.view">
            <field name="name">Surgery Master</field>
            <field name="model">nuro.surgery.master</field>
            <field name="arch" type="xml">
                <tree string="Surgery Master">
                    <field name="name"/>
                    <field name="surgery_type" readonly="1"/>
                    <field name="speciality_ids" widget="many2many_tags"/>
                    <field name="charges" groups="nuro_hms_groups.group_service_charge_user"/>
                    <field name="user_id" force_save="1" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="action_surgery_master" model="ir.actions.act_window">
            <field name="name">Surgery Master</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.surgery.master</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'delete': False}</field>
        </record>
    </data>
</odoo>