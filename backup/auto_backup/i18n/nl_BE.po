# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auto_backup
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 8.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-03-26 14:17+0000\n"
"PO-Revision-Date: 2015-03-26 14:17+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "/home/<USER>/.ssh/id_rsa"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__folder
msgid "Absolute path for storing the backups"
msgstr "Absoluut pad om backups te bewaren"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_needaction
msgid "Action Needed"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: auto_backup
#: model:ir.actions.act_window,name:auto_backup.action_backup_conf_form
#: model:ir.ui.menu,name:auto_backup.backup_conf_menu
#, fuzzy
msgid "Automated Backups"
msgstr "Auto. backups verwijderen"

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "Automatic backups of the database can be scheduled as follows:"
msgstr ""
"Automatische backups van de database kunnen als volgend gepland worden:"

#. module: auto_backup
#: model:mail.message.subtype,name:auto_backup.mail_message_subtype_failure
#, fuzzy
msgid "Backup Failed"
msgstr "Backup folder"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__backup_format
#, fuzzy
msgid "Backup Format"
msgstr "Backup folder"

#. module: auto_backup
#: model:ir.actions.server,name:auto_backup.ir_cron_backup_scheduler_0_ir_actions_server
#: model:ir.cron,cron_name:auto_backup.ir_cron_backup_scheduler_0
#: model:ir.cron,name:auto_backup.ir_cron_backup_scheduler_0
msgid "Backup Scheduler"
msgstr ""

#. module: auto_backup
#: model:mail.message.subtype,name:auto_backup.mail_message_subtype_success
msgid "Backup Successful"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__days_to_keep
msgid ""
"Backups older than this will be deleted automatically. Set 0 to disable "
"autodeletion."
msgstr ""

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
#, fuzzy
msgid "Basic backup configuration"
msgstr "Lokale backup configuratie"

#. module: auto_backup
#: model:ir.model.constraint,message:auto_backup.constraint_db_backup_name_unique
#, fuzzy
msgid "Cannot duplicate a configuration."
msgstr "Lokale backup configuratie"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__changeset_change_ids
msgid "Changeset Changes"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__changeset_ids
msgid "Changesets"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__backup_format
#, fuzzy
msgid "Choose the format for this backup."
msgstr "Absoluut pad om backups te bewaren"

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__method
msgid "Choose the storage method for this backup."
msgstr ""

#. module: auto_backup
#: code:addons/auto_backup/models/db_backup.py:0
#, python-format
msgid "Cleanup of old database backups failed."
msgstr ""

#. module: auto_backup
#: code:addons/auto_backup/models/db_backup.py:0
#, python-format
msgid "Connection Test Failed!"
msgstr ""

#. module: auto_backup
#: code:addons/auto_backup/models/db_backup.py:0
#, python-format
msgid "Connection Test Succeeded!"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__count_pending_changeset_changes
msgid "Count Pending Changeset Changes"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__count_pending_changesets
msgid "Count Pending Changesets"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__create_uid
msgid "Created by"
msgstr "Gemaakt door"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__create_date
msgid "Created on"
msgstr "Gemaakt op"

#. module: auto_backup
#: model:ir.model,name:auto_backup.model_db_backup
#, fuzzy
msgid "Database Backup"
msgstr "Database"

#. module: auto_backup
#: code:addons/auto_backup/models/db_backup.py:0
#: model:mail.message.subtype,description:auto_backup.mail_message_subtype_failure
#, python-format
msgid "Database backup failed."
msgstr ""

#. module: auto_backup
#: code:addons/auto_backup/models/db_backup.py:0
#: model:mail.message.subtype,description:auto_backup.mail_message_subtype_success
#, python-format
msgid "Database backup succeeded."
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__days_to_keep
msgid "Days To Keep"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__display_name
msgid "Display Name"
msgstr ""

#. module: auto_backup
#: code:addons/auto_backup/models/db_backup.py:0
#, python-format
msgid ""
"Do not save backups on your filestore, or you will backup your backups too!"
msgstr ""

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "Execute backup"
msgstr ""

#. module: auto_backup
#: model:ir.actions.server,name:auto_backup.action_server_backup
msgid "Execute backup(s)"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__folder
msgid "Folder"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_follower_ids
msgid "Followers"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "Go to Settings / Technical / Automation / Scheduled Actions."
msgstr "Ga naar Instellingen / Technsich / Automatisering / Geplande acties"

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "Help"
msgstr "Help"

#. module: auto_backup
#: model:ir.model.constraint,message:auto_backup.constraint_db_backup_days_to_keep_positive
msgid "I cannot remove backups from the future. Ask Doc for that."
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__id
msgid "ID"
msgstr "ID"

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__message_needaction
#: model:ir.model.fields,help:auto_backup.field_db_backup__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__message_has_error
#: model:ir.model.fields,help:auto_backup.field_db_backup__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup____last_update
#, fuzzy
msgid "Last Modified on"
msgstr "Laatst bijgewerkt op"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: auto_backup
#: model:ir.model.fields.selection,name:auto_backup.selection__db_backup__method__local
msgid "Local disk"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_ids
msgid "Messages"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__method
msgid "Method"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__name
msgid "Name"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__sftp_private_key
msgid ""
"Path to the private key file. Only the Odoo user should have read "
"permissions for that file."
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__sftp_private_key
msgid "Private key location"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields.selection,name:auto_backup.selection__db_backup__method__sftp
#, fuzzy
msgid "Remote SFTP server"
msgstr "Gebruikersnaam SFTP Server"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__sftp_password
#, fuzzy
msgid "SFTP Password"
msgstr "SFTP poort"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__sftp_port
msgid "SFTP Port"
msgstr "SFTP poort"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__sftp_host
#, fuzzy
msgid "SFTP Server"
msgstr "Gebruikersnaam SFTP Server"

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "SFTP Settings"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "Search the action named 'Backup scheduler'."
msgstr "Zoek de actie genaamd 'Backup scheduler'."

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid ""
"Set the scheduler to active and fill in how often you want backups generated."
msgstr ""
"Zet de planner op actief en vul in hoe vaak de backup moet gemaakt worden."

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__smart_search
msgid "Smart Search"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__name
msgid "Summary of this backup process"
msgstr ""

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "Test SFTP Connection"
msgstr "Test SFTP Connectie"

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__sftp_host
#, fuzzy
msgid ""
"The host name or IP address from your remote server. For example ***********"
msgstr "Het IP adres van uw externe server. Bijvoorbeeld: ***********"

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__sftp_password
#, fuzzy
msgid ""
"The password for the SFTP connection. If you specify a private key file, "
"then this is the password to decrypt it."
msgstr ""
"Het wachtwoord van de gebruiker waar de SFTP connectie mee moet gemaakt "
"worden. Dit is het wachtwoord van de gebruiker op de externe server."

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__sftp_port
msgid "The port on the FTP server that accepts SSH/SFTP calls."
msgstr "De poort op de FTP server die SSH/SFTP accepteert."

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__sftp_user
msgid ""
"The username where the SFTP connection should be made with. This is the user "
"on the external server."
msgstr ""
"De gebruikersnaam waar de SFTP connectie mee gemaakt moet worden. Dit is de "
"gebruiker op de externe server."

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_unread
msgid "Unread Messages"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid ""
"Use SFTP with caution! This writes files to external servers under the path "
"you specify."
msgstr ""
"Gebruik SFTP voorzichtig! Dit schrijft bestanden naar externe servers onder "
"het pad dat u opgeeft."

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__user_can_see_changeset
msgid "User Can See Changeset"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__sftp_user
#, fuzzy
msgid "Username in the SFTP Server"
msgstr "Gebruikersnaam SFTP Server"

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "Warning:"
msgstr "Waarschuwing:"

#. module: auto_backup
#: model:ir.model.fields,field_description:auto_backup.field_db_backup__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields,help:auto_backup.field_db_backup__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "john"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields.selection,name:auto_backup.selection__db_backup__backup_format__dump
msgid "pg_dump custom format (without filestore)"
msgstr ""

#. module: auto_backup
#: model_terms:ir.ui.view,arch_db:auto_backup.view_backup_conf_form
msgid "sftp.example.com"
msgstr ""

#. module: auto_backup
#: model:ir.model.fields.selection,name:auto_backup.selection__db_backup__backup_format__zip
msgid "zip (includes filestore)"
msgstr ""

#~ msgid "%s"
#~ msgstr "%s"

#~ msgid "Auto. E-mail on backup fail"
#~ msgstr "Auto. e-mailen wanneer backup mislukt"

#~ msgid "Backups"
#~ msgstr "Backups"

#~ msgid ""
#~ "Choose after how many days the backup should be deleted from the FTP "
#~ "server. For example:\n"
#~ "If you fill in 5 the backups will be removed after 5 days from the FTP "
#~ "server."
#~ msgstr ""
#~ "Kies na hoeveel dagen de backups verwijderd moeten worden van de FTP "
#~ "server. Bijvoorbeeld:\n"
#~ "Als u 5 invult zal de backup na 5 dagen verwijderd worden van de FTP "
#~ "server."

#~ msgid ""
#~ "Choose after how many days the backup should be deleted. For example:\n"
#~ "If you fill in 5 the backups will be removed after 5 days."
#~ msgstr ""
#~ "Kies na hoeveel dagen de backup verwijderd moet worden. Bijvoorbeeld:\n"
#~ "Als u 5 invult zal de backup verwijderd worden na 5 dagen."

#~ msgid "Configure Backup"
#~ msgstr "Configureer backup"

#~ msgid "Contact us!"
#~ msgstr "Contacteer ons!"

#~ msgid "Database you want to schedule backups for"
#~ msgstr "Database waar u backups voor wilt plannen"

#~ msgid "E-mail to notify"
#~ msgstr "E-mail om te verwittigen"

#~ msgid "Error ! No such database exists!"
#~ msgstr "Error! Deze database bestaat niet!"

#~ msgid ""
#~ "Fill in the e-mail where you want to be notified that the backup failed "
#~ "on the FTP."
#~ msgstr ""
#~ "Vul de e-mail in waarop u wilt verwittigd worden als de backup mislukt op "
#~ "de FTP."

#~ msgid "For example: /odoo/backups/"
#~ msgstr "Bijvoorbeeld: /odoo/backups/"

#~ msgid "Host"
#~ msgstr "Host"

#~ msgid "IP Address SFTP Server"
#~ msgstr "IP adres SFTP server"

#~ msgid ""
#~ "If you check this option you can choose to automaticly get e-mailed when "
#~ "the backup to the external server failed."
#~ msgstr ""
#~ "Als u deze optie aanvinkt kan u kiezen om automatisch een e-mail aan te "
#~ "krijgen als de backup naar de externe server mislukt."

#~ msgid ""
#~ "If you check this option you can choose to automaticly remove the backup "
#~ "after xx days"
#~ msgstr ""
#~ "Als u deze optie aanvinkt kan u kiezen om automatisch backups te "
#~ "verwijderen na xx dagen"

#~ msgid ""
#~ "If you check this option you can specify the details needed to write to a "
#~ "remote server with SFTP."
#~ msgstr ""
#~ "Als u deze optie aanvinkt kan u de details invullen die nodig zijn om te "
#~ "connecteren met de externe SFTP server."

#~ msgid "Need more help?"
#~ msgstr "Meer hulp nodig?"

#~ msgid "Password User SFTP Server"
#~ msgstr "Wachtwoord gebruiker SFTP server"

#~ msgid "Path external server"
#~ msgstr "Pad externe server"

#~ msgid "Port"
#~ msgstr "Poort"

#~ msgid "Remove SFTP after x days"
#~ msgstr "SFTP verwijderen na x dagen"

#~ msgid "Remove after x days"
#~ msgstr "Verwijderen na x dagen"

#~ msgid "SFTP"
#~ msgstr "SFTP"

#~ msgid "Search options"
#~ msgstr "Zoekopties"

#~ msgid "Test"
#~ msgstr "Test"

#~ msgid ""
#~ "The location to the folder where the dumps should be written to. For "
#~ "example /odoo/backups/.\n"
#~ "Files will then be written to /odoo/backups/ on your remote server."
#~ msgstr ""
#~ "De locatie naar de folder waar de backup naar toe moet geschreven worden. "
#~ "Bijvoorbeeld odoo/backups/\n"
#~ "Bestanden worden dan naar /odoo/backups/ geschreven op de externe server"

#~ msgid ""
#~ "This configures the scheduler for automatic backup of the given database "
#~ "running on given host at given port on regular intervals."
#~ msgstr ""
#~ "Dit configureert de planner voor automatische backups op de ingegeven "
#~ "database waar de host, poort en database op zijn ingegeven voor reguliere "
#~ "intervallen."

#~ msgid "Write to external server with sftp"
#~ msgstr "Schrijf naar externe server met SFTP"
