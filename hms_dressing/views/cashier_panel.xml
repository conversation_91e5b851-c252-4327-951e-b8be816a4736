<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="cashier_panel_dressing_line_request" model="ir.ui.view">
            <field name="name">Cashier</field>
            <field name="model">cashier.panel</field>
            <field name="inherit_id" ref="static_panel_request.cashier_panel_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='doctor_id']" position="attributes">
                    <attribute name="required">1</attribute>
                </xpath>


                <xpath expr="//page[@name='panel_request_lines']" position="inside">
                    <separator string="Dressing"/>
                    <field name="dressing_hms_ids" widget="many2many_tags" invisible="1"/>
                    <field name="dressing_cashier_line" context="{
                        'default_panel': 'dressing',
                        'default_create_from_object_name': 'cashier.panel',
                        'default_create_from_object_id': id,
                        'default_object_name': 'cashier.panel',
                        'default_object_id': id,
                        'default_patient_id': patient_id,
                        'default_doctor_id': doctor_id}" attrs="{'readonly': [('state', '!=', 'draft')]}">
                        <tree editable="bottom">
                            <field name="panel" readonly="1" force_save="1" invisible="1"/>
                            <field name="create_from_object_name" readonly="1" force_save="1" invisible="1"/>
                            <field name="create_from_object_id" readonly="1" force_save="1" invisible="1"/>
                            <field name="object_name" readonly="1" force_save="1" invisible="1"/>
                            <field name="object_id" readonly="1" force_save="1" invisible="1"/>
                            <field name="user_id" readonly="1" force_save="1" invisible="1"/>
                            <field name="panel" readonly="1" force_save="1" invisible="1"/>
                            <field name="date" readonly="1" force_save="1" invisible="1"/>
                            <field name="patient_id" readonly="1" force_save="1" invisible="1"/>
                            <field name="doctor_id" readonly="1" force_save="1" invisible="1"/>
                            <field name="master_id" required="1" attrs="{'readonly': [('parent.state', '!=', 'draft')]}"
                                   options="{'no_open': True, 'no_create': True}"
                                   domain="[('panel', '=', 'dressing'), ('id', 'not in', parent.dressing_hms_ids)]"/>
                            <field name="name" required="1" force_save="1" invisible="1"/>
                            <field name="unit_price" readonly="1" force_save="1" sum="Total"
                                   groups="nuro_cashier_closing.group_cashier_user"/>
                            <field name="discount_amount" readonly="1" force_save="1" sum="Total"
                                   groups="nuro_cashier_closing.group_cashier_user"/>
                            <field name="state" readonly="1"
                                   attrs="{'column_invisible': [('parent.state', '=', 'draft')]}"/>
                            <button class="fa fa-trash" type="object" name="delete_button"
                                    attrs="{'invisible': [('parent.state', '!=', 'draft')]}"/>
                        </tree>
                    </field>
                </xpath>

            </field>
        </record>

    </data>
</odoo>