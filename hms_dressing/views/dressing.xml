<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!--    tree view of the hms_dressing.dressing model-->
        <record id="view_dressing_tree" model="ir.ui.view">
            <field name="name">dressing.tree</field>
            <field name="model">dressing</field>
            <field name="arch" type="xml">
                <tree string="HMS Dressing Dressing Tree">
                    <field name="date"/>
                    <field name="followup_dressing" optional="hide"/>
                    <field name="next_dressing_date" optional="hide"/>
                    <field name="name"/>
                    <field name="patient_id"/>
                    <field name="queue"/>
                    <field name="age"/>
                    <field name="gender"/>
                    <field name="mobile"/>
                    <field name="severity_id"/>
                    <field name="wound_id"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <!--    form view of the hms_dressing.dressing model-->
        <record id="view_dressing_form" model="ir.ui.view">
            <field name="name">dressing.form</field>
            <field name="model">dressing</field>
            <field name="priority">3</field>
            <field name="arch" type="xml">
                <form string="HMS Dressing Dressing Form">
                    <header>
                        <field name="followup_dressing" invisible="1"/>
                        <field name="next_dressing_date" invisible="1"/>
                        <button name="complete_dressing" string="Complete Dressing" type="object" class="oe_highlight"
                                groups="hms_dressing.group_dressing_user"
                                attrs="{'invisible': [('state', '!=', 'in_progress')]}"/>
                        <button name="action_send_to_cashier" string="Send To Cashier" type="object"
                                class="oe_highlight" groups="hms_dressing.group_dressing_user" states="draft"/>
                        <button name="action_start_dressing" string="Start Dressing" type="object" class="oe_highlight"
                                groups="hms_dressing.group_dressing_user" states="payment"/>

                        <button name="update_followup_dressing_date" string="Update Schedule Date" type="object"
                                class="oe_highlight" groups="hms_dressing.group_dressing_user"
                                attrs="{'invisible':[('followup_dressing', '=', False)]}"/>
                        <field name="state" widget="statusbar"
                               statusbar_visible="draft,cashier,payment,in_progress,completed,cancelled"/>

                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_labtest_entry_create" type="object" string="Labtest"
                                    icon="fa-list" attrs="{'invisible': [('state', '=', 'payment')]}">
                                <field name="lab_count" readonly="1"/>
                            </button>
                            <button name="action_labtest_result_create" type="object" string="Lab Result"
                                    icon="fa-list">
                                <field name="lab_result_count" readonly="1"/>
                            </button>
                        </div>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <div class="oe_right" style="width: 300px;">
                            <div class="oe_title" style="width: 390px;">
                                <label for="queue" string="QUEUE #"/>
                                <h1>
                                    <field name="queue" class="oe_inline" style="color:brown;"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group>
                                <field name="patient_id" options="{'no_open': True, 'no_create': True}"
                                       attrs="{'readonly': [('state', 'not in', ('draft', 'cashier'))]}"/>
                                <field name="identification_code"/>
                                <field name="age"/>
                                <field name="dressing_domain_product_ids" widget="many2many_tags" invisible="1"/>
                                <field name="gender"/>
                                <field name="mobile"/>
                                <field name="patient_information"
                                       attrs="{'readonly': [('state', '!=', 'draft')], 'required': [('patient_id', '=', False)]}"/>
                                <field name="invoice_ids" widget="many2many_tags" invisible="1"/>
                            </group>
                            <group>
                                <field name="date" readonly="1"/>
                                <field name="followup_dressing"
                                       attrs="{'invisible':[('followup_dressing', '=', False)]}"/>
                                <field name="next_dressing_date"
                                       attrs="{'invisible':[('next_dressing_date', '=', False)]}"/>
                                <field name="doctor_id" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="wound_id" options="{'no_create': True,'no_open': True}" required="1"
                                       attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="wound_pressure_ulcer" invisible="1"/>
                                <field name="severity_id" options="{'no_create': True,'no_open': True}" required="1"
                                       domain="[('wound_id', '=', wound_id)]"
                                       attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="amount" invisible="1"/>
                                <field name="labtest_ids" widget="many2many_tags" invisible="1"/>
                                <field name="discount" invisible="1"/>
                                <field name="total_amount" invisible="1"/>
                                <field name="cash_paid" invisible="1"/>
                                <field name="payment_method" invisible="1"/>
                                <field name="edit_hide_css" invisible="1"/>
                                <field name="consumption_created" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Consumption" name="consumption"
                                  attrs="{'invisible': [('state', 'not in', ['in_progress', 'completed'])]}">

                                <group>
                                    <group>
                                        <button name="create_consumption_from_dressing"
                                                class="oe_highlight"
                                                type="object"
                                                string="Consume"
                                                attrs="{
                                                    'readonly': [('state', '=', 'completed')],
                                                    'invisible': [('state', '!=', 'in_progress')]
                                                }"/>
                                    </group>

                                </group>
                                <group>
                                    <group>
                                        <field name="dressing_source_location_id" options="{'no_create': True}"
                                               attrs="{'readonly': [('consumption_created', '=', True)]}" force_save="1"
                                               required="0"/>
                                    </group>
                                    <group>
                                        <field name="dressing_consume_location_id" options="{'no_create': True}"
                                               attrs="{'readonly': [('consumption_created', '=', True)]}" force_save="1"
                                               required="0"/>
                                    </group>
                                </group>
                                <field name="dressing_consumption_line_ids" nolabel="1">
                                    <tree editable="bottom">
                                        <field name="product_id" required="1"
                                               options="{'no_create': True, 'no_open': True}"
                                               domain="[('id', 'not in', parent.dressing_consumption_request_ids),
                                                ('type', '=', 'product'),
                                                ('qty_available', '&gt;', 0.0),('id', 'not in', parent.dressing_domain_product_ids)]"
                                               context="{'location': parent.dressing_source_location_id}"/>

                                        <field name="available_quantity" readonly="1" force_save="1"/>
                                        <field name="quantity" required="1"/>

                                    </tree>
                                </field>
                                <field name="dressing_consumption_request_ids" readonly="1" force_save="1">
                                    <tree editable="bottom">
                                        <field name="product_id" required="1"
                                               context="{'location': parent.dressing_source_location_id}"
                                               domain="[('id', 'not in', parent.dressing_consumption_request_ids), ('type', '=', 'product'), ('qty_available', '&gt;', 0.0)]"/>
                                        <field name="available_quantity" readonly="1" force_save="1"/>
                                        <field name="quantity" required="1"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Wound Tracking Form" name="wound_tracking"
                                  attrs="{'invisible': [('state', 'not in', ['in_progress','completed','cancelled'])], 'readonly': [('state', '!=', 'in_progress')]}">
                                <style>
                                    .oe_title h1 {
                                    font-size: 24px;
                                    margin-bottom: 10px;
                                    }
                                </style>
                                <div class="oe_title text-center w-100 mb-4">
                                    <h1 class="text-center">🩹 Wound Tracking Form</h1>
                                </div>

                                <div class="container-fluid px-0">
                                    <!-- Wound Information -->
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="o_horizontal_separator mb-3">Wound Information</div>
                                            <div class="d-flex flex-wrap align-items-center gap-4 mb-3">
                                                <div class="d-flex align-items-center" style="min-width: 150px;">
                                                    <field name="wound_pressure_ulcer" nolabel="1" class="me-1"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                    <span>Pressure Ulcer</span>
                                                </div>
                                                <div class="d-flex align-items-center" style="min-width: 150px;">
                                                    <field name="wound_surgical" nolabel="1" class="me-1"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                    <span>Surgical</span>
                                                </div>
                                                <div class="d-flex align-items-center" style="min-width: 150px;">
                                                    <field name="wound_diabetic" nolabel="1" class="me-1"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                    <span>Diabetic</span>
                                                </div>
                                                <div class="d-flex align-items-center" style="min-width: 150px;">
                                                    <field name="wound_traumatic" nolabel="1" class="me-1"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                    <span>Traumatic</span>
                                                </div>
                                                <div class="d-flex align-items-center" style="min-width: 150px;">
                                                    <field name="wound_venous_ulcer" nolabel="1" class="me-1"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                    <span>Venous Ulcer</span>
                                                </div>
                                                <div class="d-flex align-items-center" style="min-width: 150px;">
                                                    <field name="wound_other" nolabel="1" class="me-1"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                    <span>Other</span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <field name="wound_location" placeholder="Wound Location"
                                                           class="w-100"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                </div>
                                                <div class="col-12">
                                                    <field name="wound_onset_date" placeholder="dd/mm/yyyy"
                                                           class="w-100"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Wound Measurements -->
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="o_horizontal_separator mb-3">Wound Measurements</div>
                                            <div class="row">
                                                <div class="col-md-4 mb-3">
                                                    <field name="wound_length" placeholder="Length (cm)" class="w-100"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <field name="wound_width" placeholder="Width (cm)" class="w-100"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <field name="wound_depth" placeholder="Depth (cm)" class="w-100"
                                                           attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Wound Appearance -->
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="o_horizontal_separator mb-3">Wound Appearance</div>

                                            <!-- Tissue Type -->
                                            <div class="mb-4">
                                                <div class="mb-2 fw-bold">Tissue Type:</div>
                                                <div class="d-flex flex-wrap align-items-center gap-4">
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="tissue_necrotic" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Necrotic (black)</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="tissue_slough" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Slough (yellow)</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="tissue_granulation" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Granulation (red)</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="tissue_epithelializing" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Epithelializing</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="tissue_closed" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Closed</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Exudate Amount -->
                                            <div class="mb-4">
                                                <div class="mb-2 fw-bold">Exudate Amount:</div>
                                                <div class="d-flex flex-wrap align-items-center gap-4">
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="exudate_none" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>None</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="exudate_light" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Light</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="exudate_moderate" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Moderate</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="exudate_heavy" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Heavy</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Exudate Type -->
                                            <div class="mb-4">
                                                <div class="mb-2 fw-bold">Exudate Type:</div>
                                                <div class="d-flex flex-wrap align-items-center gap-4">
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="exudate_serous" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Serous</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="exudate_sanguineous" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Sanguineous</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="exudate_serosanguineous" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Serosanguineous</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="exudate_purulent" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Purulent</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Odor -->
                                            <div class="mb-4">
                                                <div class="mb-2 fw-bold">Odor:</div>
                                                <div class="d-flex flex-wrap align-items-center gap-4">
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="odor_none" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>None</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="odor_faint" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Faint</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="odor_moderate" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Moderate</span>
                                                    </div>
                                                    <div class="d-flex align-items-center" style="min-width: 150px;">
                                                        <field name="odor_strong" nolabel="1" class="me-1"
                                                               attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        <span>Strong</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Surrounding Skin Condition -->
                                            <div class="row mb-4">
                                                <div class="col-12">
                                                    <div class="o_horizontal_separator mb-3">Surrounding Skin
                                                        Condition
                                                    </div>
                                                    <div class="d-flex flex-wrap align-items-center gap-4 mb-3">
                                                        <div class="d-flex align-items-center"
                                                             style="min-width: 150px;">
                                                            <field name="skin_intact" nolabel="1" class="me-1"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                            <span>Intact</span>
                                                        </div>
                                                        <div class="d-flex align-items-center"
                                                             style="min-width: 150px;">
                                                            <field name="skin_macerated" nolabel="1" class="me-1"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                            <span>Macerated</span>
                                                        </div>
                                                        <div class="d-flex align-items-center"
                                                             style="min-width: 150px;">
                                                            <field name="skin_erythematous" nolabel="1" class="me-1"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                            <span>Erythematous</span>
                                                        </div>
                                                        <div class="d-flex align-items-center"
                                                             style="min-width: 150px;">
                                                            <field name="skin_indurated" nolabel="1" class="me-1"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                            <span>Indurated</span>
                                                        </div>
                                                        <div class="d-flex align-items-center"
                                                             style="min-width: 150px;">
                                                            <field name="skin_warm" nolabel="1" class="me-1"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                            <span>Warm</span>
                                                        </div>
                                                        <div class="d-flex align-items-center"
                                                             style="min-width: 150px;">
                                                            <field name="skin_cool" nolabel="1" class="me-1"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                            <span>Cool</span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <field name="pain_level" placeholder="Pain Level (0-10)"
                                                                   class="w-100"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Interventions -->
                                            <div class="row mb-4">
                                                <div class="col-12">
                                                    <div class="o_horizontal_separator mb-3">Interventions / Actions
                                                        Taken
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-12 mb-3">
                                                            <field name="cleaning_method" placeholder="Cleaned with..."
                                                                   class="w-100"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        </div>
                                                        <div class="col-12 mb-3">
                                                            <field name="dressing_applied"
                                                                   placeholder="Dressing Applied" class="w-100"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        </div>
                                                        <div class="col-12 mb-3">
                                                            <field name="medication_applied"
                                                                   placeholder="Medication Applied" class="w-100"
                                                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!--    action of the Menu Dressing model-->
        <record id="dressing_action" model="ir.actions.act_window">
            <field name="name">Dressing</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">dressing</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', 'in', ('draft', 'cashier')), ('followup_dressing', '=', False)]</field>
            <field name="context">{'create': True, 'delete': False, 'edit': True}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                    (0, 0, {'view_mode': 'tree', 'view_id': ref('view_dressing_tree')}),
                    (0, 0, {'view_mode': 'form', 'view_id': ref('view_dressing_form')})]"/>
        </record>


        <record id="pending_dressing_action" model="ir.actions.act_window">
            <field name="name">Pending Dressing</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">dressing</field>
            <field name="domain">[('state', 'in', ('payment', 'in_progress'))]</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'delete': False, 'create': False, 'edit': True}</field>
            <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_dressing_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_dressing_form')})]"/>
        </record>

        <record id="completed_dressing_action" model="ir.actions.act_window">
            <field name="name">Completed Dressing</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">dressing</field>
            <field name="domain">[('state', '=', 'completed')]</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'delete': False, 'create': False}</field>
            <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_dressing_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_dressing_form')})]"/>
        </record>

        <record id="all_dressing_action" model="ir.actions.act_window">
            <field name="name">All Dressing</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">dressing</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'delete': False, 'create': False, 'edit': True}</field>
            <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_dressing_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_dressing_form')})]"/>
        </record>

        <record id="follow_up_dressing_action" model="ir.actions.act_window">
            <field name="name">Followup Dressing</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">dressing</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('followup_dressing', '=', 'yes')]</field>
            <field name="context">{'delete': False, 'create': False, 'edit': False}</field>
            <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_dressing_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_dressing_form')})]"/>
        </record>


    </data>
</odoo>