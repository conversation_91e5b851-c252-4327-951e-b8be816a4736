from odoo import models, fields,api


class HmsMaster(models.Model):
    _inherit = 'hms.master'

    panel = fields.Selection(selection_add=[('dressing', 'Dressing')])


class ConfigureSeverity(models.Model):
    _name = "configure.severity"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Configure Severity"

    master_id = fields.Many2one('hms.master', string="HMS Master", copy=False)
    name = fields.Char(string="Name", tracking=True)
    wound_id = fields.Many2one('configure.wound', string='Wound', tracking=True)
    cost = fields.Float(string="Cost", tracking=True)

    _sql_constraints = [('name_uniq', 'unique(name, wound_id)', 'Severity name & Wound name must be unique')]

    def create_master_vals(self):
        """Create Vals"""
        vals = {
            'name': self.name,
            'object_id': self.id,
            'object_name': self._name,
            'unit_price': self.cost,
            # 'active': self.active,
            'panel': 'dressing'
        }
        return vals

    def add_record_hms_master(self):
        """
        HMS Master ID
        """
        master = self.env['hms.master']
        master_dict = self.create_master_vals()
        if not self.master_id:
            master_id = master.create(master_dict)
            self.master_id = master_id.id
        else:
            self.master_id.write(master_dict)


    @api.model
    def create(self, vals):
        res = super().create(vals)
        res.add_record_hms_master()
        return res

    def write(self, vals):
        """
        Write super call
        """
        res = super(ConfigureSeverity, self).write(vals)
        self.add_record_hms_master()
        return res

    def unlink(self):
        """
        Unlink Other Service
        """
        self.master_id.unlink()
        return super(ConfigureSeverity, self).unlink()