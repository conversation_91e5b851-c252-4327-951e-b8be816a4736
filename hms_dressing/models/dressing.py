from datetime import datetime

import pytz
from odoo.exceptions import UserError
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT as DATETIME_FORMAT

from odoo import api, fields, models, _


class Dressing(models.Model):
    _name = "dressing"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "Dressing"
    _order = 'id desc'

    GENDER = [
        ('male', 'Male'),
        ('female', 'Female'),
    ]

    PAYMENT_METHOD = [
        ('cash', 'Cash'),
        ('credit', 'Credit')
    ]

    request_id = fields.Many2one('hms.request', 'Request')
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company, tracking=True)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user, tracking=True)
    date = fields.Date('Date', default=fields.Date.context_today, tracking=True)
    queue = fields.Integer(string="queue", required=False, copy=False, readonly=True, tracking=True)
    payment_method = fields.Selection(PAYMENT_METHOD, copy=False, tracking=True)
    patient_information = fields.Text('Remark', tracking=True)
    patient_id = fields.Many2one('nuro.patient', string='Patient', tracking=True)
    identification_code = fields.Char(string='ID#', related='patient_id.identification_code', store=True, tracking=True)
    age = fields.Char('Patient Age', related='patient_id.age', store=True, tracking=True)
    gender = fields.Selection(GENDER, string='Gender', related='patient_id.gender', store=True, tracking=True)
    mobile = fields.Char('Mobile', related='patient_id.mobile', store=True, tracking=True)
    name = fields.Char(string='HMS Dressing', required=True, copy=False, readonly=True,
                       default=lambda self: _('/'))
    doctor_id = fields.Many2one('nuro.doctor', string='Doctor', tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('cashier', 'Send To Cashier'),
        ('payment', 'Payment Done'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], required=True, default='draft', tracking=True,)
    wound_id = fields.Many2one('configure.wound', string="Wound Type", store=True, tracking=True)
    dressing_req_id = fields.Many2one('dressing', string="Dressing Type", tracking=True)
    severity_id = fields.Many2one('configure.severity', string="Severity Type",tracking=True)
    amount = fields.Float(string="Amount", related='severity_id.cost', store=True, tracking=True)
    discount = fields.Float(string="Discount", readonly=True, tracking=True)
    total_amount = fields.Float(string="Total Amount", compute='get_total_amount', readonly=True, tracking=True)
    cash_paid = fields.Float(string="Cash Paid", readonly=True, tracking=True)
    invoice_ids = fields.Many2many('account.move', 'account_move_dressing_rel', 'dressing_id', 'move_id', 'Invoice ID',
                                   readonly=True)
    lab_count = fields.Integer(compute='get_lab_count')
    labtest_ids = fields.Many2many('nuro.lab.entry', 'dressing_lab_entry_rel', 'dressing_id', 'lab_entry_id',
                                   string='Labtest IDS')
    inpatient_id = fields.Many2one('nuro.inpatient', string='Inpatient')
    edit_hide_css = fields.Html(string='Edit Hide CSS', sanitize=False, compute='_compute_edit_hide_css')
    consumption = fields.Integer(compute='get_consumption_count')
    description_hospital = fields.Char('Description')
    expense_to_hospital = fields.Boolean('Expense To Hospital',tracking=True)
    employee_id = fields.Many2one('hr.employee', string='Employee', tracking=True)
    lab_result_count = fields.Integer(compute='get_lab_result_value',tracking=True)
    followup_dressing = fields.Selection([('yes', 'Yes'), ('no', 'No')], string='Follow-up Dressing', tracking=True)
    next_dressing_date = fields.Date("Next Dressing Date")
    dressing_source_location_id = fields.Many2one('stock.location', string='Source Location', store=True,
                                                  domain=lambda self: [
                                                      ('id', 'in', self.env.user.consume_location_ids.ids)],
                                                  )
    consumption_created = fields.Boolean(string='Consumption Created')

    dressing_consume_location_id = fields.Many2one('stock.location', string='Consume Location', store=True,
                                                   domain=lambda self: [
                                                       ('id', 'in', self.env.user.view_consume_loc_ids.ids)])
    dressing_consumption_line_ids = fields.One2many(
        comodel_name='dressing.consumption.line',
        inverse_name='dressing_request_line_id',
        string='Consumption IDS'
    )

    dressing_consumption_request_ids = fields.One2many(
        comodel_name='dressing.request.consumption.line',
        inverse_name='dressing_process_line_id',
        string='Consumption Lines'
    )

    dressing_domain_product_ids = fields.Many2many('product.product', 'dressing_product_rel', 'dressing_id',
                                                   'product_id',
                                                   compute='_get_domain_product_ids')

    # Wound Information
    wound_pressure_ulcer = fields.Boolean('Pressure Ulcer')
    wound_surgical = fields.Boolean('Surgical')
    wound_diabetic = fields.Boolean('Diabetic')
    wound_traumatic = fields.Boolean('Traumatic')
    wound_venous_ulcer = fields.Boolean('Venous Ulcer')
    wound_other = fields.Boolean('Other')
    wound_location = fields.Char('Wound Location', placeholder="Wound Location")
    wound_onset_date = fields.Date('Wound Onset Date')

    # Wound Measurements
    wound_length = fields.Float('Length (cm)', placeholder="Length (cm)")
    wound_width = fields.Float('Width (cm)', placeholder="Width (cm)")
    wound_depth = fields.Float('Depth (cm)', placeholder="Depth (cm)")

    # Wound Appearance
    # Tissue Type
    tissue_necrotic = fields.Boolean('Necrotic (black)')
    tissue_slough = fields.Boolean('Slough (yellow)')
    tissue_granulation = fields.Boolean('Granulation (red)')
    tissue_epithelializing = fields.Boolean('Epithelializing')
    tissue_closed = fields.Boolean('Closed')

    # Exudate Amount
    exudate_none = fields.Boolean('None')
    exudate_light = fields.Boolean('Light')
    exudate_moderate = fields.Boolean('Moderate')
    exudate_heavy = fields.Boolean('Heavy')

    # Exudate Type
    exudate_serous = fields.Boolean('Serous')
    exudate_sanguineous = fields.Boolean('Sanguineous')
    exudate_serosanguineous = fields.Boolean('Serosanguineous')
    exudate_purulent = fields.Boolean('Purulent')

    # Odor
    odor_none = fields.Boolean('None')
    odor_faint = fields.Boolean('Faint')
    odor_moderate = fields.Boolean('Moderate')
    odor_strong = fields.Boolean('Strong')

    # Surrounding Skin Condition
    skin_intact = fields.Boolean('Intact')
    skin_macerated = fields.Boolean('Macerated')
    skin_erythematous = fields.Boolean('Erythematous')
    skin_indurated = fields.Boolean('Indurated')
    skin_warm = fields.Boolean('Warm')
    skin_cool = fields.Boolean('Cool')
    pain_level = fields.Integer('Pain Level (0-10)', placeholder="Pain Level (0-10)")

    # Interventions
    cleaning_method = fields.Char('Cleaned with', placeholder="Cleaned with...")
    dressing_applied = fields.Char('Dressing Applied', placeholder="Dressing Applied")
    medication_applied = fields.Char('Medication Applied', placeholder="Medication Applied")

    @api.onchange('product_id')
    def _onchange_processs_line_location(self):
        if self.dressing_consumption_line_ids:
            for line in self.dressing_consumption_line_ids:
                if not line.location_id:
                    line.location_id = self.dressing_source_location_id

    def generate_queue_number(self):
        current_date = fields.Date.context_today(self)
        for rec in self:
            existing_record = self.env['dressing'].search([
                ('doctor_id', '=', rec.doctor_id.id),
                ('state', 'in', ['payment', 'in_progress', 'completed']),
                ('date', '=', current_date),
                ('id', '!=', rec.id),
            ], order='queue DESC', limit=1)
            if existing_record:
                rec.queue = existing_record.queue + 1
            else:
                rec.queue = 1

    @api.depends('dressing_consumption_line_ids')
    def _get_domain_product_ids(self):
        for rec in self:
            product_ids = rec.dressing_consumption_line_ids.mapped('product_id').ids
            rec.dressing_domain_product_ids = [(6, 0, product_ids)]

    def create_consumption_from_dressing(self):
        """Create supply consumption from dressing"""
        for dressing in self:
            if not dressing.dressing_consumption_line_ids and not dressing.dressing_consumption_request_ids:
                raise UserError(_('Please record dressing consumption before completing this dressing request.'))
            if not dressing.dressing_source_location_id or not dressing.dressing_consume_location_id:
                raise UserError(_('Please configure both source and consume locations for consumption.'))
            for line in dressing.dressing_consumption_line_ids:
                if line.quantity <= 0:
                    raise UserError(
                        _("You cannot select zero or negative quantity for product '%s'.") % line.product_id.display_name)
                if line.quantity > line.available_quantity:
                    raise UserError(
                        _("You cannot select more quantity than available quantity for product '%s'.") % line.product_id.display_name)
            line_list = dressing._prepare_consumption_lines()
            dressing._create_consumption_request_lines()
            vals = {
                'source_location_id': dressing.dressing_source_location_id.id,
                'consume_location_id': dressing.dressing_consume_location_id.id,
                'supply_consume_line': line_list,
                'dressing_id': dressing.id,
            }
            consumption_id = self.env['supply.consumption'].sudo().create(vals)
            consumption_id.create_supply_material_consume()
            dressing.dressing_consumption_line_ids.write({
                'dressing_request_line_id': False,
            })

    def _prepare_consumption_lines(self):
        """Prepare the consumption lines for supply.consumption"""
        line_list = []
        for rec in self.dressing_consumption_line_ids:
            line_list.append((0, 0, {
                'product_id': rec.product_id.id,
                'qty_requested': rec.quantity,
                'location_id': self.dressing_source_location_id.id,
                'uom_id': rec.product_id.uom_id.id,
            }))
        return line_list

    def _create_consumption_request_lines(self):
        """Create the intermediate request lines from dressing consumption lines"""
        for rec in self.dressing_consumption_line_ids:
            self.dressing_consumption_request_ids.create({
                'product_id': rec.product_id.id,
                'quantity': rec.quantity,
                'dressing_process_line_id': self.id,
                'available_quantity': rec.available_quantity,
            })


    @api.depends('state')
    def _compute_edit_hide_css(self):
        for record in self:
            if record.state not in ['draft', 'in_progress']:
                record.edit_hide_css = '<style>.o_form_button_edit {display: none !important;}</style>'
            else:
                record.edit_hide_css = False

    def print_receipt(self):
        """
        Print Covid Report
        :return:
        """
        return self.env.ref('hms_dressing.action_dressing_thermal_receipt').report_action(self)

    @api.depends('amount', 'discount')
    def get_total_amount(self):
        """
        Dressing Total Amount
        :return:
        """
        for rec in self:
            rec.total_amount = rec.amount - rec.discount

    def get_lab_count(self):
        """
        Dressing lab Count
        :return:
        """
        for rec in self:
            query = """
                    select count(*) from nuro_lab_entry where dressing_id is not null and dressing_id = %s
                    """ % rec.id
            self.env.cr.execute(query)
            result = self.env.cr.fetchone()
            rec.lab_count = result and result[0] or False

    def action_send_to_cashier(self):
        """
        Dressing send_to_cashier function
        """
        request_id = self.request_id
        if not request_id:
            request_vals = self.prepare_hms_request_dict()
            request_id = self.env['hms.request'].create(request_vals)
            self.request_id = request_id.id
        if request_id:
            cashier_panel = self.env["cashier.panel"]
            cashier_panel.create_request_record(patient_id=self.patient_id, doctor_id=self.doctor_id,
                                                record_id=self)
            self.state = 'cashier'

    def action_start_dressing(self):
        """
        Dressing start_dressing function
        """
        self.state = 'in_progress'

    @api.model
    def create(self, vals):
        """
        override create function to create sequence number
        """
        if vals.get('name', _('/')) == _('/'):
            vals['name'] = self.env['ir.sequence'].next_by_code('dressing') or _('/')
        res = super(Dressing, self).create(vals)
        if res.inpatient_id and 'surgery_id' in res.inpatient_id.read()[0]:
            if res.inpatient_id.surgery_id:
                get_earlier_dressing = self.search([('id', '!=', res.id), ('inpatient_id', '=', res.inpatient_id.id)])
                if not get_earlier_dressing:
                    res.state = 'payment'
        return res

    def get_lab_result_value(self):
        """
        Inpatient lab Count
        :return:
        """
        for rec in self:
            if rec.patient_id:
                query = """
                        select count(*) from nuro_medical_labtest_result 
                        where patient_id is not null and parent_id is null and patient_id = %s 
                        and state in ('logged_in', 'in_progress', 'other_hospital', 'completed')
                        """ % rec.patient_id.id
                self.env.cr.execute(query)
                result = self.env.cr.fetchone()
                rec.lab_result_count = result and result[0] or False
            else:
                rec.lab_result_count = 0.0

    def action_labtest_result_create(self):
        """
        Method to Create Labtest Entry
        :return:
        """
        domain = [('patient_id', '=', self.patient_id.id), ('parent_id', '=', False)]
        return {
            'name': _('Labtest'),
            'domain': domain,
            'res_model': 'nuro.medical.labtest.result',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'view_type': 'form',
            'context': {'create': False, 'edit': False}
        }

    def complete_dressing(self):
        """
        Complete Dressing
        """
        for rec in self:
            if rec.company_id.is_consumption_allow:
                if not self.dressing_consumption_request_ids and not self.dressing_consumption_line_ids:
                    raise UserError(_('Please record dressing consumption before completing this dressing request.'))
            if rec.company_id.is_consumption_allow:
                rec.create_consumption_from_dressing()
            rec.date_of_completion = fields.Date.today()
            if not rec.company_id.is_dressing_allow:
                rec.state = 'completed'
            else:
                action = self.env.ref('hms_dressing.action_follow_up_dressing_wizard').read()[0]
                action['context'] = {'default_dressing_id': rec.id}
                return action
        return False

    def update_followup_dressing_date(self):
        """
        Update Followup Dressing Date
        """
        action = self.env.ref('hms_dressing.action_schedule_dressing_date').read()[0]
        action['context'] = {'default_dressing_id': self.id}
        return action

    def prepare_hms_request_dict(self):
        """
        Prepare HMS Dictionary
        """
        vals = {
            'panel': 'dressing',
            'patient_id': self.patient_id.id,
            'doctor_id': self.doctor_id.id if self.doctor_id else False,
            'date': self.date,
            'create_from_object_name': self._name,
            'create_from_object_id': self.id,
            'dest_object_name': self._name,
            'dest_object_id': self.id,
            'master_id': self.severity_id.master_id.id,
            'state': 'send',
            'name': self.severity_id.master_id.name,
        }
        return vals

    def action_labtest_entry_create(self):
        """
        Method to Create Labtest Entry
        :return:
        """
        ctx = self._context.copy()
        for record in self:
            ctx.update({
                'default_dressing_id': record.id,
                'default_patient_id': record.patient_id.id,
                'default_patient_parent_id': record.patient_id.mother_id.id,
                'create': True,
                'edit': True,
            })
            if record.state != 'payment':
                ctx.update({
                    'create': False,
                    'edit': False,
                })

        domain = [('dressing_id', '=', self.id)]
        return {
            'name': _('Labtest Request'),
            'domain': domain,
            'res_model': 'nuro.lab.entry',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'view_type': 'form',
            'context': ctx,
        }

    def create_consumption_dressing(self):
        """
        Create Consumption Dressing
        """
        action = self.env.ref('nuro_consume_transfer.supply_consume_form_action').read()[0]
        action['domain'] = [('dressing_id', '=', self.id)]
        action['context'] = {'default_dressing_id': self.id}
        if self.state != 'payment':
            action['context'].update({
                'create': False,
                'edit': False,
            })
        return action

    def get_consumption_count(self):
        """
        Get Consumption Count
        """
        for rec in self:
            count = self.env['supply.consumption'].search_count([('dressing_id', '=', self.id)])
            rec.consumption = count and count or 0

    @api.onchange('dressing_consumption_line_ids')
    def onchange_dressing_consumption_line_get_qty(self):
        """
        Get the available on hand quantity of the product
        """
        for rec in self.dressing_consumption_line_ids:
            rec.available_quantity = 0.0
            quant = self.env["stock.quant"]
            if rec.product_id:
                location_id = rec.dressing_request_line_id.dressing_source_location_id
                available_qty = quant.sudo()._get_available_quantity(
                    product_id=rec.product_id,
                    location_id=self.dressing_source_location_id,
                    strict=True,
                )
                rec.available_quantity = available_qty


class NuroLabEntry(models.Model):
    _inherit = 'nuro.lab.entry'

    dressing_id = fields.Many2one('dressing', string='Dressing')

    @api.model
    def create(self, vals):
        """
        Override Create Method for Labtest Entry
        :return:
        """
        res = super(NuroLabEntry, self).create(vals)
        if res.dressing_id:
            res.dressing_id.labtest_ids = [(4, res.id)]
        return res


class AccountMove(models.Model):
    _inherit = 'account.move'

    dressing_id = fields.Many2one('dressing', string='Dressing')


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    panel = fields.Selection(selection_add=[('dressing', 'Dressing')], string='Panel')


class SupplyConsume(models.Model):
    _inherit = 'supply.consumption'

    dressing_id = fields.Many2one('dressing', string='Dressing')


class DressingConsumptionLine(models.Model):
    _name = 'dressing.consumption.line'
    _description = 'Dressing Consumption Line'

    # dressing_id = fields.Many2one('dressing', string='Dressing', required=True, ondelete='cascade')
    dressing_request_line_id = fields.Many2one('dressing', string='Dressing Process Line',
                                               ondelete='cascade')

    product_id = fields.Many2one('product.product', 'Product', tracking=True)
    quantity = fields.Float(string='Quantity', required=True)
    available_quantity = fields.Float(string='Available Quantity')
    location_id = fields.Many2one('stock.location', string='Location')

class DressingRequestConsumptionLine(models.Model):
    _name = 'dressing.request.consumption.line'
    _description = 'Dressing Request Consumption Line'

    dressing_process_line_id = fields.Many2one('dressing', string='Dressing Request Line',
                                               ondelete='cascade')
    product_id = fields.Many2one('product.product', 'Product', tracking=True)
    quantity = fields.Float(string='Quantity', required=True)
    available_quantity = fields.Float(string='Available Quantity')
    location_id = fields.Many2one('stock.location', string='Location')
