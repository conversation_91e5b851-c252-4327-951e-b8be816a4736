<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="blood_transfusion_product_charges" model="product.product">
            <field name="name">Blood Transfusion Charges</field>
            <field name="type">service</field>
            <field name="is_bt">False</field>
            <field name="purchase_ok">False</field>
            <field name="panel_product">True</field>
        </record>

        <record id="blood_transfusion_product_charges_whole_blood" model="product.product">
            <field name="name">Whole Blood</field>
            <field name="type">service</field>
            <field name="is_bt">True</field>
            <field name="purchase_ok">False</field>
            <field name="panel_product">True</field>
        </record>

        <record id="blood_transfusion_product_charges_plasma" model="product.product">
            <field name="name">Plasma</field>
            <field name="type">service</field>
            <field name="is_bt">True</field>
            <field name="purchase_ok">False</field>
            <field name="panel_product">True</field>
        </record>

        <record id="blood_transfusion_product_charges_packed_rbc" model="product.product">
            <field name="name">Packed RBCs</field>
            <field name="type">service</field>
            <field name="is_bt">True</field>
            <field name="purchase_ok">False</field>
            <field name="panel_product">True</field>
        </record>

        <record id="blood_transfusion_product_charges_platelets" model="product.product">
            <field name="name">Platelets</field>
            <field name="type">service</field>
            <field name="is_bt">True</field>
            <field name="purchase_ok">False</field>
            <field name="panel_product">True</field>
        </record>

        <record id="blood_transfusion_discount_amount" model="product.product">
            <field name="name">BT Discount</field>
            <field name="type">service</field>
            <field name="purchase_ok">False</field>
            <field name="panel_product">True</field>
        </record>
    </data>
</odoo>
