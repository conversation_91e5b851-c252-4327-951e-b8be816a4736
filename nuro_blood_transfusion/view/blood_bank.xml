<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="blood_request_form_view_inherit_bt" model="ir.ui.view">
            <field name="name">Blood Request</field>
            <field name="model">blood.request</field>
            <field name="inherit_id" ref="nuro_blood_bank.nuro_blood_request_form_view_sheet"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='blood_location_id']" position="after">
                    <field name="bt_id" invisible="1"/>
                    <field name="billing_type" invisible="1"/>
                    <field name="issuance" attrs="{'readonly': [('state', '=', 'done')]}" invisible="1"/>
                    <field name="priority"/>
                    <field name="bt_product_ids" widget="many2many_tags" invisible="1"/>
                    <field name="labtest_master_ids" invisible="1" force_save="1" widget="many2many_tags"/>
                    <field name="in_blood_move_line_invisible" invisible="1"/>
                    <field name="out_blood_move_line_invisible" invisible="1"/>
                </xpath>

                <xpath expr="//page[@name='donor_line']" position="before">
                    <page string="Issue Blood" name="in_issue_blood" invisible="1">
                        <!--                          attrs="{'invisible': [('in_blood_move_line_invisible', '=', True)]}">-->
                        <field name="in_blood_move_line" readonly="1">
                            <tree>
                                <field name="name"/>
                                <field name="create_date"/>
                                <field name="type"/>
                                <field name="location_id"/>
                                <field name="dest_location_id"/>
                                <field name="quantity"/>
                                <field name="expiry_date"/>
                                <field name="state"/>
                            </tree>
                        </field>
                    </page>
                    <page string="Blood Consume" name="out_issue_blood" invisible="1">
                        <!--                          attrs="{'invisible': [('out_blood_move_line_invisible', '=', True)]}">-->
                        <field name="out_blood_move_line" readonly="1">
                            <tree>
                                <field name="name"/>
                                <field name="create_date"/>
                                <field name="type"/>
                                <field name="location_id"/>
                                <field name="dest_location_id"/>
                                <field name="quantity"/>
                                <field name="expiry_date"/>
                                <field name="state"/>
                            </tree>
                        </field>
                    </page>
                    <page name="donation_request" string="BT Line">
                        <field name="bt_master_ids" widget="many2many_tags" invisible="1"/>
                        <field name="blood_request_line" attrs="{'readonly': [('state', '!=', 'draft')]}"
                               context="{'default_blood_group_id': blood_group_id}">
                            <tree editable="bottom">
                                <field name="bt_id" invisible="1"/>
                                <field name="br_id" invisible="1"/>
                                <field name="bt_line_id" invisible="1"/>
                                <field name="bt_product_id" options="{'no_create': True, 'no_open': True}"
                                       domain="[('id', 'not in', parent.bt_master_ids),('is_bt', '=', True)]"
                                       attrs="{'readonly': ['|', ('bt_id', '!=', False), ('br_id', '!=', False)]}"
                                       required="1"/>
                                <field name="blood_group_id" options="{'no_create': True, 'no_open': True}"
                                       readonly="1" required="1" force_save="1"/>
                                <field name="priority" required="1"
                                       attrs="{'readonly': ['|', ('bt_id', '!=', False), ('br_id', '!=', False)]}"/>
                                <field name="blood_unit" required="1"
                                       attrs="{'readonly': [('bt_line_id', '!=', False)]}"/>
                                <field name="unit_price" invisible="1"/>
                                <field name="total_amount" invisible="1"/>
                                <field name="state" readonly="1"/>
                            </tree>
                        </field>
                    </page>
                </xpath>

                <xpath expr="//header" position="inside">
                    <field name="bd_donor_req" invisible="1"/>
                    <field name="cashier_request_invisible" invisible="1"/>
                    <field name="donor_line_add_invisible" invisible="1"/>
                    <field name="issue_blood_donation_validation" invisible="1"/>
                    <button name="create_bt_details" type="object" string="Send To Cashier"
                            attrs="{'invisible': [('cashier_request_invisible', '=', True)]}"
                            class="oe_highlight"/>
                    <button name="issue_blood_details" type="object" string="Issue Blood"
                            attrs="{'invisible': [('issue_blood_donation_validation', '=', True)]}"
                            class="oe_highlight"/>
                </xpath>

                <xpath expr="//button[@name='create_donor_line_addition_wizard']" position="replace">

                    <button name="create_donor_line_addition_wizard" type="object" class="oe_highlight"
                            string="Add Donor" attrs="{'invisible': [('donor_line_add_invisible', '=', True)]}"/>
                </xpath>

            </field>
        </record>

        <record id="blood_request_list_view_inherit" model="ir.ui.view">
            <field name="name">Blood Request</field>
            <field name="model">blood.request</field>
            <field name="inherit_id" ref="nuro_blood_bank.nuro_blood_request_tree_view_sheet"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='blood_group_id']" position="after">
                    <field name="bt_product_ids" widget="many2many_tags"/>
                </xpath>
                <xpath expr="//field[@name='doctor_id']" position="after">
                    <field name="priority"/>
                </xpath>

            </field>
        </record>

    </data>
</odoo>