<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="blood_transfusion_form_view" model="ir.ui.view">
            <field name="name">Blood Transfusion</field>
            <field name="model">nuro.blood.transfusion</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <form string="Blood Transfusion">
                    <header>
                        <field name="free_patient" invisible="1"/>
                        <field name="labtest_result_id" force_save="1" invisible="1"/>
                        <button name="print_blood_transfusion_afive_receipt" string="Print Request" type="object"
                                class="oe_highlight" states="request,send_to_lab"/>
                        <button name="%(nuro_blood_transfusion.action_nuro_blood_transfusion_cash_wizard)d"
                                type="action" string="Cash"
                                attrs="{'invisible': [('state', 'not in', ('draft','request'))]}"
                                groups="nuro_cashier_closing.group_cashier_user" class="oe_highlight"/>
                        <button name="%(nuro_blood_transfusion.action_blood_transfusion_credit_payment)d"
                                type="action" string="Credit"
                                attrs="{'invisible': [('state', 'not in', ('draft','request'))]}"
                                groups="nuro_cashier_closing.group_cashier_user" class="oe_highlight"/>

                        <button name="%(nuro_blood_transfusion.action_bt_free_patient_wizard_appointment)d"
                                type="action" string="Free Patient" class="oe_highlight"
                                attrs="{'invisible': ['|', ('free_patient', '=', False), ('state', 'not in', ('draft', 'request'))]}"
                                groups="nuro_cashier_payment_setting.group_cashier_free_patient"/>

                        <button name="%(nuro_blood_transfusion.action_nuro_medical_bt_refund_wizard)d"
                                type="action" string="Refund" states="send_to_lab"
                                groups="nuro_cashier_closing.group_cashier_user" class="oe_highlight"/>
                        <button name="print_receipt" type="object" string="Print Receipt" class="oe_highlight"
                                states="send_to_lab" groups="nuro_cashier_closing.group_cashier_user"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,send_to_lab"
                               statusbar_colors='{"draft":"blue","send_to_lab":"green"}'/>
                    </header>
                    <sheet string="Blood Transfusion">
                        <style>
                            .o_form_view .o_horizontal_separator {
                            color: #174290;
                            font-weight: bold;
                            }
                            a {
                            color: #174290;
                            text-decoration: none;
                            background-color: transparent;
                            }
                            h1 {
                            color: #174290;
                            }
                        </style>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name" string="Blood Transfusion #"/>
                                <h1>
                                    <field name="name" class="oe_inline" default_focus="1" readonly="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group string="Patient Information">
                                <field name="patient_id" required="1"
                                       attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="patient_parent_id" readonly="1" force_save="1"
                                       attrs="{'invisible': [('patient_parent_id', '=', False)]}"/>
                                <field name="identification_code" readonly="1" force_save="1"/>
                                <field name="gender" readonly="1" force_save="1"/>
                                <field name="age" readonly="1" force_save="1"/>
                                <field name="mobile" readonly="1" force_save="1"/>
                                <separator string="Donor &amp; Blood Information"/>
                                <field name="blood_unit" required="1" readonly="1"/>
                                <field name="user_id" readonly="1" force_save="1" invisible="1"/>
                            </group>
                            <group string="Information">
                                <field name="free_patient" attrs="{'readonly': [('state', '!=', 'draft')]}"
                                       groups="nuro_cashier_payment_setting.group_cashier_free_patient"/>
                                <field name="out" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="doctor_id" attrs="{
                                'readonly': [('state', '!=', 'draft')],
                                'required': [('out', '=', False)],
                                'invisible': [('out', '=', True)]
                                }"/>
                                <separator string="Lab Info"/>
                                <field name="date" required="1" readonly="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="BT Request" name="bt_request">
                                <field name="blood_request_line" readonly="1">
                                    <tree>
                                        <field name="bt_product_id" required="1"/>
                                        <field name="blood_group_id" options="{'no_create': True, 'no_open': True}"
                                               readonly="1" required="1"/>
                                        <field name="priority" readonly="1" required="1"/>
                                        <field name="blood_unit" required="1"/>
                                        <field name="unit_price" readonly="1"/>
                                        <field name="total_amount" readonly="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                        <hr/>
                        <group groups="nuro_cashier_closing.group_cashier_user">
                            <group>
                                <field name="payment_method" readonly="1" invisible="1"/>
                                <field name="donor_number" readonly="1"/>
                            </group>
                            <group class="oe_subtotal_footer oe_right">
                                <field name="subtotal" class="oe_subtotal_footer oe_right" force_save="1"/>
                                <field name="discount_amount" class="oe_subtotal_footer oe_right" readonly="1"
                                       force_save="1"/>
                                <field name="total_amount" class="oe_subtotal_footer oe_right" force_save="1"/>
                                <field name="paid_amount" attrs="{'invisible': [('paid_amount', '=', 0.0)]}"
                                       class="oe_subtotal_footer oe_right"/>
                                <field name="credit_amount" attrs="{'invisible': [('credit_amount', '=', 0.0)]}"
                                       class="oe_subtotal_footer oe_right"/>
                                <field name="balance_amount" class="oe_subtotal_footer oe_right" force_save="1"/>
                                <field name="refund_amount" class="oe_subtotal_footer oe_right" force_save="1"
                                       readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="blood_transfusion_list_view" model="ir.ui.view">
            <field name="name">Blood Transfusion</field>
            <field name="model">nuro.blood.transfusion</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <tree string="Blood Transfusion">
                    <field name="name"/>
                    <field name="patient_id"/>
                    <field name="identification_code"/>
                    <field name="mobile" optional="hide"/>
                    <field name="gender" optional="hide"/>
                    <field name="date"/>
                    <field name="balance_amount" groups="nuro_cashier_closing.group_cashier_user"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="nuro_blood_transfusion_search_view" model="ir.ui.view">
            <field name="name">Blood Transfusion</field>
            <field name="model">nuro.blood.transfusion</field>
            <field name="arch" type="xml">
                <search string="Blood Transfusion">
                    <field name="patient_id" string="Patient"/>
                    <field name="mobile" string="Mobile"/>
                    <field name="name" string="Name"/>
                    <separator/>
                    <filter string="Today Transfusion" name="transfusion_date_cashier"
                            domain="[('date', '&gt;=', current_date),
                            ('date', '&lt;=', current_date)]" context="{}"/>
                    <filter name="gender_male" string="Male" domain="[('gender', '=', 'male')]"/>
                    <filter name="gender_female" string="Female" domain="[('gender', '=', 'female')]"/>
                    <filter name="draft_state" string="Draft" domain="[('state', '=', 'draft')]"/>
                    <filter name="request_state" string="Request" domain="[('state', '=', 'request')]"/>
                    <filter name="send_to_lab_state" string="Send To Lab" domain="[('state', '=', 'send_to_lab')]"/>
                    <filter name="cancel_state" string="Cancelled" domain="[('state', '=', 'cancel')]"/>
                    <separator/>
                    <group expand="0" string="Group By..." colspan="11" col="11">
                        <filter string="Patient" name="patient_group_by" context="{'group_by':'patient_id'}"/>
                        <filter string="Doctor" name="doctor_group_by" context="{'group_by':'doctor_id'}"/>
                        <filter string="Gender" name="gender_group_by" context="{'group_by':'gender'}"/>
                        <filter string="Date" name="date_group_by"
                                context="{'group_by':'date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_blood_transfusion" model="ir.actions.act_window">
            <field name="name">Blood Transfusion</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.blood.transfusion</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="nuro_blood_transfusion_search_view"/>
            <field name="context">{'search_default_transfusion_date_cashier': 1, 'create': False}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('blood_transfusion_list_view')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('blood_transfusion_form_view')})]"/>
        </record>


        <!--=========================================BT Doctor Menu==============================================-->
        <record id="blood_transfusion_form_view_doctor" model="ir.ui.view">
            <field name="name">Blood Transfusion</field>
            <field name="model">nuro.blood.transfusion</field>
            <field name="priority">2</field>
            <field name="arch" type="xml">
                <form string="Blood Transfusion">
                    <header>
                        <button name="transfusion_done_creation" type="object" class="oe_highlight" string="Complete BT"
                                states="send_to_lab" groups="nuro_hms_groups.group_doctor_user"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft"
                               statusbar_colors='{"draft":"blue","send_to_lab":"green"}'/>
                    </header>
                    <sheet string="Blood Transfusion">
                        <style>
                            .o_form_view .o_horizontal_separator {
                            color: #174290;
                            font-weight: bold;
                            }
                            a {
                            color: #174290;
                            text-decoration: none;
                            background-color: transparent;
                            }
                            h1 {
                            color: #174290;
                            }
                        </style>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name" string="Blood Transfusion #"/>
                                <h1>
                                    <field name="name" class="oe_inline" default_focus="1" readonly="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group string="Patient Information">
                                <field name="patient_id" readonly="1"/>
                                <field name="identification_code" readonly="1" force_save="1"/>
                                <field name="gender" readonly="1" force_save="1"/>
                                <field name="age" readonly="1" force_save="1"/>
                                <field name="mobile" readonly="1" force_save="1"/>
                                <separator string="Donor &amp; Blood Information"/>
                                <field name="blood_group_id" readonly="1"/>
                                <field name="bt_product_ids" force_save="1" required="1" widget="many2many_tags"
                                       options="{'no_open': True, 'no_create': True}" readonly="1"/>
                                <field name="blood_unit" required="1"
                                       attrs="{'readonly': [('state', 'not in', ('draft', 'request'))]}"/>
                                <field name="user_id" readonly="1" force_save="1" invisible="1"/>
                            </group>
                            <group string="Information">
                                <field name="out" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="doctor_id" attrs="{
                                'readonly': [('state', '!=', 'draft')],
                                'required': [('out', '=', False)],
                                'invisible': [('out', '=', True)]
                                }"/>
                                <separator string="Lab Info"/>
                                <field name="date" required="1" readonly="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="BT Request" name="bt_request">
                                <field name="blood_request_line" readonly="1">
                                    <tree>
                                        <field name="bt_product_id" required="1"/>
                                        <field name="blood_unit" required="1"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Issue Blood" name="in_issue_blood">
                                <field name="in_blood_move_line" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="create_date"/>
                                        <field name="type"/>
                                        <field name="location_id"/>
                                        <field name="dest_location_id"/>
                                        <field name="quantity"/>
                                        <field name="expiry_date"/>
                                        <field name="state"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Blood Consume" name="out_issue_blood">
                                <field name="out_blood_move_line" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="create_date"/>
                                        <field name="type"/>
                                        <field name="location_id"/>
                                        <field name="dest_location_id"/>
                                        <field name="quantity"/>
                                        <field name="expiry_date"/>
                                        <field name="state"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Blood Donor Lines" name="blood_donor_line">
                                <field name="donor_request_line" readonly="1">
                                    <tree>
                                        <field name="donor_id" required="1"/>
                                        <field name="blood_group_id" readonly="1"/>
                                        <field name="qty" readonly="1"/>
                                        <field name="questionnaire_result" readonly="1"/>
                                        <field name="health_assessment_result" readonly="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="blood_transfusion_list_view_doctor" model="ir.ui.view">
            <field name="name">Blood Transfusion</field>
            <field name="model">nuro.blood.transfusion</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <tree string="Blood Transfusion">
                    <field name="name"/>
                    <field name="patient_id"/>
                    <field name="identification_code"/>
                    <field name="mobile" optional="hide"/>
                    <field name="gender" optional="hide"/>
                    <field name="date"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="nuro_blood_transfusion_search_view_doctor" model="ir.ui.view">
            <field name="name">Blood Transfusion</field>
            <field name="model">nuro.blood.transfusion</field>
            <field name="arch" type="xml">
                <search string="Blood Transfusion">
                    <field name="patient_id" string="Patient"/>
                    <field name="mobile" string="Mobile"/>
                    <field name="name" string="Name"/>
                    <separator/>
                    <filter string="Today Transfusion" name="transfusion_date_cashier"
                            domain="[('date', '&gt;=', current_date),
                            ('date', '&lt;=', current_date)]" context="{}"/>
                    <filter name="gender_male" string="Male" domain="[('gender', '=', 'male')]"/>
                    <filter name="gender_female" string="Female" domain="[('gender', '=', 'female')]"/>
                    <filter name="draft_state" string="Draft" domain="[('state', '=', 'draft')]"/>
                    <filter name="request_state" string="Request" domain="[('state', '=', 'request')]"/>
                    <filter name="send_to_lab_state" string="Send To Lab" domain="[('state', '=', 'send_to_lab')]"/>
                    <filter name="cancel_state" string="Cancelled" domain="[('state', '=', 'cancel')]"/>
                    <separator/>
                    <group expand="0" string="Group By..." colspan="11" col="11">
                        <filter string="Patient" name="patient_group_by" context="{'group_by':'patient_id'}"/>
                        <filter string="Doctor" name="doctor_group_by" context="{'group_by':'doctor_id'}"/>
                        <filter string="Gender" name="gender_group_by" context="{'group_by':'gender'}"/>
                        <filter string="Date" name="date_group_by"
                                context="{'group_by':'date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_blood_transfusion_doctor" model="ir.actions.act_window">
            <field name="name">Blood Transfusion</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.blood.transfusion</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', 'in', ('send_to_lab', 'done'))]</field>
            <field name="search_view_id" ref="nuro_blood_transfusion_search_view_doctor"/>
            <field name="context">{'search_default_transfusion_date_cashier': 1, 'create': False, 'edit': False}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('blood_transfusion_list_view_doctor')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('blood_transfusion_form_view_doctor')})]"/>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="nuro_medical_labtest_sample_collection_form_blood_bank" model="ir.ui.view">
            <field name="name">Sample Collection Form</field>
            <field name="model">nuro.medical.labtest.result</field>
            <field name="inherit_id" ref="nuro_labtest.nuro_medical_labtest_sample_collection_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//div[hasclass('oe_left')]" position="after">
                    <field name="bt_request_details" invisible="1"/>
                    <div class="oe_right" style="width: 500px;"
                         attrs="{'invisible': [('bt_request_details', '=', False)]}">
                        <div class="oe_title" style="width: 390px;">
                            <label class="oe_edit_only" for="name" string="Blood Bank #"/>
                            <h1>
                                Blood Bank Request
                            </h1>
                        </div>
                    </div>
                </xpath>

            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="nuro_labtest_result_blood_transfusion_form_inherit" model="ir.ui.view">
            <field name="name">Labtest</field>
            <field name="model">nuro.medical.labtest.result</field>
            <field name="priority">1</field>
            <field name="inherit_id" ref="nuro_labtest.nuro_medical_labtest_criteria_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <field name="blood_transfusion_id" invisible="1"/>
                    <button name="print_blood_transfusion_labtest_report" type="object"
                            string="Blood Transfusion Report"
                            attrs="{'invisible': ['|',('state', '!=', 'completed'), ('blood_transfusion_id', '=', False)]}"
                            class="oe_highlight"/>
                    <button name="print_blood_group_labtest_report" type="object"
                            string="Blood Group Report"
                            attrs="{'invisible': ['|',('state', '!=', 'completed'), ('blood_group_bool', '=', False)]}"
                            class="oe_highlight"/>
                    <field name="blood_donor_id" invisible="1"/>
                    <field name="blood_unit" invisible="1"/>
                    <field name="blood_group_id" invisible="1"/>
                    <field name="donor_request_id" invisible="1"/>
                    <field name="bt_request_details" invisible="1"/>
                </xpath>

                <xpath expr="//div[hasclass('oe_left')]" position="after">
                    <div class="oe_right" style="width: 500px;"
                         attrs="{'invisible': [('bt_request_details', '=', False)]}">
                        <div class="oe_title" style="width: 390px;">
                            <label class="oe_edit_only" for="name" string="Blood Bank #"/>
                            <h1>
                                Blood Bank Request
                            </h1>
                        </div>
                    </div>
                </xpath>

                <xpath expr="//field[@name='labtest_master_id']" position="after">
                    <separator string="Recipient Information"
                               attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                    <field name="recipient_patient_id" readonly="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"
                           options="{'no_create': True,'no_open': True}"/>
                    <field name="recipient_identification_code" readonly="1" force_save="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                    <field name="recipient_gender" readonly="1" force_save="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                    <field name="recipient_mobile" readonly="1" force_save="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                    <field name="recipient_age" readonly="1" force_save="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                </xpath>

                <xpath expr="//group[@name='patient_info']" position="attributes">
                    <attribute name="attrs">{'invisible': [('donor_request_id', '!=', False)]}</attribute>
                </xpath>

                <xpath expr="//group[@name='patient_info']" position="after">
                    <group name="donor_info" string="Donor Information"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}">
                        <field name="patient_id" context="{'create': False, 'edit': False}" readonly="1"/>
                        <field name="identification_code" readonly="1" force_save="1"/>
                        <field name="gender" readonly="1" force_save="1"/>
                        <field name="mobile" readonly="1" force_save="1"/>
                        <field name="age" readonly="1" force_save="1"/>
                    </group>
                </xpath>

                <xpath expr="//notebook" position="inside">
                    <page string="Blood Transfusion Reports"
                          attrs="{'invisible': [('bt_request_details', '!=', True)]}">
                        <group>
                            <group>
                                <field name="negative_a" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="negative_b" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="negative_d" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="a_cell" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="b_cell" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="donor_blood_group" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                            </group>
                            <group>
                                <field name="antibody_screening"
                                       attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="antibody_identification_panel"
                                       attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="direct_antiglobulin"
                                       attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="final_result"
                                       attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                            </group>
                        </group>
                        <group>
                            <field name="compatibility_test_line"
                                   attrs="{'readonly': [('state', '!=', 'in_progress')]}">
                                <tree editable="bottom">
                                    <field name="match_result"/>
                                    <field name="match_interpretation"/>
                                </tree>
                            </field>
                        </group>
                    </page>
                    <page string="Blood Group Reports"
                          attrs="{'invisible': ['|',('blood_group_bool', '=', False),('parent_id', '=', False)]}">
                        <group>
                            <group name="Result" colspan="2" col="2">
                                <field name="labtest_result_lines" force_save="1" nolabel="1"
                                       attrs="{'readonly': [('state', '!=', 'in_progress')]}">
                                    <tree editable="bottom" create="false">
                                        <field name="sequence" force_save="1" readonly="1"/>
                                        <field name="is_master" invisible="1"/>
                                        <field name="name" force_save="1" readonly="1"/>
                                        <field name="normal_range" force_save="1" readonly="1" attrs="{'column_invisible': [('parent.flag_not_applicable', '!=', True)]}"/>
                                        <field name="min_value" force_save="1" readonly="1" attrs="{'column_invisible': [('parent.flag_not_applicable', '=', True)]}"/>
                                        <field name="max_value" force_save="1" readonly="1" attrs="{'column_invisible': [('parent.flag_not_applicable', '=', True)]}"/>
                                        <field name="result_value" attrs="{'readonly': [('is_master', '=', True)], 'column_invisible': [('parent.flag_not_applicable', '=', True)]}"/>
                                        <field name="outcome_result" attrs="{'readonly': [('is_master', '=', True)], 'column_invisible': [('parent.flag_not_applicable', '!=', True)]}"/>
                                        <field name="test_ranges_ids" readonly="1" invisible="1"/>
                                        <field name="test_ranges_id" options="{'no_create': True,'no_open': True}" attrs="{'readonly': [('is_master', '!=', True)]}" domain="[('id', 'in', test_ranges_ids)]"/>
                                        <field name="units" invisible="1" readonly="1" force_save="1"/>
                                        <button name="change_result_open_wizard" type="object" class="oe_highlight"
                                                attrs="{'invisible': [('parent.state', '!=', 'completed')]}"
                                                string="Change Result"
                                                groups="nuro_labtest.group_labtest_result_change_user"/>
                                    </tree>
                                </field>
                            </group>
                            <group>
                                <field name="negative_a" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="negative_b" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="negative_d" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                            </group>
                            <group>
                                <field name="a_cell" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="b_cell" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="b_cell" attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                                <field name="result_blood_group_id" options="{'no_create': True, 'no_open': True}"
                                       attrs="{'readonly': [('state', '!=', 'in_progress')]}"/>
                            </group>
                        </group>
                    </page>

                </xpath>

            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="nuro_labtest_result_blood_transfusion_sample_collection_form_inherit" model="ir.ui.view">
            <field name="name">Labtest</field>
            <field name="model">nuro.medical.labtest.result</field>
            <field name="priority">2</field>
            <field name="inherit_id" ref="nuro_labtest.nuro_medical_labtest_sample_collection_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//header">
                    <field name="blood_donor_id" invisible="1"/>
                    <field name="blood_unit" invisible="1"/>
                    <field name="blood_group_id" invisible="1"/>
                    <field name="donor_request_id" invisible="1"/>
                </xpath>

                <xpath expr="//div[hasclass('oe_left')]" position="after">
                    <div class="oe_right" style="width: 500px;"
                         attrs="{'invisible': [('donor_request_id', '=', False)]}">
                        <div class="oe_title" style="width: 390px;">
                            <label class="oe_edit_only" for="name" string="Blood Bank #"/>
                            <h1>
                                Blood Bank Request
                            </h1>
                        </div>
                    </div>
                </xpath>

                <xpath expr="//field[@name='labtest_master_id']" position="after">
                    <separator string="Recipient Information"
                               attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                    <field name="recipient_patient_id" readonly="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"
                           options="{'no_create': True,'no_open': True}"/>
                    <field name="recipient_identification_code" readonly="1" force_save="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                    <field name="recipient_gender" readonly="1" force_save="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                    <field name="recipient_mobile" readonly="1" force_save="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                    <field name="recipient_age" readonly="1" force_save="1"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}"/>
                </xpath>

                <xpath expr="//group[@name='patient_info']" position="attributes">
                    <attribute name="attrs">{'invisible': [('donor_request_id', '!=', False)]}</attribute>
                </xpath>

                <xpath expr="//group[@name='patient_info']" position="after">
                    <group name="donor_info" string="Donor Information"
                           attrs="{'invisible': [('donor_request_id', '=', False)]}">
                        <field name="patient_id" readonly="1"/>
                        <field name="identification_code" readonly="1" force_save="1"/>
                        <field name="gender" readonly="1" force_save="1"/>
                        <field name="mobile" readonly="1" force_save="1"/>
                        <field name="age" readonly="1" force_save="1"/>
                    </group>
                </xpath>

            </field>
        </record>
    </data>
</odoo>
