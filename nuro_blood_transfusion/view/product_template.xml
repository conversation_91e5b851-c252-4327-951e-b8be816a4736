<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="product_template_form_view_form" model="ir.ui.view">
            <field name="name">Product</field>
            <field name="model">product.template</field>
            <field name="priority" eval="17"/>
            <field name="arch" type="xml">
                <form string="Product">
                    <sheet>
                        <style>
                            .o_form_view .o_horizontal_separator {
                            color: #174290;
                            font-weight: bold;
                            }
                            a {
                            color: #174290;
                            text-decoration: none;
                            background-color: transparent;
                            }
                            h1 {
                            color: #174290;
                            }
                        </style>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name" string="Name #"/>
                                <h1>
                                    <field name="name" class="oe_inline" default_focus="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group>
                                <field name="labtest_test_charge" groups="nuro_cashier_closing.group_cashier_user"/>
                                <field name="package_charges" groups="nuro_cashier_closing.group_cashier_user"/>
                                <field name="list_price" invisible="1"/>
                                <field name="is_bt" invisible="1"/>
                            </group>
                            <group>
                                <field name="type" invisible="1"/>
                                <field name="categ_id" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Labtest Line">
                                <field name="labtest_master_ids" widget="many2many_tags" invisible="1"/>
                                <field name="labtest_line" string="Labtest Line">
                                    <tree editable="bottom">
                                        <field name="labtest_master_id" required="1"
                                               options="{'no_open': True, 'no_create': True}"
                                               domain="[('id', 'not in', parent.labtest_master_ids)]"/>
                                        <field name="department_id" readonly="1"/>
                                        <field name="unit_price" readonly="1"/>
                                        <field name="package_price"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="product_template_tree_view" model="ir.ui.view">
            <field name="name">Product</field>
            <field name="model">product.template</field>
            <field name="priority" eval="17"/>
            <field name="arch" type="xml">
                <tree string="Product">
                    <field name="name"/>
                    <field name="labtest_test_charge" groups="nuro_cashier_closing.group_cashier_user"/>
                    <field name="package_charges" groups="nuro_cashier_closing.group_cashier_user"/>
                </tree>
            </field>
        </record>

        <record id="action_menu_bt_product_configuration" model="ir.actions.act_window">
            <field name="name">Blood Transfusion Type</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.template</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('is_bt', '=', True)]</field>
            <field name="context">{'default_is_bt': True}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('product_template_tree_view')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('product_template_form_view_form')})]"/>
        </record>
    </data>
</odoo>