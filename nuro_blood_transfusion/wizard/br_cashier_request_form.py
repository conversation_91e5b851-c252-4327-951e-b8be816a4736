# -*- coding: utf-8 -*-

from odoo.exceptions import UserError

# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import fields, models, _


class BloodTransfusionCRWizard(models.TransientModel):
    _name = 'blood.transfusion.cr.wizard'
    _description = 'Blood Transfusion Cashier Request Wizard'

    br_id = fields.Many2one('blood.request', string='Blood Request')
    blood_request_line = fields.One2many('blood.transfusion.cr.wizard.line', 'btcw_id', string='Blood Transfusion Line')

    def create_bt_vals(self):
        """
        Create Blood Transfusion
        """
        lines = self.blood_request_line.filtered(lambda x: x.blood_unit > 0.0)
        qty = sum(lines.mapped('blood_unit'))
        product_ids = lines.mapped('bt_product_id')
        out = False
        if not self.br_id.doctor_id:
            out = True
        vals = {
            'patient_id': self.br_id.patient_id.id,
            'identification_code': self.br_id.identification_code,
            'gender': self.br_id.gender,
            'age': self.br_id.age,
            'mobile': self.br_id.mobile,
            'doctor_id': self.br_id.doctor_id and self.br_id.doctor_id.id or False,
            'out': out,
            'blood_request_id': self.br_id.id,
            'blood_group_id': self.br_id.blood_group_id.id,
            'priority': self.br_id.priority,
            'blood_unit': qty,
            'bt_product_ids': [(6, 0, product_ids.ids)],
            'donor_number': len(lines)
        }
        return vals

    def create_bt_request(self):
        """
        Create Blood Transfusion Request
        """
        lines = self.blood_request_line.filtered(lambda x: x.blood_unit > 0.0)
        if not lines:
            raise UserError(_('There are no lines to request.!!!'))
        bt_vals = self.create_bt_vals()
        bt_obj = self.env['nuro.blood.transfusion']
        line_list = []
        for line in lines:
            line_vals = {
                'bt_product_id': line.bt_product_id.id,
                'priority': line.priority,
                'blood_group_id': line.blood_group_id.id,
                'blood_unit': line.blood_unit,
            }
            line_list.append((0, 0, line_vals))
            line.bt_request_id.unit_billed += line.blood_unit
        bt_vals.update({
            'blood_request_line': line_list
        })
        bt_id = bt_obj.create(bt_vals)
        for bt_line in bt_id.blood_request_line:
            bt_line.get_total_amount()
        self.blood_request_line.mapped('bt_request_id').write({'bt_line_id': bt_id.id})
        bt_id.onchange_bt_product_id()
        self.br_id.state = 'waiting_payment'
        self.br_id.bt_id = bt_id.id


class BloodTransfusionCRWizardLine(models.TransientModel):
    _name = 'blood.transfusion.cr.wizard.line'
    _description = 'Blood Transfusion Cashier Request Wizard Line'

    btcw_id = fields.Many2one('blood.transfusion.cr.wizard', string='Blood Transfusion Cashier Request Wizard')
    bt_request_id = fields.Many2one('bt.request.line', string='Blood Request')
    bt_product_id = fields.Many2one('product.product', string="BT Type")
    priority = fields.Selection([('high', 'High'), ('low', 'Low'), ('medium', 'Medium')], string="Priority")
    blood_group_id = fields.Many2one("blood.group", string="Blood Type")
    blood_unit = fields.Float('Unit')
    unit_billed = fields.Float('Unit Billed')
