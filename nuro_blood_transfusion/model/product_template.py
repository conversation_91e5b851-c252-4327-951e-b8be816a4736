# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
"""
Module Docstring
"""
from odoo import fields, models, api


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    is_bt = fields.Boolean('Blood Transfusion')
    labtest_line = fields.One2many('nuro.labtest.line.details', 'product_id', string='Labtest Line')
    labtest_test_charge = fields.Float('Service Charges', tracking=True, compute='compute_medical_charge', store=True)
    package_charges = fields.Float('Package Charges', tracking=True, compute='compute_medical_charge', store=True)
    labtest_master_ids = fields.Many2many('nuro.labtest.master', 'product_labtest_master_rel', 'product_id',
                                          'master_id', string="Labtest Master IDS", compute="compute_medical_charge")

    @api.depends('labtest_line')
    def compute_medical_charge(self):
        """
        Compute Package and Total Amount
        :return:
        """
        for rec in self:
            lab_unit_amount = sum(rec.labtest_line.mapped('unit_price'))
            lab_package_amount = sum(rec.labtest_line.mapped('package_price'))
            rec.labtest_test_charge = lab_unit_amount
            rec.package_charges = lab_package_amount
            rec.labtest_master_ids = [(6, 0, rec.labtest_line.mapped('labtest_master_id').ids)]


class LabtestMasterDetails(models.Model):
    _name = 'nuro.labtest.line.details'
    _description = 'Labtest Line'
    _order = 'id DESC'

    labtest_master_id = fields.Many2one('nuro.labtest.master', string='Labtest Master')
    department_id = fields.Many2one('nuro.labtest.department', related='labtest_master_id.department_id', store=True,
                                    string='Department')
    unit_price = fields.Float('Unit Price', related='labtest_master_id.test_charge', store=True)
    package_price = fields.Float('Package Price')
    product_id = fields.Many2one('product.template')
