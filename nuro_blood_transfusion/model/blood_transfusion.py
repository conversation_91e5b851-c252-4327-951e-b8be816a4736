# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED

from datetime import datetime

from dateutil.relativedelta import relativedelta
from odoo.exceptions import UserError

from odoo import api, fields, models, _

PAYMENT_METHOD = [
    ('cash', 'Cash'),
    ('free', 'Free'),
    ('credit', 'Credit')
]

GENDER = [
    ('male', 'Male'),
    ('female', 'Female')
]

BLOOD_GROUP = [
    ('opve', 'O +ve'),
    ('onve', 'O -ve'),
    ('apve', 'A +ve'),
    ('anve', 'A -ve'),
    ('bpve', 'B +ve'),
    ('bnve', 'B -ve'),
    ('abpve', 'AB +ve'),
    ('abnve', 'AB -ve')
]


class NuroBloodTransfusion(models.Model):
    _name = 'nuro.blood.transfusion'
    _description = 'Blood Transfusion'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id DESC'

    STATE = [
        ('draft', 'Draft'),
        ('request', 'Request'),
        ('send_to_lab', 'Payment Done'),
        ('transfusion_done', 'Transfusion Done'),
        ('cancel', 'Cancel')
    ]

    name = fields.Char(string='Blood Transfusion #', size=16, required=True, readonly=True, default=lambda *a: '/',
                       index=True)
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user)
    patient_id = fields.Many2one('nuro.patient', string='Patient', help="Patient Name",
                                 domain=[('deceased', '!=', True)])
    patient_parent_id = fields.Many2one('nuro.patient', string='Parent')
    blood_unit = fields.Float('Blood Unit Required')
    blood_donor_id = fields.Many2one("blood.donor", string="Donor")
    donor_number = fields.Integer('Donor Count')
    priority = fields.Selection([('high', 'High'), ('low', 'Low'), ('medium', 'Medium')], string="Priority")
    blood_group_id = fields.Many2one("blood.group", string="Blood Group Required")
    identification_code = fields.Char(string='ID#')
    gender = fields.Selection(GENDER, string='Gender', store=True)
    age = fields.Char('Patient Age')
    mobile = fields.Char('Mobile')
    doctor_id = fields.Many2one('nuro.doctor', string='Doctor')
    out = fields.Boolean('Outside Doctor')
    free_patient = fields.Boolean('Free Patient', default=False)
    date = fields.Datetime('Date', default=fields.Datetime.now)
    state = fields.Selection(STATE, default='draft', string='State')
    subtotal = fields.Float('Subtotal', tracking=True, readonly=True)
    discount_amount = fields.Float('Discount', tracking=True, readonly=True)
    total_amount = fields.Float('Total', tracking=True, readonly=True,
                                compute='amount_total_value')
    paid_amount = fields.Float('Paid', tracking=True, readonly=True, copy=False)
    credit_amount = fields.Float('Credit', tracking=True, readonly=True, copy=False)
    balance_amount = fields.Float('Balance', tracking=True, readonly=True,
                                  compute='balance_amount_value')
    refund_amount = fields.Float(string='Refund Amount', tracking=True)
    payment_method = fields.Selection(PAYMENT_METHOD, string='Payment Method', readonly=True, copy=False,
                                      tracking=True)
    bt_product_id = fields.Many2one('product.product', string="Blood Transfusion Type", domain=[('is_bt', '=', True)])
    bt_product_ids = fields.Many2many('product.product', string="Blood Transfusion Type", domain=[('is_bt', '=', True)])
    labtest_result_id = fields.Many2one('nuro.medical.labtest.result', string='Result')
    cashier_user = fields.Many2one('res.users')
    description_hospital = fields.Char('Description')
    expense_to_hospital = fields.Boolean('Expense To Hospital')
    employee_id = fields.Many2one('hr.employee', string='Employee')
    payment_id = fields.Many2one('account.payment')
    approve_by = fields.Many2one('hr.employee', 'Approved By', domain=lambda self: [
        ('user_id', '!=', False),
        ('user_id.groups_id', 'in', self.env.ref('nuro_cashier_payment_setting.discount_request_approve').id)])
    blood_request_ids = fields.Many2many('blood.request', string="Blood Request IDS")
    blood_request_id = fields.Many2one('blood.request', string="Blood Request IDS")
    blood_request_line = fields.One2many('bt.request.line', 'bt_id', string="Blood Request")
    billing_type = fields.Selection([('pre', 'Pre'), ('post', 'Post')], string="BT Billing Type",
                                    related="company_id.billing_type", store=True)
    donor_request_line = fields.One2many('donor.request.line', 'bt_id', string="Donor Request")
    in_blood_move_line = fields.One2many('blood.group.move', 'in_bt_id', string='Issue Move Line')
    out_blood_move_line = fields.One2many('blood.group.move', 'out_bt_id', string='Consumed Move Line')
    in_blood_move_line_invisible = fields.Boolean(compute='_get_in_out_move_line_invisible')
    out_blood_move_line_invisible = fields.Boolean(compute='_get_in_out_move_line_invisible')

    def _get_in_out_move_line_invisible(self):
        """
        Get In Out Move Line Invisible Form
        """
        for rec in self:
            if rec.in_blood_move_line:
                rec.in_blood_move_line_invisible = False
            else:
                rec.in_blood_move_line_invisible = True

            if rec.out_blood_move_line:
                rec.out_blood_move_line_invisible = False
            else:
                rec.out_blood_move_line_invisible = True

    def transfusion_done_creation(self):
        """
        Make Transfusion Done
        """
        self.state = 'transfusion_done'

    def create_blood_request_vals(self):
        """
        Create Blood Request
        """
        product_ids = self.blood_request_line.mapped('bt_product_id').ids
        unit = sum(self.blood_request_line.mapped('blood_unit'))
        vals = {
            'patient_id': self.patient_id.id,
            'identification_code': self.identification_code,
            'gender': self.gender,
            'age': self.age,
            'mobile': self.mobile,
            'doctor_id': self.doctor_id.id,
            'bt_id': self.id,
            'blood_group_id': self.blood_group_id.id,
            'priority': self.priority,
            'bt_product_ids': [(6, 0, product_ids)],
            'qty': unit,
            'state': 'draft',
            'date': fields.Date.today(),
        }
        return vals

    def request_blood_request(self):
        """
        Request Blood Transfusion
        """
        if self.blood_request_id:
            self.blood_request_id.state = 'in_process'
        if self.env.company.billing_type == 'pre' and not self.blood_request_id:
            bt_request_vals = self.create_blood_request_vals()
            ctx = {'active_model': self._name, 'active_id': self.id}
            blood_request_id = self.env['blood.request'].with_context(ctx).create(bt_request_vals)
            self.blood_request_line.write({
                'br_id': blood_request_id.id
            })
            self.blood_request_ids = [(4, blood_request_id.id)]
            self.blood_request_id = blood_request_id.id
        if self.env.company.billing_type == 'pre' and self.blood_request_id:
            self.blood_request_id.state = 'draft'
        if self.env.company.billing_type == 'post' and self.blood_request_id:
            self.blood_request_id.state = 'payment_done'

    @api.depends('blood_donor_line')
    def get_donor_lines_count(self):
        """
        Donor Lines Count
        """
        for rec in self:
            rec.donor_number = len(rec.blood_donor_line)

    @api.onchange('payment_type')
    def onchange_payment_type(self):
        """
        onchange Payment Type
        """
        if self.payment_type:
            if self.payment_type == 'cash':
                self.bill_to_type = False
                self.bill_to_user_id = False
                self.employee_id = False
                # self.expense_record_id = False
            if self.payment_type == 'expense':
                self.bill_to_type = False
                self.bill_to_user_id = False
                self.employee_id = False
                # self.expense_record_id = False
                partner = self.env['res.partner'].search([])
                return {'domain': {'bill_to_user_id': [('id', '=', partner.ids)]}}
            if self.payment_type == 'credit':
                self.bill_to_type = False
                self.bill_to_user_id = False
                self.employee_id = False
                # self.expense_record_id = False

    @api.onchange('bill_to_type', 'employee_id')
    def onchange_bill_to_type(self):
        """
        Onchange Bill To Type
        """
        if self.bill_to_type:
            if self.bill_to_type == 'patient':
                self.employee_id = False
                self.bill_to_user_id = False
                self.bill_to_user_id = self.patient_id.partner_id.id
            if self.bill_to_type == 'employee' and self.employee_id:
                self.bill_to_user_id = False
                self.bill_to_user_id = self.employee_id.user_id.partner_id.id or self.employee_id.address_home_id.id
            if self.bill_to_type == 'sponsor':
                self.bill_to_user_id = False
                partner = self.env['res.partner'].search([('is_sponsor', '=', True)])
                return {'domain': {'bill_to_user_id': [('id', '=', partner.ids)]}}

    def get_default_context_value(self):
        """
        Get Default Context Value
        """
        vals = {
            'active_model': self._name,
            'active_id': self.id
        }
        if self.payment_type in ('credit', 'expense'):
            vals.update({
                'default_payment_type': self.payment_type,
                'default_bill_to_type': self.bill_to_type,
                'default_bill_to_user_id': self.bill_to_user_id.id,
            })
        return vals

    def create_wizard_cash_payment(self):
        """
        Create Wizard Cash Payment
        :return:
        """
        action = self.env.ref('nuro_blood_transfusion.action_nuro_blood_transfusion_cash_wizard').read()[0]
        ctx = self.get_default_context_value()
        action['context'] = ctx
        return action

    def create_wizard_credit_payment(self):
        """
        Create Wizard Credit Payment
        :return:
        """
        action = self.env.ref('nuro_blood_transfusion.action_blood_transfusion_credit_payment').read()[0]
        ctx = self.get_default_context_value()
        action['context'] = ctx
        return action

    def payment_processing_validation(self):
        """
        Payment Processing Validation
        """
        if not self.payment_type:
            raise UserError(_('Please Select Payment Type Before Proceeding.!!!'))
        if self.payment_type and self.payment_type == 'credit' and not self.bill_to_type:
            raise UserError(_('Please Select Bill to type before processing the payment.!!!'))
        if self.payment_type and self.payment_type == 'credit' and self.bill_to_type in ('sponsor', 'employee'):
            if self.bill_to_type == 'sponsor' and not self.bill_to_user_id:
                raise UserError(_('Please Select Bill To User Before Processing the payment.!!!'))
            if self.bill_to_type == 'employee' and not self.employee_id:
                raise UserError(_('Please Select Employee Before Processing the payment.!!!'))
        # if self.payment_type and self.payment_type == 'expense' and (
        #         not self.expense_record_id or not self.bill_to_user_id):
        #     raise UserError(_('Please Select Expense Record and Bill to before processing the payment.!!!'))
        if self.state not in ('draft', 'request'):
            raise UserError(_('Record has been processed Already.!!!!'))

    def get_payment_bt_format_view(self):
        """
        Get Dental Payment Wizard
        """
        self.payment_processing_validation()
        if self.payment_type == 'cash':
            return self.create_wizard_cash_payment()
        if self.payment_type in ('credit', 'expense'):
            return self.create_wizard_credit_payment()

    def get_last_write_uid_with_user(self):
        """
        Get Write UID
        :return:
        """
        user_id = self.env['res.users'].search([('id', '=', self.write_uid.id)])
        if user_id:
            return user_id.name

    def action_bt_test_cancel(self):
        """
        Search Blood Transfusion
        :return:
        """
        all_draft = all(line.state == 'draft' for line in self.blood_request_ids)
        if self.blood_request_ids and all_draft:
            self.blood_request_ids.write({'state': 'cancel'})
        else:
            raise UserError(_('Refund can not be made as request has been started already.!!!'))

        # search_labtest = self.env['nuro.lab.entry'].search([('blood_transfusion_id', '=', self.id)])
        # if any(line.state in ('completed', 'other_hospital') for line in search_labtest.lab_entry_line):
        #     raise UserError(_('Labtest has been started already can not be cancelled.!!!!'))
        # else:
        #     search_labtest.lab_entry_line.write({
        #         'state': 'cancelled',
        #         'sample_collection_state': 'cancelled'
        #     })
        #     search_labtest.write({
        #         'state': 'cancel'
        #     })

    def print_bt_doctor_request_form(self):
        """receipt printing for the user to get the receipt for the Blood Transfusion"""
        return self.env.ref('nuro_blood_transfusion.action_blood_transfusion_doctor_request_form').report_action(self)

    def print_blood_transfusion_afive_receipt(self):
        """receipt printing for the user to get the receipt for the Blood Transfusion"""
        return self.env.ref('nuro_blood_transfusion.action_nuro_bt_afive_receipt').report_action(self)

    def submit_receipt_bt(self):
        """
        Submit Blood Transfusion Request
        :return:
        """
        self.write({'state': 'request'})
        return self.print_blood_transfusion_afive_receipt()

    def print_receipt(self):
        """
        Blood Transfusion Receipt printing
        :return:
        """
        return self.env.ref('nuro_blood_transfusion.action_nuro_blood_transfusion_thermal_receipt').report_action(self)

    @api.onchange('patient_id')
    def onchange_patient_id(self):
        """
        onchange of patient fetching the information for patient as age mobile and gender
        :return:
        """
        if self.patient_id:
            self.identification_code = self.patient_id.identification_code
            self.age = self.patient_id.age
            self.mobile = self.patient_id.mobile
            self.gender = self.patient_id.gender

    @api.onchange('blood_request_line')
    def onchange_bt_product_id(self):
        """
        Onchange Bt Product
        """
        self.subtotal = sum(self.blood_request_line.mapped('total_amount'))

    @api.model
    def create(self, values):
        """
        creating the sequence on creation of new Blood Transfusion
        :param values:
        :return:
        """
        sequence = self.env['ir.sequence'].next_by_code('nuro.blood.transfusion.sequence')
        values['name'] = sequence
        res = super(NuroBloodTransfusion, self).create(values)
        if not self.env.user.partner_id.tz:
            raise UserError(_('Please Configure the Timezone for current Logged in User.!!!'))
        return res

    @api.depends('subtotal', 'discount_amount')
    def amount_total_value(self):
        """
        Amount Total Value
        :return:
        """
        for rec in self:
            rec.total_amount = rec.subtotal - rec.discount_amount

    @api.depends('paid_amount', 'credit_amount')
    def balance_amount_value(self):
        """
        Amount Balance Value
        :return:
        """
        for rec in self:
            rec.balance_amount = rec.total_amount - (rec.paid_amount + rec.credit_amount)

    def _create_move_lines_bt(self, amount, line):
        """
        creating Blood Transfusion invoice line without discount
        :param amount:
        :return:
        """
        product_id = line.bt_product_id
        if not product_id:
            raise UserError(_('Please configure the Product for the lab!!!'))
        vals = {
            'product_id': product_id.id,
            'name': product_id.name,
            'price_unit': amount,
            'quantity': 1,
            'account_id': product_id.property_account_income_id.id or
                          product_id.categ_id.property_account_income_categ_id.id,
        }
        return vals

    def _create_move_lines_bt_discount(self, amount):
        """
        creating discount invoice line for lab
        :param amount:
        :return:
        """
        product_id = self.env.company.bt_discount_product_id
        account_id = self.env.company.bt_discount_account_id
        if not account_id:
            raise UserError(_('Please configure the Discount account for lab!!!'))
        if not product_id:
            raise UserError(_('Please configure the Product for the lab!!!'))
        self.discount_amount = amount
        vals = {
            'product_id': product_id.id,
            'name': product_id.name,
            'price_unit': -amount,
            'quantity': 1,
            'account_id': account_id.id,
        }
        return vals

    def _create_invoice_dict(self, partner_id, invoice_line_ids, invoice_type):
        """
        preparing invoice Dictionary
        :param invoice_line_ids:
        :param invoice_type:
        :return:
        """
        vals = {
            'partner_id': partner_id.id,
            'state': 'draft',
            'type': invoice_type,
            'invoice_date': fields.datetime.now().date(),
            'ref': "Blood Transfusion # : " + self.name,
            'invoice_origin': self.name,
            'blood_transfusion_id': self.id,
            'patient_id': self.patient_id.id,
            'patient_parent_id': self.patient_parent_id.id,
            'invoice_line_ids': invoice_line_ids,
        }
        return vals

    def create_invoice(self, amount, discount_amount, partner_id, invoice_type):
        """
        Method to create Invoice and return invoice ids in vals
        :param discount_amount:
        :param amount:
        :param partner_id:
        :param invoice_type:
        :return:
        """
        curr_invoice_line = []
        for rec in self:
            amount = amount + discount_amount
            for line in self.blood_request_line:
                income_line = rec._create_move_lines_bt(line=line, amount=line.total_amount)
                curr_invoice_line.append((0, 0, income_line))
            if discount_amount > 0.0:
                discount_line = rec._create_move_lines_bt_discount(
                    amount=discount_amount
                )
                curr_invoice_line.append((0, 0, discount_line))
            # noinspection PyProtectedMember
            curr_invoice = rec._create_invoice_dict(
                partner_id=partner_id,
                invoice_type=invoice_type,
                invoice_line_ids=curr_invoice_line
            )
            inv_ids = rec.env["account.move"].create(curr_invoice)
            if invoice_type == 'out_invoice':
                rec.accounting_date = fields.datetime.now().date()
            else:
                rec.refund_date = fields.datetime.now().date()
            return inv_ids

    def _create_invoice_payment(self, partner_id, amount, journal_id, payment_method_id, invoice_ids, payment_type):
        """
        Private Method to create Invoice Payment
        :param partner_id:
        :param amount:
        :param journal_id:
        :param payment_method_id:
        :param invoice_ids:
        :param payment_type:
        :return:
        """
        vals = {
            'amount': abs(amount),
            'journal_id': journal_id.id,
            # 'patient_id': self.patient_id.id,
            'payment_method_id': payment_method_id,
            'invoice_payment_type': 'automated',
            'blood_transfusion_id': self.id,
            'currency_id': invoice_ids.currency_id.id,
            'payment_type': payment_type,
            'partner_id': partner_id.id,
            'partner_type': 'customer',
            'communication': ' '.join([ref for ref in invoice_ids.mapped('ref') if ref]),
            'invoice_ids': [(6, 0, invoice_ids.ids)]
        }
        return vals

    def create_payment(self, partner_id, amount, journal_id, payment_method_id, invoice_ids, payment_type):
        """
        Create Payment For With the Given attributes
        :param partner_id:
        :param amount:
        :param journal_id:
        :param payment_method_id:
        :param invoice_ids:
        :param payment_type:
        :return:
        """
        for rec in self:
            payment_ids = rec._create_invoice_payment(
                partner_id=partner_id,
                amount=amount,
                journal_id=journal_id,
                payment_method_id=payment_method_id,
                payment_type=payment_type,
                invoice_ids=invoice_ids
            )
            payments = rec.env['account.payment'].create(payment_ids)
            return payments

    def _create_debit_move_line_vals(self, partner_id, amount, account_id):
        """
        Debit Line Dictionary for the account move line
        :param partner_id:
        :param amount:
        :return:
        """
        debit_vals = {
            'name': self.name,
            'debit': round(amount, 2),
            'credit': 0.0,
            'partner_id': partner_id.id,
            'account_id': account_id.id,
            'date': datetime.now().date()
        }
        return debit_vals

    def _create_credit_move_line_vals(self, partner_id, amount, account_id):
        """
        Credit Line Dictionary for the account move line
        :param partner_id:
        :param amount:
        :return:
        """
        credit_vals = {
            'name': self.name,
            'debit': 0.0,
            'credit': amount,
            'partner_id': partner_id.id,
            'account_id': account_id.id,
            'date': datetime.now().date()
        }
        return credit_vals

    def _create_account_move_dict(self, journal_id, line_ids):
        """
        Creating Journal Entry for hospital Expense
        :param journal_id:
        :param line_ids:
        :return:
        """
        vals = {
            'type': 'entry',
            'journal_id': journal_id.id,
            'date': fields.Date.context_today(self),
            'ref': "Blood Transfusion # : " + self.name,
            'blood_transfusion_id': self.id,
            'patient_id': self.patient_id.id,
            'state': 'draft',
            'line_ids': line_ids
        }
        return vals

    def create_account_move(self, journal_id, partner_id, amount, employee_id):
        """
        Creating account move entry
        :param employee_id:
        :param amount:
        :param partner_id:
        :param journal_id:
        :return:
        """
        account_move_obj = self.env['account.move']
        for rec in self:
            line_ids = []
            for blood_req_line in rec.blood_request_line:
            # product_id = self.env['product.product'].browse(self.env.company.bt_product_id.id)
                product_id = blood_req_line.bt_product_id
                credit_account_id = product_id.property_account_income_id or \
                                    product_id.categ_id.property_account_income_categ_id
                if not employee_id:
                    account_id = self.env['account.account'].browse(
                        int(self.env['ir.config_parameter'].sudo().get_param('hospital_expense_account_id')))
                else:
                    account_id = self.env['account.account'].browse(
                        int(self.env['ir.config_parameter'].sudo().get_param('hospital_employee_expense_account_id')))

                if not product_id:
                    raise UserError(_('Please configure the lab Product!!!'))
                if not account_id:
                    raise UserError(_('Please configure Hospital Expense Account and Employee Expense Account!!!'))
                debit_line = rec._create_debit_move_line_vals(
                    partner_id=partner_id, amount=amount, account_id=account_id)
                credit_line = rec._create_credit_move_line_vals(
                    partner_id=partner_id, amount=amount, account_id=credit_account_id)
                line_ids.append((0, 0, debit_line))
                line_ids.append((0, 0, credit_line))
            move = rec._create_account_move_dict(
                journal_id=journal_id, line_ids=line_ids)
            ctx = {'active_model': self._name, 'active_id': self.id}
            move_ids = account_move_obj.with_context(ctx).create(move)
            rec.accounting_date = fields.datetime.now().date()
            return move_ids

    def prepare_lab_entry_line(self):
        """
        Prepare Lab entry Lines
        :return:
        """
        lab_entry_line = []
        for rec in self.bt_product_id.labtest_line:
            lab_entry_line.append((0, 0, {
                'partner_id': self.patient_id.partner_id.id,
                'department_id': rec.department_id.id,
                'labtest_master_id': rec.labtest_master_id.id,
                'test_charge': 0.0,
                'discount_amount': 0.0,
                'total_amount': 0.0,
            }))
        return lab_entry_line

    def prepare_labtest_entry(self, lab_entry_line, donor_line):
        """
        Prepare Labtest entry
        :return:
        """
        vals = {
            'blood_transfusion_id': self.id,
            'patient_id': self.patient_id.id,
            'identification_code': self.identification_code,
            'gender': self.gender,
            'age': self.age,
            'mobile': self.mobile,
            'doctor_id': self.doctor_id.id,
            'date': fields.datetime.now(),
            'blood_unit': self.blood_unit,
            'blood_donor_id': donor_line.blood_donor_id.id,
            'blood_group_id': self.blood_group_id.id,
            'lab_entry_line': lab_entry_line
        }
        return vals

    def create_labtest_entry(self, donor_line):
        """
        Create Labtest Entry and it's value
        :return:
        """
        lab_entry_obj = self.env['nuro.lab.entry']
        lab_entry_line = self.prepare_lab_entry_line()
        labtest_vals = self.prepare_labtest_entry(lab_entry_line=lab_entry_line, donor_line=donor_line)
        lab_entry_id = lab_entry_obj.create(labtest_vals)
        if lab_entry_id:
            labtest_line_list = lab_entry_id.lab_entry_line.ids
            partner_id = self.patient_id.partner_id
            lab_result_id = lab_entry_id._create_labtest_new_record(
                labtest_line_list=labtest_line_list,
                partner_id=partner_id,
                expense=False,
                employee_id=False
            )
            lab_entry_id.state = 'send_to_lab'
            return lab_result_id

    def create_labtest_entry_donor(self):
        """
        Labtest Entry Create
        """
        for line in self.blood_donor_line.filtered(lambda lab: not lab.labtest_result_id):
            labtest = self.create_labtest_entry(donor_line=line)
            line.labtest_result_id = labtest.id


class DonorInformationLines(models.Model):
    _name = 'donor.information.lines'
    _description = 'Donor Information Lines'
    _order = 'id DESC'

    blood_donor_id = fields.Many2one("blood.donor", string="Donor")
    blood_group_id = fields.Many2one("blood.group", string="Blood Group")
    labtest_result_id = fields.Many2one('nuro.medical.labtest.result', string='Result')
    bt_id = fields.Many2one("nuro.blood.transfusion", string="Blood Transfusion")

    def unlink(self):
        """
        Unlink Method Request
        """
        if self.labtest_result_id:
            raise UserError(_('You can not delete the line as it has been processed already.!!!'))
        return super(DonorInformationLines, self).unlink()


class NuroLabEntry(models.Model):
    _inherit = 'nuro.lab.entry'

    blood_unit = fields.Float('Blood Unit')
    blood_donor_id = fields.Many2one("blood.donor", string="Donor")
    blood_group_id = fields.Many2one("blood.group", string="Blood Group")
    blood_transfusion_id = fields.Many2one('nuro.blood.transfusion')

    def update_existing_labtest_lines_record(self, lab_test_id, partner_id, expense=False, employee_id=False):
        """
        Update Existing Labtest Line Record
        :param lab_test_id:
        :param partner_id:
        :param expense:
        :param employee_id:
        :return:
        """
        res = super(NuroLabEntry, self).update_existing_labtest_lines_record(
            lab_test_id=lab_test_id, partner_id=partner_id, expense=expense, employee_id=employee_id)
        res.update({
            'blood_transfusion_id': self.blood_transfusion_id and self.blood_transfusion_id.id or False,
            'blood_unit': self.blood_unit and self.blood_unit or False,
            'blood_donor_id': self.blood_donor_id and self.blood_donor_id.id or False,
            'blood_group_id': self.blood_group_id and self.blood_group_id.id or False,
        })
        return res

    def create_parent_labtest_result_vals(self, expense=False, employee_id=False):
        """
        Create Parent Labtest  Result
        :param expense:
        :param employee_id:
        :return:
        """
        res = super(NuroLabEntry, self).create_parent_labtest_result_vals(expense=expense, employee_id=employee_id)
        res.update({
            'blood_transfusion_id': self.blood_transfusion_id and self.blood_transfusion_id.id or False,
            'blood_unit': self.blood_unit and self.blood_unit or False,
            'blood_donor_id': self.blood_donor_id and self.blood_donor_id.id or False,
            'blood_group_id': self.blood_group_id and self.blood_group_id.id or False,
        })
        return res

    def create_child_labtest_result_vals(self, partner_id, lab_test_id, expense=False, employee_id=False):
        """
        Create Parent Labtest  Result
        :param expense:
        :param employee_id:
        :return:
        """
        res = super(NuroLabEntry, self).create_child_labtest_result_vals(
            partner_id=partner_id, lab_test_id=lab_test_id, expense=expense, employee_id=employee_id)
        res.update({
            'blood_transfusion_id': self.blood_transfusion_id and self.blood_transfusion_id.id or False,
            'blood_unit': self.blood_unit and self.blood_unit or False,
            'blood_donor_id': self.blood_donor_id and self.blood_donor_id.id or False,
            'blood_group_id': self.blood_group_id and self.blood_group_id.id or False,
        })
        return res


class NuroMedicalLabtestResult(models.Model):
    _inherit = 'nuro.medical.labtest.result'

    SCREENING = [('negative', 'Negative'), ('positive', 'Positive')]

    blood_unit = fields.Float('Blood Unit')
    blood_donor_id = fields.Many2one("blood.donor", string="Donor")
    blood_group_id = fields.Many2one("blood.group", string="Blood Group")
    result_blood_group_id = fields.Many2one("blood.group", string="Blood Group")
    blood_transfusion_id = fields.Many2one('nuro.blood.transfusion')
    blood_request_id = fields.Many2one('blood.request', string="Blood Request")
    donor_request_id = fields.Many2one('donor.request.line', string="Donor Request ID")
    recipient_patient_id = fields.Many2one('nuro.patient', string='Patient')
    recipient_identification_code = fields.Char(string='ID#')
    recipient_gender = fields.Selection(GENDER, string='Gender', store=True)
    recipient_age = fields.Char('Patient Age')
    recipient_mobile = fields.Char('Mobile')
    blood_group = fields.Selection(BLOOD_GROUP)
    negative_a = fields.Char('-A')
    negative_b = fields.Char('-B')
    negative_d = fields.Char('-D')
    a_cell = fields.Char('A Cell')
    b_cell = fields.Char('B Cell')
    donor_blood_group = fields.Selection(BLOOD_GROUP, 'Blood Group')
    antibody_screening = fields.Selection(SCREENING,
                                          string='Antibody Screening')
    antibody_identification_panel = fields.Char('Antibody Identification Panel')
    direct_antiglobulin = fields.Selection(SCREENING, string='Direct Antiglobulin')
    compatibility_test_line = fields.One2many('compatibility.test', 'labtest_id', string='Compatibility Testing')
    final_result = fields.Selection(SCREENING, string='Final Screening Result')
    bt_request_details = fields.Boolean('Request Validation', compute="_compute_bt_request_details")

    def _compute_bt_request_details(self):
        """Compute BT Request Details"""
        for rec in self:
            validation = rec.blood_transfusion_id or rec.blood_donation_id
            if not rec.parent_id and validation:
                rec.bt_request_details = True
            else:
                rec.bt_request_details = False

    def print_blood_transfusion_labtest_report(self):
        """
        receipt printing for the user to get the receipt for the Blood Transfusion Labtest Report
        :return:
        """
        return self.env.ref('nuro_blood_transfusion.action_nuro_bt_afive_labtest_report').report_action(self)

    def print_blood_group_labtest_report(self):
        """
        receipt printing for the user to get the receipt for the Blood Transfusion Labtest Report
        :return:
        """
        return self.env.ref('nuro_blood_transfusion.action_nuro_afive_blood_group_result_report').report_action(self)

    def get_blood_test_report(self):
        """
        Get Blood Test reprot
        :return:
        """
        name = False
        for line in self.labtest_result_lines:
            if name:
                name += ',' + line.test_ranges_id.name
            else:
                name = line.test_ranges_id.name
        return name

    def create_blood_donation(self):
        """
        Create Blood Donation
        :return:
        """
        expiry_date = fields.Date.today() + relativedelta(days=45)
        location_id = self.env['blood.location'].search([('type', '=', 'internal')], limit=1)
        surgery_id = False
        inpatient_id = False
        if self.child_lines[0].lab_entry_id.blood_transfusion_id.surgery_id:
            surgery_id = self.child_lines[0].lab_entry_id.blood_transfusion_id.surgery_id.id
        if self.child_lines[0].lab_entry_id.blood_transfusion_id.inpatient_id:
            inpatient_id = self.child_lines[0].lab_entry_id.blood_transfusion_id.inpatient_id.id
        vals = {
            'date': fields.Date.today(),
            'donor_id': self.blood_donor_id.id,
            'blood_group_id': self.blood_group_id.id,
            'donation_qty': self.blood_unit,
            'blood_location_id': location_id.id,
            'expiry_date': expiry_date,
            'surgery_id': surgery_id,
            'inpatient_id': inpatient_id
        }
        return vals

    def create_blood_group_donation(self):
        """
        Create Blood Group Donation
        :return:
        """
        donation = False
        donation_dict = False
        blood_donation_obj = self.env['blood.donation']
        if self.parent_id and all(line.state == 'completed' for line in self.parent_id.child_lines):
            donation = True
            donation_dict = self.parent_id.create_blood_donation()
        if not self.parent_id and self.state == 'completed':
            donation = True
            donation_dict = self.create_blood_donation()
        if donation and not self.blood_donation_id:
            blood_donation = blood_donation_obj.create(donation_dict)
            return blood_donation
        if self.blood_donation_id:
            self.blood_donation_id.write({
                "state": "screening_completed"
            })
        return False

    def set_all_test_completed(self):
        """
        Super Call Complete All Lab test
        :return:
        """
        res = super(NuroMedicalLabtestResult, self).set_all_test_completed()
        self.create_blood_group_donation()
        for line in self.child_lines:
            if line.result_blood_group_id:
                line.patient_id.blood_group_id = line.result_blood_group_id.id
        return res

    def complete_labtest(self):
        """
        inherit to check if blood group details are filled before complete result
        :return:
        """
        for labtest in self:
            if self.result_blood_group_id:
                self.patient_id.blood_group_id = self.result_blood_group_id.id
            labtest.parent_id.create_blood_group_donation()
            if labtest.blood_group_bool and not labtest.result_blood_group_id:
                raise UserError(_('Please fill lab test cross matching records before complete!'))
        return super(NuroMedicalLabtestResult, self).complete_labtest()


class CompatibilityTest(models.Model):
    _name = 'compatibility.test'
    _description = 'Compatibility Test'

    labtest_id = fields.Many2one('nuro.medical.labtest.result')
    blood_transfusion_id = fields.Many2one('nuro.blood.transfusion', related='labtest_id.blood_transfusion_id',
                                           string='Donor Number', store=True)
    match_result = fields.Char('Xmatch Results')
    match_interpretation = fields.Selection([
        ('incompatible', 'Incompatible'),
        ('compatible', 'Compatible')
    ], string='Xmatch Interpretation')


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    blood_transfusion_id = fields.Many2one('nuro.blood.transfusion', string='Blood Transfusion ID#')
