# -*- coding: utf-8 -*-

from dateutil.relativedelta import relativedelta
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo.exceptions import UserError

from odoo import api, fields, models, _


class BloodDonation(models.Model):
    _inherit = 'blood.donation'

    bt_product_ids = fields.Many2many('product.product', string="Blood Product", domain=[('is_bt', '=', True)])


class BloodRequest(models.Model):
    _inherit = 'blood.request'

    issuance = fields.Selection([('from_donation', 'Donation'), ('from_blood_bank', 'Blood Bank'), ('both', 'Both')],
                                default='from_donation')
    issued_qty = fields.Integer('Issued')
    bt_id = fields.Many2one('nuro.blood.transfusion', string="Blood Transfusion")
    bt_product_ids = fields.Many2many('product.product', string="Blood Product", domain=[('is_bt', '=', True)])
    labtest_master_ids = fields.Many2many('nuro.labtest.master', string="Labtest Master", readonly=True)
    billing_type = fields.Selection([('pre', 'Pre'), ('post', 'Post')], string="BT Billing Type",
                                    related="company_id.billing_type", store=True)
    blood_request_line = fields.One2many('bt.request.line', 'br_id', string="Blood Request")
    priority = fields.Selection([('high', 'High'), ('low', 'Low'), ('medium', 'Medium')], string="Priority")
    cashier_request_invisible = fields.Boolean(compute='cashier_request_invisible_record')
    bd_donor_req = fields.Boolean('BD Requested', compute="_compute_bd_req")
    state = fields.Selection(selection_add=[
        ('send_to_cashier', 'Send To Cashier')
    ])
    bt_master_ids = fields.Many2many('product.product', 'br_product_master_rel', 'br_id',
                                     'product_id', string="BT IDS", compute="compute_medical_charge")
    blood_group_move_ids = fields.Many2many('blood.group.move', 'blood_group_move_request_rel', 'request_id',
                                            'blood_group_move_id', string='Process Move')
    donor_line_add_invisible = fields.Boolean('Donor Line Add Invisible', compute='_compute_donor_line_add_invisible')
    issue_blood_donation_validation = fields.Boolean('Issue Blood Donation Validation',
                                                     compute='_compute_issue_blood_donation_validation')

    def print_bt_doctor_request_form(self):
        """receipt printing for the user to get the receipt for the Blood Transfusion"""
        return self.env.ref('nuro_blood_transfusion.action_blood_request_doctor_request_form').report_action(self)

    def _compute_issue_blood_donation_validation(self):
        for rec in self:
            if rec.env.company.billing_type == 'pre':
                lines = rec.donor_request_lines.filtered(lambda x: x.cross_match_user_id and x.issued_qty == 0)
                if not lines:
                    rec.issue_blood_donation_validation = True
                else:
                    rec.issue_blood_donation_validation = False
            else:
                if rec.state != 'payment_done':
                    rec.issue_blood_donation_validation = True
                else:
                    rec.issue_blood_donation_validation = False

    def _compute_donor_line_add_invisible(self):
        """Compute Donor Line Add Invisible"""
        for rec in self:
            if rec.env.company.billing_type == 'pre':
                if not rec.bt_id or (
                        rec.bt_id and rec.bt_id.state != 'send_to_lab') or rec.state != 'draft' or rec.qty_donor_number == 0:
                    rec.donor_line_add_invisible = True
                else:
                    rec.donor_line_add_invisible = False
            else:
                if (
                        rec.bt_id and rec.bt_id.state != 'send_to_lab') or rec.state != 'draft' or rec.qty_donor_number == 0:
                    rec.donor_line_add_invisible = True
                else:
                    rec.donor_line_add_invisible = False

    def create_donor_line_addition_wizard(self):
        """Create Donor Line Addition Wizard"""
        if self.qty_donor_number == 0:
            raise UserError(_('All Unit Has been requested can not add more donor.!!!'))
        if self.donor_line_add_invisible:
            raise UserError(_('Can not add More donor as all condition is not setisfy.!!!'))
        return super().create_donor_line_addition_wizard()

    @api.depends('blood_request_line')
    def compute_medical_charge(self):
        """Compute Package and Total Amount"""
        for rec in self:
            rec.bt_master_ids = [(6, 0, rec.blood_request_line.mapped('bt_product_id').ids)]

    @api.onchange('blood_request_line')
    def onchange_blood_request_lines(self):
        """Onchange Blood Request Lines"""
        if self.blood_request_line:
            self.qty = sum(self.blood_request_line.mapped('blood_unit'))
        else:
            self.qty = 0

    @api.onchange('issuance')
    def onchange_issuance_record(self):
        """Onchange Issuance Record"""
        if self.issuance and self.issuance != 'from_donation':
            current_date = fields.Date.context_today(self)
            move_line = self.env['blood.group.move'].search([
                ('state', '!=', 'reserved'),
                ('expiry_date', '>=', current_date),
                ('blood_group_id', '=', self.blood_group_id.id),
                ('available_qty', '>', 0)
            ], order='expiry_date ASC')
            requested_qty = self.qty - self.issued_qty
            # if not move_line or move_line and len(move_line) < requested_qty:
            if not move_line:
                message = 'There is no available blood in the bank for this blood type.!!!'
                self.issuance = 'from_donation'
                return {'warning': {'title': 'Move Line Error', "message": message}}

    def blood_bank_request_further_wizards(self):
        """Blood Bank Request Further Wizard"""
        current_date = fields.Date.context_today(self)
        move_line = self.env['blood.group.move'].search([
            ('state', '!=', 'reserved'),
            ('expiry_date', '>=', current_date),
            ('blood_group_id', '=', self.blood_group_id.id),
            ('available_qty', '>', 0)
        ], order='expiry_date ASC')
        if not move_line:
            raise UserError(_('There is no available blood in the bank for this blood type.!!!'))
        action = self.env.ref('nuro_blood_transfusion.action_blood_request_view').read()[0]
        requested_qty = self.qty - self.issued_qty
        action['context'] = {'default_br_id': self.id, 'default_max_length': requested_qty}
        return action

    def process_blood_request_further(self):
        """Process Further Blood Transfusion"""
        if not self.blood_group_id:
            raise UserError(_('Please Add Blood Group Before Processing.!!!'))
        if not self.priority:
            raise UserError(_('Please Add Priority Before Processing.!!!'))
        if self.env.company.billing_type == 'pre':
            self.state = 'in_process'
        if self.env.company.billing_type == 'post':
            self.create_bt_details()

    def issue_blood_details(self):
        """Issue Blood Request"""
        # move_ids = self.donor_request_lines.mapped('blood_move_id')
        if self.issue_blood_donation_validation:
            raise UserError(_('You Can not issue blood Right Now.!!!'))
        line_list = []
        total_donor_qty = sum(
            self.donor_request_lines.filtered(lambda x: x.blood_donation_id and x.blood_move_in_ids).mapped(
                'blood_move_in_ids').mapped('quantity'))
        requested_qty = self.qty - self.issued_qty
        if requested_qty <= 0:
            raise UserError(_('All Blood Has been requested Issued Already'))
        move_line = self.blood_group_move_ids
        if self.issuance in ('from_donation', 'both'):
            for line in self.donor_request_lines.filtered(lambda x: x.blood_donation_id and x.blood_move_in_ids):
                for mv in line.blood_move_in_ids.filtered(lambda x: x.available_qty > 0.0):
                    line_list.append((0, 0, {
                        'name': mv.name,
                        'type': mv.type,
                        'donor_line_id': line.id,
                        'in_move_id': mv.id,
                        'quantity': mv.available_qty,
                        'issue_qty': 0.0,
                        'state': mv.state,
                        'expiry_date': mv.expiry_date,
                    }))
        if self.issuance in ('from_blood_bank', 'both'):
            if not move_line:
                return self.blood_bank_request_further_wizards()
            for line in move_line:
                line_list.append((0, 0, {
                    'name': line.name,
                    'type': line.type,
                    'donor_line_id': False,
                    'in_move_id': line.id,
                    'quantity': line.available_qty,
                    'issue_qty': 0.0,
                    'state': line.state,
                    'expiry_date': line.expiry_date,
                }))
        donor_lines = self.donor_request_lines
        compatible_line = donor_lines.filtered(lambda x: x.cross_match_status == 'compatible')
        if self.other_hospital:
            compatible_line = donor_lines.filtered(
                lambda x: x.cross_match_status == 'compatible' or x.cross_match == 'not_done')
        action = self.env.ref('nuro_blood_transfusion.action_issue_blood_wizard_form').read()[0]
        action['context'] = {
            'default_br_id': self.id,
            'default_bt_id': self.bt_id.id,
            'default_blood_group_id': self.blood_group_id.id,
            'default_donor_ids': [(6, 0, compatible_line.mapped('donor_id').ids)],
        }
        return action

    def _compute_bd_req(self):
        """Compute BD Request"""
        for rec in self:
            if all(ln.bt_line_id for ln in rec.blood_request_line):
                rec.bd_donor_req = False
            else:
                rec.bd_donor_req = True

    def cashier_request_invisible_record(self):
        """Labtest Invisible Record"""
        for rec in self:
            rec.cashier_request_invisible = True
            if self.env.company.billing_type == 'pre' and rec.state == 'draft' and not self.bt_id and self.bd_donor_req:
                rec.cashier_request_invisible = False
            any_requested = any(line.blood_unit != line.unit_billed for line in self.blood_request_line)
            donor_lines = self.donor_request_lines
            incompatible_line = donor_lines.filtered(lambda x: x.cross_match_status == 'compatible')
            if self.other_hospital:
                incompatible_line = donor_lines.filtered(
                    lambda x: x.cross_match_status == 'compatible' or x.cross_match == 'not_done')
            qty = self.qty - len(incompatible_line)
            if any_requested and self.env.company.billing_type == 'post' and qty == 0.0 and not self.bt_id and self.bd_donor_req:
                rec.cashier_request_invisible = False

    @api.onchange('bt_product_ids')
    def onchange_bt_product_ids(self):
        """Onchange Blood Product IDS"""
        if self.bt_product_ids:
            labtest_ids = set(self.bt_product_ids.mapped('labtest_line').mapped('labtest_master_id').ids)
            self.labtest_master_ids = [(6, 0, labtest_ids)]

    def create_bt_vals(self):
        """Create Blood Transfusion"""
        out = False
        if not self.doctor_id:
            out = True
        vals = {
            'patient_id': self.patient_id.id,
            'identification_code': self.identification_code,
            'gender': self.gender,
            'age': self.age,
            'mobile': self.mobile,
            'doctor_id': self.doctor_id and self.doctor_id.id or False,
            'out': out,
            'blood_request_id': self.id,
            'blood_group_id': self.blood_group_id.id,
            'priority': self.priority,
            'blood_unit': self.qty,
            'bt_product_ids': [(6, 0, self.blood_request_line.mapped('bt_product_id').ids)],
            'donor_number': len(self.blood_request_line)
        }
        return vals

    def create_blood_donation_request_vals(self, line):
        """Create Blood Donation Request Vals"""
        vals = {
            'patient_id': self.patient_id.id,
            'identification_code': self.identification_code,
            'gender': self.gender,
            'age': self.age,
            'mobile': self.mobile,
            'doctor_id': self.doctor_id.id,
            'date': fields.Date.context_today(self),
            'donor_id': line.donor_id.id,
            'blood_group_id': line.blood_group_id.id,
            'donation_qty': line.qty,
            'expiry_date': fields.Date.context_today(self) + relativedelta(days=21),
        }
        return vals

    def action_confirm(self):
        """Action Confirm Super Call"""
        res = super(BloodRequest, self).action_confirm()
        if self.billing_type == 'post':
            if not self.bt_id and self.patient_id:
                raise UserError(_('Please create Blood Transfusion Request first before confirmation.!!!'))
            elif self.bt_id and self.bt_id.state != 'send_to_lab':
                raise UserError(_('Blood Transfusion Request Has not been processed Yet'))
            elif not self.donor_request_lines and self.patient_id:
                raise UserError(_('There is no blood Donation Line'))
            else:
                for rec in self.donor_request_lines:
                    donation_vals = self.create_blood_donation_request_vals(line=rec)
                    donation_obj = self.env['blood.donation']
                    donation_id = donation_obj.create(donation_vals)
                    donation_id.action_confirm()
                    donation_id.action_done()
                self.action_done()
        return res

    # def create_bt_details(self):
    #     """
    #     Create BT Details
    #     """
    # if not self.blood_group_id:
    #     raise UserError(_('Please Add Blood Group Before Processing.!!!'))
    # if not self.priority:
    #     raise UserError(_('Please Add Priority Before Processing.!!!'))
    # bt_obj = self.env['nuro.blood.transfusion']
    # vals = self.create_bt_vals()
    # bt_id = bt_obj.create(vals)
    # if bt_id:
    #     donor_line = self.donor_request_lines.filtered(lambda x: x.assessment_result == 'fit')
    #     if donor_line:
    #         donor_line.write({
    #             'bt_id': bt_id.id
    #         })
    #     br_line = self.blood_request_line
    #     if not br_line:
    #         raise UserError(_('There are not Blood Product.!!!'))
    #     br_line.write({
    #         'bt_id': bt_id.id
    #     })
    #     self.bt_id = bt_id.id
    #     bt_id.onchange_bt_product_id()
    #     self.state = 'send_to_cashier'

    def create_bt_details(self):
        """Create Blood Request test"""
        if self.cashier_request_invisible:
            raise UserError(_('You Can not Send to Cashier Right now.!!!'))
        blood_request_line = self.blood_request_line.filtered(
            lambda x: x.blood_unit != x.unit_billed and not x.bt_line_id)
        line_list = []
        for line in blood_request_line:
            vals = {
                'bt_request_id': line.id,
                'bt_product_id': line.bt_product_id.id,
                'priority': line.priority,
                'blood_group_id': line.blood_group_id.id,
                'blood_unit': line.blood_unit - line.unit_billed,
                'unit_billed': line.unit_billed,
            }
            line_list.append((0, 0, vals))
        if line_list:
            ctx = {
                'default_br_id': self.id,
                'default_blood_request_line': line_list,
            }
            action = self.env.ref('nuro_blood_transfusion.action_blood_transfusion_cr_wizard').read()[0]
            action['context'] = ctx
            return action
        else:
            raise UserError(_('There is not line to send to cashier.!!!'))


class BTRequestLine(models.Model):
    _name = 'bt.request.line'
    _description = 'Blood Transfusion Request Line'
    _order = 'id ASC'

    bt_product_id = fields.Many2one('product.product', string="BT Type")
    priority = fields.Selection([('high', 'High'), ('low', 'Low'), ('medium', 'Medium')], string="Priority")
    blood_group_id = fields.Many2one("blood.group", string="Blood Type")
    blood_unit = fields.Float('Unit')
    unit_billed = fields.Float('Unit Billed')
    br_id = fields.Many2one('blood.request', string="Blood Request")
    bt_id = fields.Many2one('nuro.blood.transfusion', string="Blood Transfusion")
    bt_line_id = fields.Many2one('nuro.blood.transfusion', string="Blood Transfusion")
    unit_price = fields.Float('Unit Price', related="bt_product_id.package_charges", store=True)
    total_amount = fields.Float('Total Price', compute="get_total_amount", store=True)
    state = fields.Selection([('draft', 'Draft'), ('requested', 'Requested')], string='State', default="draft")
    br_state = fields.Selection(related="br_id.state", string="State", store=True)

    @api.onchange('bt_product_id')
    def onchange_bt_product_details(self):
        """Onchange BT Product ID"""
        if self.bt_product_id and not self.blood_group_id:
            message = "Please sent blood grouping test for the patient.!!!"
            self.bt_product_id = False
            return {'warning': {'title': 'Blood Group Error', "message": message}}

    @api.constrains('blood_unit')
    def constrains_blood_unit_res(self):
        """
        Constrains Blood Unit
        """
        for rec in self:
            if rec.blood_unit <= 0.0:
                raise UserError(_('Please add the blood unit for the request.!!!'))

    def unlink(self):
        """
        Def Unlink
        """
        for rec in self:
            if rec.bt_line_id:
                raise UserError(_('Record has been processed already.!!!'))
            return super(BTRequestLine, rec).unlink()

    @api.depends('bt_product_id', 'blood_unit')
    def get_total_amount(self):
        """
        Get Total Amount From
        """
        for rec in self:
            rec.total_amount = rec.unit_price * rec.blood_unit


class DonorRequestLine(models.Model):
    _inherit = 'donor.request.line'

    bt_id = fields.Many2one('nuro.blood.transfusion', string="Blood Transfusion")
    bt_line_id = fields.Many2one('nuro.blood.transfusion', string="Blood Transfusion")

    def update_blood_request_status(self):
        """Update Blood Request Status"""
        if self.env.company.billing_type == 'pre':
            line = self.br_id.donor_request_lines.filtered(lambda x: x.cross_match_status == 'compatible')
            if self.br_id.other_hospital:
                line = self.br_id.donor_request_lines.filtered(
                    lambda x: x.cross_match_status == 'compatible' or x.cross_match == 'not_done')
            if len(line) == self.br_id.qty:
                self.br_id.state = 'in_process'
        return super(DonorRequestLine, self).update_blood_request_status()

    def donation_receiving_method(self):
        """
        Donation Receiving Method
        """
        for rec in self:
            br_id = rec.br_id
            blood_donation_id = rec.blood_donation_id
            blood_move_ids = rec.blood_move_in_ids
            assessment_result = rec.assessment_result
            if (blood_donation_id and blood_move_ids) or br_id.state not in (
                    'lab_requested', 'in_process') or assessment_result != 'fit':
                rec.donation_receiving = True
            else:
                rec.donation_receiving = False


class BloodGroupMove(models.Model):
    _inherit = 'blood.group.move'

    in_bt_id = fields.Many2one('nuro.blood.transfusion', string='Blood Transfusion')
    out_bt_id = fields.Many2one('nuro.blood.transfusion', string='Blood Transfusion')
