# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, tools, _


class AccountReportFilter(models.AbstractModel):
    _inherit = 'account.report'

    filter_partner_department = None

    # @api.model
    # def _init_filter_department(self, options, previous_options=None):
    #     if not self.filter_partner_department:
    #         return
    #
    #     options['department'] = True
    #     options['department_ids'] = previous_options and previous_options.get('department_ids') or []
    #     selected_department_ids = [int(account) for account in options['department_ids']]
    #     selected_departments = selected_department_ids and self.env['partner.responsible.dept'].browse(selected_department_ids) or self.env['partner.responsible.dept']
    #     options['selected_department_ids'] = selected_departments.mapped('name')

    @api.model
    def _init_filter_chart_of_account(self, options, previous_options=None):
        if not self.filter_chart_of_account:
            return

        options['accounts'] = True
        options['department'] = True
        options['account_ids'] = previous_options and previous_options.get('account_ids') or []
        selected_account_ids = [int(account) for account in options['account_ids']]
        selected_accounts = selected_account_ids and self.env['account.account'].browse(selected_account_ids) or \
                            self.env['account.account']
        options['selected_account_ids'] = selected_accounts.mapped('name')

        # if not self.filter_partner_department:
        #     return

        options['department'] = True
        options['department_ids'] = previous_options and previous_options.get('department_ids') or []
        selected_department_ids = [int(account) for account in options['department_ids']]
        selected_departments = selected_department_ids and self.env['partner.responsible.dept'].browse(selected_department_ids) or self.env['partner.responsible.dept']
        options['selected_department_ids'] = selected_departments.mapped('name')

    @api.model
    def _get_options_chart_of_account_domain(self, options):
        domain = []
        if options.get('account_ids'):
            account_ids = [int(account) for account in options['account_ids']]
            domain.append(('account_id', 'in', account_ids))
        return domain

    @api.model
    def _get_options_partner_department(self, options):
        domain = []
        if options.get('department_ids'):
            department_ids = [int(dept) for dept in options['department_ids']]
            domain.append(('responsible_dept_id', 'in', department_ids))
        return domain

    ####################################################
    # OPTIONS: CORE
    ####################################################

    @api.model
    def _get_options_domain(self, options):
        domain = super(AccountReportFilter, self)._get_options_domain(options)
        domain += self._get_options_partner_department(options)
        domain += self._get_options_chart_of_account_domain(options)
        return domain

    ####################################################
    # MISC
    ####################################################

    def get_report_informations(self, options):
        info = super(AccountReportFilter, self).get_report_informations(options)
        if info["options"].get("accounts"):
            info["options"]['selected_account_ids'] = [self.env['account.account'].browse(int(account)).name for account in info["options"]['account_ids']]
        if info["options"].get("department"):
            info["options"]['selected_department_ids'] = [self.env['partner.responsible.dept'].browse(int(dept)).name for dept in info["options"]['department_ids']]
        return info