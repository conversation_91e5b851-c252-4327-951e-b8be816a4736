from odoo.exceptions import UserError

from odoo import models, fields, api, _


class NuroOpCreditWizard(models.TransientModel):
    _inherit = 'nuro.op.credit.wizard'

    @api.model
    def default_get(self, fields):
        """
        Default Get for Imaging Credit Wizard
        :param fields:
        :return:
        """
        res = super(NuroOpCreditWizard, self).default_get(fields)
        op_entry_id = self.env['nuro.op.entry'].browse(self.env.context.get('active_id'))
        request_approval = self.env['discount.approve.obj'].search([
            ('model_id', '=', op_entry_id._name),
            ('object_id', '=', op_entry_id.id),
            ('state', '=', 'approved')
        ])
        if request_approval:
            res['discount_request_id'] = request_approval[0].id
            res['approved_discount'] = True
        return res

    discount_request_id = fields.Many2one('discount.approve.obj')
    approved_discount = fields.Boolean('Discount Approved')
    request_discount = fields.Boolean('Request Discount')
    requested_to_id = fields.Many2one('res.users', domain=lambda self: [
        ('groups_id', 'in', self.env.ref('nuro_discount_approve.discount_request_approve').id)])

    @api.onchange('total_amount')
    def onchange_total_amount_field(self):
        """
        Onchange Amount Value
        :return:
        """
        request_approval = self.env['discount.approve.obj'].search([
            ('model_id', '=', self.op_entry_id._name),
            ('object_id', '=', self.op_entry_id.id),
            ('state', '=', 'approved')
        ])
        if request_approval and self.total_amount > 0.0:
            self.discount_amount = self.total_amount / 100 * request_approval[0].discount_requested

    def create_discount_request(self):
        """
        Create
        :return:
        """
        if self.op_entry_id.state not in ('draft', 'request', 'partial_paid'):
            raise UserError(_('Record Has been Processed Already'))
        information = """<table><br></table><table class="table table-bordered"><tbody>"""
        for line in self.credit_payment_line:
            panel = 'OP Procedure'
            name = line.op_master_id.name
            amount = line.op_charges
            information = information + """<tr><td>%s</td><td>%s</td><td>%s</td></tr>""" % (panel, name, amount)
        information = information + """</tbody></table>"""
        if self.request_discount:
            discount_request_obj = self.env['discount.approve.obj']
            available_request = discount_request_obj.search([
                ('name', '=', self.op_entry_id.name),
                ('state', 'in', ('draft', 'request', 'approved'))
            ])
            if not available_request:
                request = discount_request_obj.create_discount_request(
                    name=self.op_entry_id.name, model_id=self.op_entry_id._name,
                    object_id=self.op_entry_id.id, amount_discount=self.discount_amount,
                    patient_id=self.op_entry_id.patient_id.id, requested_by_id=self.env.user.id,
                    requested_to_id=self.requested_to_id.id, amount=self.total_amount,
                    requested_by_allowed_discount=self.env.user.discount, information=information
                )
                request.message_post_with_view('mail.message_origin_link',
                                               values={'self': request, 'origin': self.op_entry_id},
                                               subtype_id=self.env.ref('mail.mt_note').id)
            else:
                raise UserError(_("Already Requested You Can not Create request Twice.!!!"))

    def action_op_credit(self):
        """
        OP Procedure Super Call
        :return:
        """
        forced_pay = False
        if self.approved_discount:
            forced_pay = True
        self.env.user._check_discount_access(self.total_amount, self.discount_amount, forced_pay=forced_pay)
        if self.discount_request_id:
            self.discount_request_id.state = 'used'
        return super(NuroOpCreditWizard, self).action_op_credit()


class NuroOpCashWizard(models.TransientModel):
    _inherit = 'nuro.op.cash.wizard'

    @api.model
    def default_get(self, fields):
        """
        Default Get for Imaging Credit Wizard
        :param fields:
        :return:
        """
        res = super(NuroOpCashWizard, self).default_get(fields)
        op_entry_id = self.env['nuro.op.entry'].browse(self.env.context.get('active_id'))
        request_approval = self.env['discount.approve.obj'].search([
            ('model_id', '=', op_entry_id._name),
            ('object_id', '=', op_entry_id.id),
            ('state', '=', 'approved')
        ])
        if request_approval:
            res['discount_request_id'] = request_approval[0].id
            res['approved_discount'] = True
        return res

    discount_request_id = fields.Many2one('discount.approve.obj')
    approved_discount = fields.Boolean('Discount Approved')
    request_discount = fields.Boolean('Request Discount')
    requested_to_id = fields.Many2one('res.users', domain=lambda self: [
        ('groups_id', 'in', self.env.ref('nuro_discount_approve.discount_request_approve').id)])

    @api.onchange('total_amount')
    def onchange_total_amount_field(self):
        """
        Onchange Amount Value
        :return:
        """
        request_approval = self.env['discount.approve.obj'].search([
            ('model_id', '=', self.op_entry_id._name),
            ('object_id', '=', self.op_entry_id.id),
            ('state', '=', 'approved')
        ])
        if request_approval and self.total_amount > 0.0:
            self.discount_amount = self.total_amount / 100 * request_approval[0].discount_requested

    def create_discount_request(self):
        """
        Create
        :return:
        """
        if self.op_entry_id.state not in ('draft', 'request', 'partial_paid'):
            raise UserError(_('Record Has been Processed Already'))
        information = """<table><br></table><table class="table table-bordered"><tbody>"""
        for line in self.cash_payment_line:
            panel = 'OP Procedure'
            name = line.op_master_id.name
            amount = line.op_charges
            information = information + """<tr><td>%s</td><td>%s</td><td>%s</td></tr>""" % (panel, name, amount)
        information = information + """</tbody></table>"""
        if self.request_discount:
            discount_request_obj = self.env['discount.approve.obj']
            available_request = discount_request_obj.search([
                ('name', '=', self.op_entry_id.name),
                ('state', 'in', ('draft', 'request', 'approved'))
            ])
            if not available_request:
                request = discount_request_obj.create_discount_request(
                    name=self.op_entry_id.name, model_id=self.op_entry_id._name,
                    object_id=self.op_entry_id.id, amount_discount=self.discount_amount,
                    patient_id=self.op_entry_id.patient_id.id, requested_by_id=self.env.user.id,
                    requested_to_id=self.requested_to_id.id, amount=self.total_amount,
                    requested_by_allowed_discount=self.env.user.discount, information=information
                )
                request.message_post_with_view('mail.message_origin_link',
                                               values={'self': request, 'origin': self.op_entry_id},
                                               subtype_id=self.env.ref('mail.mt_note').id)
            else:
                raise UserError(_("Already Requested You Can not Create request Twice.!!!"))

    def create_cash_payment_entry(self):
        """
        OP Procedure Super Call
        :return:
        """
        forced_pay = False
        if self.approved_discount:
            forced_pay = True
        self.env.user._check_discount_access(self.total_amount, self.discount_amount, forced_pay=forced_pay)
        if self.discount_request_id:
            self.discount_request_id.state = 'used'
        return super(NuroOpCashWizard, self).create_cash_payment_entry()
