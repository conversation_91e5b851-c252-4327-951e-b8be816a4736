from odoo import api, fields, models


class BusinessUnit(models.Model):
    _inherit = 'business.unit'

    include_all_os = fields.<PERSON><PERSON><PERSON>('Include All Other Service')
    os_line_ids = fields.One2many(
        'business.unit.line',
        'business_unit_id',
        string='Business Unit Lines', domain=[('panel', '=', 'os')],
        help="Lines containing masters and branches for this business unit"
    )
    os_hms_ids = fields.Many2many("hms.master", "bu_os_master_rel", "bu_id", "master_id", string="Request IDS",
                                  compute="compute_os_master_record", )

    @api.depends("os_line_ids")
    def compute_os_master_record(self):
        """compute OS Master View"""
        self.ensure_one()
        master_ids = self.os_line_ids.mapped('master_id').ids
        self.os_hms_ids = [(6, 0, master_ids)]

    @api.onchange('include_all_os')
    def _onchange_include_all_os(self):
        """Remove os_line_ids when include_all_os is toggled (True or False)"""
        if self.os_line_ids:
            self.os_line_ids = [(5, 0, 0)]  # Clear all os_line_ids

    def write(self, vals):
        """Override write method to clear os_line_ids when include_all_os changes"""
        if 'include_all_os' in vals:
            # Find records that have os_line_ids
            for record in self:
                if record.os_line_ids:
                    # Clear os_line_ids for this record when write is called
                    vals.update({'os_line_ids': [(5, 0, 0)]})
                    break
        return super(BusinessUnit, self).write(vals)

class BusinessUnitLine(models.Model):
    _inherit = 'business.unit.line'

    panel = fields.Selection(selection_add=[('os', 'Other Service')])