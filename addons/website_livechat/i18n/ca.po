# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_livechat
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <mpal<PERSON>@tda.ad>, 2020
# <PERSON><PERSON><PERSON>, 2020
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2022
# Farou<PERSON>ri, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: Farouk Jabiri, 2022\n"
"Language-Team: Catalan (https://www.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__session_count
msgid "# Sessions"
msgstr "# Sessions"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "<span class=\"fa fa-comments mr-2\"/>Speaking With"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Live Chat</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Xat en viu</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<span>Livechat Channel</span>"
msgstr "<span>Canal de Livechat</span>"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "Available"
msgstr "Reservat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Bad"
msgstr "Dolent"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_search
msgid "Busy"
msgstr "Ocupat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Channel"
msgstr "Canal"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Chat"
msgstr "Conversa"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_res_config_settings
msgid "Config Settings"
msgstr "Configuració"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_description
msgid "Description of the channel displayed on the website page"
msgstr "Descripció del canal que es mostra a la pàgina web"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr "Canal de debat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Great"
msgstr "Gran"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutament HTTP"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Happy face"
msgstr "Cara feliç"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/xml/thread.xml:0
#, python-format
msgid "History"
msgstr "Historial"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_mail_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "Està en marxa livechat?"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/xml/thread.xml:0
#, python-format
msgid "Lang"
msgstr "Lang"

#. module: website_livechat
#: model:website.menu,name:website_livechat.menu_livechat
msgid "Live Support"
msgstr "Suport autònom"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Live chat channel of your website"
msgstr "Canal de xat en directe del vostre lloc web"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_im_livechat_channel
msgid "Livechat Channel"
msgstr "Canal de Livechat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "Livechat Support Channels"
msgstr "Canals de suport per a Livechat"

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_mail_channel__livechat_active
msgid ""
"Livechat session is not considered as active if the visitor left the "
"conversation."
msgstr ""

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:0
#, python-format
msgid "Name"
msgstr "Nom"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Neutral face"
msgstr "Cara neutral"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:0
#, python-format
msgid "New Channel"
msgstr "Nou canal"

#. module: website_livechat
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid "No Livechat Channel allows you to send a chat request for website %s."
msgstr ""
"No hi ha cap canal de Livechat que us permeti enviar una sol·licitud de xat "
"per al lloc web %s."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Not rated yet"
msgstr "Encara no valorat"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/xml/thread.xml:0
#, python-format
msgid "Offline"
msgstr "Fora de línia"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Okay"
msgstr "Val"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/xml/thread.xml:0
#, python-format
msgid "Online"
msgstr "En línia"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_name
msgid "Operator Name"
msgstr "Nom de l'operador"

#. module: website_livechat
#: code:addons/website_livechat/models/website_visitor.py:0
#, python-format
msgid ""
"Recipients are not available. Please refresh the page to get latest visitors"
" status."
msgstr ""
"Els destinataris no estan disponibles. Actualitzeu la pàgina per obtenir "
"l'estat dels últims visitants."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Sad face"
msgstr "Cara trista"

#. module: website_livechat
#: model:ir.actions.server,name:website_livechat.website_livechat_send_chat_request_action_server
msgid "Send Chat Requests"
msgstr "Envia les sol·licituds de xat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_tree
msgid "Send chat request"
msgstr "Envia la petició de xat"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_form
msgid "Sessions"
msgstr "Sessions "

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.website_visitor_view_kanban
msgid "Speaking With"
msgstr "Parlant amb"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__livechat_operator_id
msgid "Speaking with"
msgstr "Parlant amb"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Statistics"
msgstr "Estadístiques"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The"
msgstr "El"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The Team"
msgstr "L'equip"

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "The visitor"
msgstr "El visitant"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "There are no public livechat channels to show."
msgstr "No hi ha canals de xat públic a mostrar."

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "There are no ratings for this channel for now."
msgstr "No hi ha valoracions per a aquest canal de moment."

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_mail_channel__livechat_visitor_id
msgid "Visitor"
msgstr "Visitant"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/xml/thread.xml:0
#, python-format
msgid "Visitor is connected"
msgstr ""

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/xml/thread.xml:0
#, python-format
msgid "Visitor is offline"
msgstr ""

#. module: website_livechat
#: model:ir.actions.act_window,name:website_livechat.website_visitor_livechat_session_action
msgid "Visitor's Sessions"
msgstr "Sessions del visitant"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website_visitor__mail_channel_ids
msgid "Visitor's livechat channels"
msgstr "Canals livechat dels visitants"

#. module: website_livechat
#: model:ir.ui.menu,name:website_livechat.website_livechat_visitor_menu
msgid "Visitors"
msgstr "Visitants"

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/xml/thread.xml:0
#: model:ir.model,name:website_livechat.model_website
#, python-format
msgid "Website"
msgstr "Lloc web"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_res_config_settings__channel_id
msgid "Website Live Channel"
msgstr "Canal autònom del lloc web"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website__channel_id
msgid "Website Live Chat Channel"
msgstr "Canal de xat del lloc web"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_website_visitor
msgid "Website Visitor"
msgstr "Visitant del lloc web"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_description
msgid "Website description"
msgstr "Descripció del lloc web"

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid "has left the conversation."
msgstr ""

#. module: website_livechat
#: code:addons/website_livechat/models/mail_channel.py:0
#, python-format
msgid ""
"has started a conversation with %s. The chat request has been canceled."
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "last feedbacks"
msgstr "darrers comentaris"
