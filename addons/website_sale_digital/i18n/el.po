# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_digital
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <george_tarasid<PERSON>@yahoo.com>, 2019
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-27 16:34+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid "Add attachments for this digital product"
msgstr ""

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_ir_attachment
msgid "Attachment"
msgstr "Συνημμένο"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid "Digital Attachments"
msgstr "Ψηφιακά Συνημμένα"

#. module: website_sale_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.product_product_view_form_inherit_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.product_template_view_form_inherit_digital
msgid "Digital Files"
msgstr ""

#. module: website_sale_digital
#: model:ir.model.fields,field_description:website_sale_digital.field_ir_attachment__product_downloadable
msgid "Downloadable from product portal"
msgstr "Μεταφορτωμένο από την πύλη ειδών"

#. module: website_sale_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.sale_order_portal_content_inherit_website_sale_digital
msgid "Downloads"
msgstr ""

#. module: website_sale_digital
#: model:ir.model.fields,field_description:website_sale_digital.field_product_product__attachment_count
#: model:ir.model.fields,field_description:website_sale_digital.field_product_template__attachment_count
msgid "File"
msgstr "Αρχείο"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_account_move_line
msgid "Journal Item"
msgstr "Στοιχείο Ημερολογίου"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_product_product
msgid "Product"
msgstr "Είδος"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_product_template
msgid "Product Template"
msgstr "Πρότυπο Είδους "

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/ir_attachment.py:0
#, python-format
msgid "Sorry, you are not allowed to access this document."
msgstr "Συγνώμη , δεν επιτρέπεται η πρόσβαση σε αυτό το έγγραφο"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid ""
"The attached files are the ones that will be purchased and sent to the "
"customer."
msgstr ""

#. module: website_sale_digital
#: model:product.template,uom_name:website_sale_digital.product_1
msgid "Units"
msgstr ""

#. module: website_sale_digital
#: model:product.template,name:website_sale_digital.product_1
msgid "eBook: Office Renovation for Dummies"
msgstr ""
