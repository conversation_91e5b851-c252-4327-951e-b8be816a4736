# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://www.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
msgid "<span class=\"o_vat_label\">VAT</span>"
msgstr "<span class=\"o_vat_label\">IVA</span>"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Configuració"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, you will not be able to save a contact if its "
"VAT number cannot be verified by the European VIES service."
msgstr ""
"Si aquesta casella de selecció està marcada, no podrà salvar un contacte si "
"el seu número d'IVA no pot ser verificat pel servei de vies europees."

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:188
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] does not seem to be valid. \n"
"Note: the expected format is %s"
msgstr ""
"El nombre [%s] d'IVA per al soci  [%s] no sembla ser vàlid.\n"
"Nota: el format esperat és %s"

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:187
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] either failed the VIES VAT validation "
"check or did not respect the expected format %s."
msgstr ""
"El nombre [%s] d'IVA per a l'empresa [%s] o ha fallat la validació d'IVA "
"VIES o no respecta el format esperat %s."

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.company_form_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_short_form
msgid "VAT"
msgstr "CIF/NIF"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Verifica els números d'IVA"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Verifica els números de l'IVA utilitzant el servei European VIES"
