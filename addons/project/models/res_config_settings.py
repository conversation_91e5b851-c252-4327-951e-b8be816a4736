# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    module_project_forecast = fields.<PERSON><PERSON>an(string="Forecasts")
    module_hr_timesheet = fields.<PERSON>olean(string="Task Logs")
    group_subtask_project = fields.<PERSON><PERSON>an("Sub-tasks", implied_group="project.group_subtask_project")
    group_project_rating = fields.<PERSON><PERSON>an("Use Rating on Project", implied_group='project.group_project_rating')
