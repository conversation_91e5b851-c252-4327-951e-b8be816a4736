# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_form
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <baskhuu<PERSON><EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# Khishig<PERSON>d <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:13+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: tserendavaa tsogtoo <<EMAIL>>, 2020\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "&amp;times;"
msgstr "&amp;цаг;"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' нь зөв биш огноо"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' нь зөв биш огноо, цаг"

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr "<p>Хавсаргасан файлууд : </p>"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Add a custom field"
msgstr "Өөриймшүүлсэн талбар нэмэх"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Add an existing field"
msgstr ""

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "Маягтууд дээр ашиглах боломжтой"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "Алдаа гарсан, маягтыг илгээгээгүй."

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "Энэ талбарыг вэб маягтууд дээр хар жагсаалтад оруулах"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "Вэб маягтууд дээр хар жагсаалтад орсон"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Cancel"
msgstr "Цуцлах"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Change Form Parameters"
msgstr "Маягтын параметруудыг өөрчлөх"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Checkbox"
msgstr "Чекбокс"

#. module: website_form
#: model:ir.model,name:website_form.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Date"
msgstr "Огноо"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Date &amp; Time"
msgstr "Огноо &amp; Цаг"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Decimal Number"
msgstr "Аравтын тоо"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Email"
msgstr "Имэйл"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
msgid "Email To"
msgstr "И-мэйл хаяг"

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "Энэ загвар дээр маягт бүтээгчийг зөвшөөрөх."

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "Error"
msgstr "Алдаа"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "Захиалгат маягтын өгөгдлийн талбар"

#. module: website_form
#: model:ir.model,name:website_form.model_ir_model_fields
msgid "Fields"
msgstr "Талбарууд"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "File Upload"
msgstr "Файл ачааллах"

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""
"Маягтын үйлдлийн шошго. Ж: crm.lead нь 'Имэйл илгээх', project.issue нь "
"'Асуудал үүсгэх' байж болно."

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Hidden"
msgstr "Нуугдсан"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "Маягтын үйлдлийн шошго"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Long Text"
msgstr "Урт текст"

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "Metadata"
msgstr "Тэмдэг өгөгдөл"

#. module: website_form
#: model:ir.model,name:website_form.model_ir_model
msgid "Models"
msgstr "Модел"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Multiple Checkboxes"
msgstr "Олон чекбоксууд"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr "Таарах бичлэг алга !"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Number"
msgstr "Дугаар"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Option 1"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Option 2"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Option 3"
msgstr ""

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "Other Information:"
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Phone Number"
msgstr "Утасны дугаар"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "Маягтыг зөв бөглөнө үү"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Radio Buttons"
msgstr "Радио Товчнууд"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Required"
msgstr "Шаардлагатай"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "Save"
msgstr "Хадгалах"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Selection"
msgstr "Сонголт"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#: model_terms:ir.ui.view,arch_db:website_form.s_website_form
msgid "Send"
msgstr "Илгээх"

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr ""
"Тэмдэг болон захиалгат маягтын талбаруудын өгөгдлийг хадгалах талбарыг "
"тодорхойлно уу."

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Subject"
msgstr "Гарчиг"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "Success"
msgstr "Амжилттай"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_res_config_settings__website_form_enable_metadata
#: model:ir.model.fields,field_description:website_form.field_website__website_form_enable_metadata
msgid "Technical data on contact form"
msgstr "Холбоо барих формын техникийн дата"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.snippet_options
msgid "Text"
msgstr "Текст"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "Thanks!"
msgstr "Баярлалаа!"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "Маягт амжилттай илгээгдлээ."

#. module: website_form
#: code:addons/website_form/controllers/main.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.res_config_settings_view_form
msgid "Track metadata (IP, User Agent, ...) on your Website Forms"
msgstr ""

#. module: website_form
#: model:ir.model.fields,help:website_form.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "We will get back to you shortly."
msgstr "Бид тантай удахгүй эргэн холбогдох болно."

#. module: website_form
#: model:ir.model,name:website_form.model_website
msgid "Website"
msgstr "Вэбсайт"

#. module: website_form
#: model:ir.model.fields,field_description:website_form.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr ""

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.ir_model_view
msgid "Website Forms"
msgstr "Вэбсайт Форм"

#. module: website_form
#: model:ir.model.fields,help:website_form.field_res_config_settings__website_form_enable_metadata
#: model:ir.model.fields,help:website_form.field_website__website_form_enable_metadata
msgid "You can choose to log technical data like IP, User Agent ,..."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "You can't duplicate an item which refers to an actual record."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/js/website_form_editor.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr ""

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Your Company"
msgstr "Таны Компани"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Your Name"
msgstr "Таны нэр"

#. module: website_form
#. openerp-web
#: code:addons/website_form/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website_form.contactus_form
#, python-format
msgid "Your Question"
msgstr "Таны асуулт"

#. module: website_form
#: model_terms:ir.ui.view,arch_db:website_form.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website_form.contactus_thanks
msgid "Your message has been sent successfully."
msgstr "Таны зурвас амжилттай илгээгдсэн."
