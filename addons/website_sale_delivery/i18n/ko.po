# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_delivery
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:33+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: JH CHOI <<EMAIL>>, 2020\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "<b>Shipping Method: </b>"
msgstr "<b>선적 방법 : </b>"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr "고객에게 전달하고자 하는 상품 설명. 이 설명은 모든 판매 주문, 배송 주문 및 고객 송장/대변 전표에 복사됩니다"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "Choose a delivery method"
msgstr "배송 방법을 선택하십시오"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_res_country
msgid "Country"
msgstr "국가"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr "DHL 선적 방법"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "배송 금액"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr "새로운 배송 방법을 선택하면 배송이 업데이트됩니다"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid "Delivery:"
msgstr "배송 :"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description"
msgstr "설명"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "전자 상거래 및 온라인 견적서에 표시되는 설명."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "온라인 견적에 대한 설명"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "Easypost 선적 방법"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr "FedEx 선적 방법"

#. module: website_sale_delivery
#. openerp-web
#: code:addons/website_sale_delivery/static/src/js/website_sale_delivery.js:92
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_methods
#, python-format
msgid "Free"
msgstr "무료"

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr "배송 방법이 귀하의 주소와 호환되지 않는 것 같습니다. 페이지를 새로 고침하고 다시 시도하십시오."

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr "귀하의 주문 및 발송 주소로 선적할 방법이 없습니다. 자세한 내용은 문의하여 주시기 바랍니다."

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_search_inherit_website_sale_delivery
msgid "Published"
msgstr "게시됨"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order
msgid "Sales Order"
msgstr "판매 주문"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery_methods
msgid "Select to compute delivery rate"
msgstr "배송률을 계산하려면 선택"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale_delivery.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "선적 방법"

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/controllers/main.py:71
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "죄송합니다. 귀하의 주문을 선적할 수 없습니다."

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order__amount_delivery
msgid "The amount without tax."
msgstr "세금 별도 금액입니다."

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr "USPS 선적 방법"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "Bpost 선적 방법"
