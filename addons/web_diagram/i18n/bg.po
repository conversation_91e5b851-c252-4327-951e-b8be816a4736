# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_diagram
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON><PERSON>, 2020
# <PERSON><PERSON><PERSON> <albena_viche<PERSON>@abv.bg>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Bulgarian (https://www.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:73
#: code:addons/web_diagram/static/src/js/diagram_controller.js:160
#, python-format
msgid "Activity"
msgstr "Дейност"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:192
#, python-format
msgid ""
"Are you sure you want to remove this node ? This will remove its connected "
"transitions as well."
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:172
#, python-format
msgid "Are you sure you want to remove this transition?"
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:73
#: code:addons/web_diagram/static/src/js/diagram_controller.js:110
#, python-format
msgid "Create:"
msgstr "Създайте:"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_view.js:16
#, python-format
msgid "Diagram"
msgstr "Диаграма"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/xml/base_diagram.xml:5
#, python-format
msgid "New Node"
msgstr "Нов възел"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:143
#: code:addons/web_diagram/static/src/js/diagram_controller.js:160
#, python-format
msgid "Open:"
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:110
#: code:addons/web_diagram/static/src/js/diagram_controller.js:143
#, python-format
msgid "Transition"
msgstr "Преход"
