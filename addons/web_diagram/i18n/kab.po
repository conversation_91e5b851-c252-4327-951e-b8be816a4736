# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_diagram
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-09-08 09:27+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: <PERSON><PERSON><PERSON> (http://www.transifex.com/odoo/odoo-9/language/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram.js:249
#: code:addons/web_diagram/static/src/js/diagram.js:277
#, python-format
msgid "Activity"
msgstr "Armud"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram.js:282
#: code:addons/web_diagram/static/src/js/diagram.js:326
#, python-format
msgid "Create:"
msgstr "Rnu:"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram.js:220
#, python-format
msgid ""
"Deleting this node cannot be undone.\n"
"It will also delete all connected transitions.\n"
"\n"
"Are you sure ?"
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram.js:238
#, python-format
msgid ""
"Deleting this transition cannot be undone.\n"
"\n"
"Are you sure ?"
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram.js:19
#, python-format
msgid "Diagram"
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram.js:124
#, python-format
msgid "New"
msgstr "Amaynut"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/xml/base_diagram.xml:5
#, python-format
msgid "New Node"
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram.js:254
#: code:addons/web_diagram/static/src/js/diagram.js:310
#, python-format
msgid "Open: "
msgstr "Lli: "

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram.js:305
#: code:addons/web_diagram/static/src/js/diagram.js:321
#, python-format
msgid "Transition"
msgstr "Asaka"
