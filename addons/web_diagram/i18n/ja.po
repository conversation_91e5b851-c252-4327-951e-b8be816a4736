# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_diagram
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Japanese (https://www.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:73
#: code:addons/web_diagram/static/src/js/diagram_controller.js:160
#, python-format
msgid "Activity"
msgstr "活動"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:192
#, python-format
msgid ""
"Are you sure you want to remove this node ? This will remove its connected "
"transitions as well."
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:172
#, python-format
msgid "Are you sure you want to remove this transition?"
msgstr ""

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:73
#: code:addons/web_diagram/static/src/js/diagram_controller.js:110
#, python-format
msgid "Create:"
msgstr "作成:"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_view.js:16
#, python-format
msgid "Diagram"
msgstr "ダイアグラム"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/xml/base_diagram.xml:5
#, python-format
msgid "New Node"
msgstr "新規ノード"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:143
#: code:addons/web_diagram/static/src/js/diagram_controller.js:160
#, python-format
msgid "Open:"
msgstr "オープン:"

#. module: web_diagram
#. openerp-web
#: code:addons/web_diagram/static/src/js/diagram_controller.js:110
#: code:addons/web_diagram/static/src/js/diagram_controller.js:143
#, python-format
msgid "Transition"
msgstr "遷移"
