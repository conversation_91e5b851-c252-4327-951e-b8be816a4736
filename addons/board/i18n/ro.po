# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Romanian (https://www.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:58
#, python-format
msgid ""
"\"Add to\n"
"                Dashboard\""
msgstr ""
"Adaugă la \n"
"tabloul de bord"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:30
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/add_to_board_menu.js:132
#, python-format
msgid "'%s' added to dashboard"
msgstr "'%s' adăugat la tabloul de bord"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:88
#: code:addons/board/static/src/xml/board.xml:98
#, python-format
msgid "Add"
msgstr "Adaugă"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:83
#: code:addons/board/static/src/xml/board.xml:93
#, python-format
msgid "Add to my Dashboard"
msgstr "Adaugă la tabloul meu de bord"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:378
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr "Sunteți sigur(ă) ca doriți să ștergeți acest element?"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:432
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "Panou"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:11
#, python-format
msgid "Change Layout"
msgstr "Modifică aspect"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:9
#, python-format
msgid "Change Layout.."
msgstr "Modifică aspect.."

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:41
#, python-format
msgid "Choose dashboard layout"
msgstr "Alege aspectul tabloului de bord"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/add_to_board_menu.js:136
#, python-format
msgid "Could not add filter to dashboard"
msgstr "Nu s-a putut adăuga filtru la tablou de bord"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:83
#, python-format
msgid "Edit Layout"
msgstr "Editează aspectul"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:46
#, python-format
msgid "Layout"
msgstr "Aspect"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/board_view.js:46
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr "Tabloul meu de bord"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/add_to_board_menu.js:133
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr "Actualizați browserul pentru ca modificările să aibă efect."

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:56
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                menu, switch to list or graph view, and click"
msgstr ""
"Pentru a adăuga primul dvs. raport în acest tablou de bord, accesați orice "
"meniu, comutați la vizualizare listă sau grafic și faceți clic pe"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:60
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                dashboard using the search options."
msgstr ""
"Puteți filtra și grupa datele înainte de inserare în\n"
"tabloul de bord, folosind opțiunile de căutare"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:54
#, python-format
msgid "Your personal dashboard is empty"
msgstr "Tabloul de bord personal este gol."

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:58
#, python-format
msgid "in the extended search options."
msgstr "în opțiunile de căutare extinsă."
