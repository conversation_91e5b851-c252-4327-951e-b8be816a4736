<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="_assets_primary_variables" inherit_id="web._assets_primary_variables">
            <xpath expr="//link[last()]" position="after">
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/variables.scss"/>
            </xpath>
        </template>

        <template id="assets_backend" name="mail assets" inherit_id="web.assets_backend">
            <xpath expr="." position="inside">
                <script type="text/javascript" src="/mail/static/src/js/many2many_tags_email.js"></script>

                <!-- Services -->
                <script type="text/javascript" src="/mail/static/src/js/services/mail_manager.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/services/mail_document_thread_manager.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/services/mail_status_manager.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/services/mail_notification_manager.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/services/mail_window_manager.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/services/mail_service.js"></script>
                <!-- Models -->
                <script type="text/javascript" src="/mail/static/src/js/models/mail_failure.js"></script>
                    <!-- messages -->
                    <script type="text/javascript" src="/mail/static/src/js/models/messages/abstract_message.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/messages/message.js"></script>
                    <!-- threads -->
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/abstract_thread.js"></script>
                        <!-- channels -->
                        <script type="text/javascript" src="/mail/static/src/js/models/threads/channel.js"></script>
                        <script type="text/javascript" src="/mail/static/src/js/models/threads/multi_user_channel.js"></script>
                        <script type="text/javascript" src="/mail/static/src/js/models/threads/two_user_channel.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/create_mode_document_thread.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/dm_chat.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/document_thread.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/livechat.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/mailbox.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/searchable_thread.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/thread.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/mixins/channel_seen_mixin.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/threads/mixins/thread_typing_mixin.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/utils/timer.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/utils/timers.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/models/utils/cc_throttle_function.js"></script>
                <!-- Widgets -->
                    <!-- thread windows -->
                    <script type="text/javascript" src="/mail/static/src/js/thread_windows/abstract_thread_window.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/thread_windows/thread_window.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/discuss.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/document_viewer.js"></script>
                    <!-- composers -->
                    <script type="text/javascript" src="/mail/static/src/js/composers/basic_composer.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/composers/chatter_composer.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/composers/extended_composer.js"></script>
                    <script type="text/javascript" src="/mail/static/src/js/composers/mention_manager.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/chatter.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/followers.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/form_controller.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/form_view.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/form_renderer.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/basic_model.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/basic_view.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/thread_field.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/thread_widget.js"></script>
                <!-- systray -->
                <script type="text/javascript" src="/mail/static/src/js/systray/systray_activity_menu.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/systray/systray_messaging_menu.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/tours/mail.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/user_menu.js"></script>
                <!-- tools -->
                <script type="text/javascript" src="/mail/static/src/js/tools/debug_manager.js"></script>
                <!-- filter menu -->
                <script type="text/javascript" src="/mail/static/src/js/filter_menu.js"></script>
                <!-- utils -->
                <script type="text/javascript" src="/mail/static/src/js/utils.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/activity.js"></script>
                <!-- Activity view type -->
                <script type="text/javascript" src="/mail/static/src/js/views/activity/activity_view.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/views/activity/activity_model.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/views/activity/activity_controller.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/views/activity/activity_renderer.js"></script>
                <script type="text/javascript" src="/mail/static/src/js/views/activity/activity_record.js"></script>
                <!-- Attachment box -->
                <script type="text/javascript" src="/mail/static/src/js/attachment_box.js"></script>
                <!-- Others -->
                <script type="text/javascript" src="/mail/static/src/js/emojis.js"></script>

                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/announcement.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/discuss.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/abstract_thread_window.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/thread_window.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/composer.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/chatter.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/followers.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/thread.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/systray.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/mail_activity.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/activity_view.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/kanban_view.scss"/>
                <link rel="stylesheet" type="text/scss" href="/mail/static/src/scss/attachment_box.scss"/>
            </xpath>
        </template>

        <template id="js_test_assets" name="mail_js_test_assets" inherit_id="web.js_tests_assets">
            <xpath expr="." position="inside">
                <script type="text/javascript" src="/mail/static/tests/helpers/mock_server.js"></script>
                <script type="text/javascript" src="/mail/static/tests/helpers/test_utils.js"></script>
            </xpath>
        </template>

        <template id="qunit_suite" name="mail_tests" inherit_id="web.qunit_suite">
            <xpath expr="//t[@t-set='head']" position="inside">
                <!-- thread window -->
                    <script type="text/javascript" src="/mail/static/tests/thread_window/basic_thread_window_tests.js"></script>
                    <script type="text/javascript" src="/mail/static/tests/thread_window/blank_thread_window_tests.js"></script>
                    <script type="text/javascript" src="/mail/static/tests/thread_window/hidden_thread_window_tests.js"></script>
                    <script type="text/javascript" src="/mail/static/tests/thread_window/moderation_thread_window_tests.js"></script>
                    <script type="text/javascript" src="/mail/static/tests/thread_window/typing_thread_window_tests.js"></script>
                <script type="text/javascript" src="/mail/static/tests/document_thread_window_tests.js"></script>
                <script type="text/javascript" src="/mail/static/tests/chatter_tests.js"></script>
                <script type="text/javascript" src="/mail/static/tests/mail_utils_tests.js"></script>
                <script type="text/javascript" src="/mail/static/tests/discuss_tests.js"></script>
                <script type="text/javascript" src="/mail/static/tests/document_viewer_tests.js"></script>
                <!-- systray -->
                    <script type="text/javascript" src="/mail/static/tests/systray/systray_activity_menu_tests.js"></script>
                    <script type="text/javascript" src="/mail/static/tests/systray/systray_messaging_menu_tests.js"></script>
                    <script type="text/javascript" src="/mail/static/tests/systray/systray_messaging_menu_mail_failure_tests.js"></script>
                <script type="text/javascript" src="/mail/static/tests/discuss_moderation_tests.js"></script>
                <script type="text/javascript" src="/mail/static/tests/discuss_seen_indicator_tests.js"></script>
                <script type="text/javascript" src="/mail/static/tests/discuss_typing_notification_tests.js"></script>
                <!-- tools -->
                <script type="text/javascript" src="/mail/static/tests/tools/debug_manager_tests.js"></script>
                <!-- models -->
                    <script type="text/javascript" src="/mail/static/tests/models/cc_throttle_function_tests.js"></script>
                    <script type="text/javascript" src="/mail/static/tests/models/timer_tests.js"></script>
                    <script type="text/javascript" src="/mail/static/tests/models/timers_tests.js"></script>
                <!-- activity view -->
                <script type="text/javascript" src="/mail/static/tests/activity_tests.js"></script>
                <!-- services -->
                <script type="text/javascript" src="/mail/static/tests/mail_status_manager_tests.js"></script>
            </xpath>
        </template>

        <template id="message_origin_link">
            <p>This <t t-esc="self.env['ir.model']._get(self._name).name.lower()"/> has been <span t-if="edit">modified</span><span t-if="not edit">created</span> from:
                <t t-foreach="origin" t-as="o">
                    <a href="#" t-att-data-oe-model="o._name" t-att-data-oe-id="o.id"> <t t-esc="o.display_name"/></a><span t-if="origin.ids[-1:] != o.ids">, </span>
                </t>
            </p>
        </template>

    </data>
</odoo>
