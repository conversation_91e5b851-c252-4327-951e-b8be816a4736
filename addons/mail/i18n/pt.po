# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# 425fe09b3064b9f906f637fff94056ae_a00ea56 <0fa3588fa89906bfcb3a354600956e0e_308047>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:22+0000\n"
"PO-Revision-Date: 2019-08-26 09:11+0000\n"
"Last-Translator: Reinaldo Ramos <<EMAIL>>, 2022\n"
"Language-Team: Portuguese (https://www.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr "Este canal é privado. As pessoas devem ser convidadas para aderirem."

#. module: mail
#: code:addons/mail/wizard/invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s convidou-o a seguir um novo documento."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#, python-format
msgid "%d Messages"
msgstr "%d Mensagens"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "%d days overdue"
msgstr "%d dias em atraso"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid ""
"%s \n"
"(inactive)"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (cópia)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mixins/thread_typing_mixin.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s e %s estão a escrever..."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s criado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mixins/thread_typing_mixin.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s está a escrever..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mixins/thread_typing_mixin.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s e outros estão a escrever..."

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "%s: %s assigned to you"
msgstr "%s: %s foi-lhe atribuido"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "&nbsp;("
msgstr "&nbsp;("

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid ", due on"
msgstr ", vencido a"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Mostrar mensagens antigas --------"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Future"
msgstr "0 Futuro"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Late"
msgstr "0 Atrasado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Today"
msgstr "0 Hoje"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b>.to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>\n"
"            Type <b>:shortcut</b> to insert canned responses in your message.<br>"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">%(author)s invited %(new_partner)s to <a "
"href=\"#\" class=\"o_channel_redirect\" data-oe-"
"id=\"%(channel_id)s\">#%(channel_name)s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">%(author)s convidou %(new_partner)s para "
"<a href=\"#\" class=\"o_channel_redirect\" data-oe-"
"id=\"%(channel_id)s\">#%(channel_name)s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">criado <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">joined <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">juntou-se a <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">left <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">saiu <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_short
msgid ""
"<i class=\"fa fa-ban\" style=\"color: red;\" role=\"img\" title=\"This email"
" is blacklisted for mass mailing\" aria-label=\"Blacklisted\" "
"attrs=\"{'invisible': [('is_blacklisted', '=', False)]}\" "
"groups=\"base.group_user\"/>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p> Create a private channel.</p>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Escreva aqui uma mensagem</b> para os seguidores do canal.</p> <p>Pode"
" notificar alguém com <i>'@'</i> ou associar outro canal com <i>'#'</i>. "
"Comece a sua mensagem com <i>'/'</i> para obter a lista dos possíveis "
"comandos.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Os Canais tornam mais fácil a organização de informação entre diferentes "
"tópicos e grupos.</p> <p>Experimente <b>criar o seu primeiro canal</b> (ex. "
"vendas, marketing, artigo XYZ, festa depois do trabalho, etc).</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a channel here.</p>"
msgstr "<p>Crie aqui um canal.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a public or private channel.</p>"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/create_mode_document_thread.js:0
#, python-format
msgid "<p>Creating a new record...</p>"
msgstr "<p>A criar um novo registo...</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr ""
"<span class=\"fa fa-info-circle\"/> Atenção: Não será possível enviar este "
"email de novo para os reIt won't be possible to send this mail again to the "
"destinatários que não selecionou."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr "<span class=\"o_form_label\">Atividades</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Adicionar</span>\n"
"                                    <span class=\"o_stat_text\">Ação de Contexto</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Remover</span>\n"
"                                    <span class=\"o_stat_text\">Ação de Contexto</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""
"<strong>Comunicação interna</strong>: Uma reposta irá enviar uma nota "
"interna. Os seguidores não receberão qualquer notificação por email."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""
"<strong>Serão usados apenas os registos selecionados na vista de lista.</strong><br/>\n"
"                                O email será enviado para todos os registo selecionados na lista."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>Atividades Recomendadas</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Um dicionário de Python que será avaliado para fornecer valores predefinidos"
" quando criar novos registos para este aliás."

#. module: mail
#: code:addons/mail/models/ir_actions.py:0
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr ""
"Uma próxima atividade só pode ser planeada em modelos que usem a discussão"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""
"Um atalho é um atalho de teclado. P.e., se escrever #bd, será transformado "
"em \"Bom dia\"."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Accept"
msgstr "Aceitar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Accept selected messages"
msgstr "Aceitar mensagens selecionadas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Accept |"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__moderation_status__accepted
msgid "Accepted"
msgstr "Aceite"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Grupos de Acesso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Acção Necessária"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
msgid "Action To Do"
msgstr "Ação a fazer"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action to Perform"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Uma ação pode despoletar comportamentos específicos, como abrir uma vista do"
" calendário ou marcar automaticamente um documento como concluído depois do "
"carregamento."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Ativado por padrão na inscrição"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "Activo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "Domínio ativo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model:mail.message.subtype,name:mail.mt_activities
#, python-format
msgid "Activities"
msgstr "Actividades"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:0
#: code:addons/mail/static/src/xml/chatter.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#, python-format
msgid "Activity"
msgstr "Atividade"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Marcador de Exceções de Atividade"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Estado da Atividade"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Tipo de Atividade"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Tipos de Atividade"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "Activity User Type"
msgstr "Tipos de atividade do Utilizador"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "Tipo de Atividade"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Add"
msgstr "Adicionar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Add Attachments"
msgstr "Adicionar Anexos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__channel_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__channel_ids
#, python-format
msgid "Add Channels"
msgstr "Adicionar Canais"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "Adicione Uma Lista Negra de Email"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Adicionar Seguidores"

#. module: mail
#: code:addons/mail/models/ir_actions.py:0
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr "adicionar sinal "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__user_signature
#: model:ir.model.fields,field_description:mail.field_mail_template__user_signature
msgid "Add Signature"
msgstr "Adicionar Assinatura"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Add a channel"
msgstr "Adicione um canal"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "Adicione um novo %(document)s ou envie um email para %(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address in the blacklist"
msgstr "Adicione um endereço de email à lista negra"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#, python-format
msgid "Add attachment"
msgstr "Adicionar anexo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add channels to notify..."
msgstr "Adicionar canais para notificar..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Adicione contactos a notificar..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Add this email address to white list of people"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Contactos Adicionais"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Avançado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "Definições Avançadas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "Alerta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model:ir.model.fields,field_description:mail.field_res_users__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "Nome alternativo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
#: model:ir.model.fields,field_description:mail.field_res_users__alias_contact
msgid "Alias Contact Security"
msgstr "Segurança de Contacto do Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "Domínio Apelido"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "Nome do Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "Domínio do alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "Modelo Aliased"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Nome alternativo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "All"
msgstr "Todos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Allowed Emails"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_moderation__status__allow
msgid "Always Allow"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Always Allow |"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/mail_failure.js:0
#, python-format
msgid "An error occurred when sending an email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/static/src/js/models/messages/message.js:0
#, python-format
msgid "Anonymous"
msgstr "Anónimo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_mail__no_auto_thread
#: model:ir.model.fields,help:mail.field_mail_message__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""
"As respostas não cairão no assunto do documento original da discussão. Isto "
"terá um impacto na identificação da mensagem já gerado."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Aplica-se a"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "Apply"
msgstr "Aplicar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
msgid "Archived"
msgstr "Arquivado"

#. module: mail
#: code:addons/mail/wizard/mail_resend_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures. You won't be "
"able to re-send these mails later!"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "Atribuído a"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Anexar um Ficheiro"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
msgid "Attachment"
msgstr "Anexo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Conta Anexada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#: code:addons/mail/static/src/xml/chatter.xml:0
#: model:ir.model.fields,field_description:mail.field_email_template_preview__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Anexos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Parceiros Autenticados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Autor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Author Signature (mass mail only)"
msgstr "Assinatura do Autor (E-mails em massa apenas)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Autor da mensagem. Se não definido, o campo email_from pode conter um "
"endereço de email que não corresponde a nenhum parceiro."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Avatar do Autor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "Grupo autorizado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Eliminar Automático"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "Auto Inscrição"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Subscrição Automática"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_autovacuum
msgid "Automatic Vacuum"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_notify
msgid "Automatic notification"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Ban"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Ban List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Ban this email address"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Banned Emails"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Base Tributável"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Be careful with channels following internal notifications"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr "Melhores cumprimentos, "

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
msgid "Blacklist"
msgstr "Lista negra"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Body"
msgstr "Corpo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
msgid "Bounce"
msgstr "Devolver"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "Emails Rejeitados"

#. module: mail
#: code:addons/mail/models/mail_cc_mixin.py:0
#, python-format
msgid "CC Email"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Ligar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
msgid "Can Write"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Cancelar Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "Cancelado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Resposta pré-feita / Atalho"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__canned_response_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__canned_response_ids
msgid "Canned Responses"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Destinatários da mensagem Cc"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_cc
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr ""
"Destinatários com conhecimento (espaços reservados podem ser usados ​​aqui)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall
msgid "Catchall Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Changed"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Changed Field"
msgstr "Campo Alterado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_moderation__channel_id
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "Canal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_message_ids
msgid "Channel Message"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_moderation_menu
msgid "Channel Moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Tipo de Canal"

#. module: mail
#: model:ir.model,name:mail.model_mail_moderation
msgid "Channel black/white list"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Channel settings"
msgstr "Definições do canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "Canais"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "Canais/Parceiros"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Chat"
msgstr "Conversar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__chat
msgid "Chat Discussion"
msgstr "Discussão da Conversação"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "Chat Shortcode"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__out_of_office_message
msgid "Chat Status"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Mensagens filhas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Choose a language:"
msgstr "Escolher um idioma:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Choose an example"
msgstr "Escolher um exemplo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Close"
msgstr "Fechar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:0
#, python-format
msgid "Close chat window"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__closed
msgid "Closed"
msgstr "Cancelada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Cópia de endereços de recipientes separados por vírgula"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Ids de parceiros recipientes separados por vírgula"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__partner_to
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr "Ids de parceiros separados por vírgula (podem usar contentores aqui)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Endereços separados por vírgula"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_to
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Endereços dos destinatários separados por vírgula (espaços reservados podem "
"ser usados ​​aqui)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Comentário"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Compor Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Modo de composição"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "config configurações"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "Parabéns, a sua caixa de entrada está vazia"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "Parabéns, a sua caixa de entrada está vazia!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Contactos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Conteúdo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Conteúdo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "Estado de Conversa Dobrado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "A conversa está minimizada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Conversations"
msgstr "Conversações"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Contador do número de emails devolvidos para este contacto"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Create"
msgstr "Criar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Create %s (Private)"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Create %s (Public)"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create a new %(document)s"
msgstr "Crie um novo%(document)s"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create a new %(document)s by sending an email to %(email_link)s"
msgstr "Crie um novo %(document)s enviando um email para %(email_link)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Criado por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_moderation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_email_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_moderation__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#, python-format
msgid "Created on"
msgstr "Criado em"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Data da criação"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "Usuário atual marcou uma notificação ligada a esta mensagem"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__is_moderator
msgid "Current user is a moderator of the channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__custom_channel_name
msgid "Custom channel name"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_res_partner_needaction_rel_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Data"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Dates"
msgstr "Datas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Dias"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "Prazo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Caro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Destaque"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Predefinição"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_description
msgid "Default Description"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_next_type_id
msgid "Default Next Activity"
msgstr "Próxima Atividade Predefinida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "Valor Predefinido"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "Valores por Defeito"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__use_default_to
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Recipientes Por Defeito"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__use_default_to
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Destinatários padrão do registo: \n"
"- Parceiro (usando id em um parceiro ou o campo partner_id) OU \n"
"- E-mail (usando email_from ou campo de e-mail)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "Apagar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Eliminar Mensagens"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "Eliminar Cópia da Mensagem"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Falha na Entrega"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Descrição"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Descrição que será adicionada na mensagem enviada para este subtipo. Se "
"vazio, o nome será adicionado em seu lugar."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Direct Messages"
msgstr "Mensagens Diretas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Discard selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Discard |"
msgstr ""

#. module: mail
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Discuss"
msgstr "Discussões"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion Channel"
msgstr "Canal de Discussão"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Discussões"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_address_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_moderation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread__display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract__display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Mostrar uma opção sobre documentos relacionados para abrir um assistente de "
"composição com este modelo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"Não mantenha uma cópia do \"e-mail\" no histórico de comunicações do "
"documento (apenas vários envios)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/chatter.js:0
#, python-format
msgid "Do you really want to delete %s?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Documento"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Seguidores do Documento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr "Modelo do Documento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Nome do Documento"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Document not downloadable"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Done"
msgstr "Concluído"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr "Concluir  & Abrir Próxima"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Concluir & Agendar Próxima"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Download"
msgstr "Transferir"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#, python-format
msgid "Drag Files Here"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Dropdown menu"
msgstr "Criar novo Contrato"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Dropdown menu - Followers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "Data de vencimento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Data de Vencimento em"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Due in %d days"
msgstr "Vencido em %d dias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Tipo de Vencimento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr "Gereador dinâmico de Conteúdo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "Edit Subscription of "
msgstr "Editar Subscrição de "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Edit subscription"
msgstr "Editar Subscrição"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_moderation__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#: model:mail.activity.type,name:mail.mail_activity_data_email
msgid "Email"
msgstr "Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
msgid "Email Address"
msgstr "Endereço eletrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_address_mixin
msgid "Email Address Mixin"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "Alias de Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Apelidos de E-mail"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Configuração do correio eletrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Email Preview"
msgstr "Pré-visualizar E-mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Pesquisa de email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
msgid "Email Status"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "Modelo de E-mail"

#. module: mail
#: model:ir.model,name:mail.model_email_template_preview
msgid "Email Template Preview"
msgstr "Pre visualização do Template do Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_template
msgid "Email Templates"
msgstr "Modelos de E-mail"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Email Thread"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "Endereço de email já existe!"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__alias_id
msgid ""
"Email address internally associated with this user. Incoming emails will "
"appear in the user's notifications."
msgstr ""
"Endereço de e-mail associado internamente a este usuário. Mensagens "
"recebidas aparecerão nas notificações do usuário."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Endereço de email do remetente. Este campo será definido quando nenhum "
"parceiro é encontrada e substitui o campo author_id na conversa."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__bounce
msgid "Email address rejected by destination"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to redirect replies..."
msgstr "Endereço de email para redirecionar eespostas"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted means that the recipient won't receive "
"mass mailing anymore."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "E-mail cc"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistente de composição de e-mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Mensagem Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "Assistente de reenvio de email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "Emails"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#, python-format
msgid "Emojis"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/attachment_box.js:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "Erro"

#. module: mail
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Erro durante a comunicação com o servidor de suporte."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"Erro sem exception. Provavelmente dever-se-á a atualizações concorrentes dos"
" registos de notificação. Contacte um administrador."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr ""
"Erro sem excepção. Possivelmente devido ao envio de um email sem qualquer "
"destinatário."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_channel_res_model_id_uniq
msgid "Error, a channel cannot follow twice the same object."
msgstr "Erro, um canal não pode seguir duas vezes o mesmo objecto."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Erro, um parceiro não pode seguir duas vezes o mesmo objecto"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_partner_xor_channel
msgid ""
"Error: A follower must be either a partner or a channel (but not both)."
msgstr ""
"Erro: Um seguidor tem de ser um parceiro ou um canal (mas não os dois)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__public
msgid "Everyone"
msgstr "Todos"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Exceção"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Filtros Avançados..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Mensagem Falhada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Falhou"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Failed to render template %r using values %r"
msgstr "Falhou a renderizar o template %r usando os valores %r"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Razão da Falha"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Razão da Falha: Isto é normalmente a expectativa enviada pelo servidor de "
"email, guardada para facilitar o debug de problemas nos envios."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Favorito Por"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Feedback"
msgstr "Enviar Opiniões"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
msgid "Field"
msgstr "Campo"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "Descrição do Campo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "Tipo de Campo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Campo usado para associar o modelo relacionado ao subtipo do modelo ao usar "
"subscrição automática de um documento relacionado. O campo usado para "
"calcular é getattr(related_document.relation_field)."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Campos"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Expressão espaço reservado final, a cópia colada no campo do template "
"desejado."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__folded
msgid "Folded"
msgstr "Dobrado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Follow"
msgstr "Seguir"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
#, python-format
msgid "Followers"
msgstr "Seguidores"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_channel_ids
msgid "Followers (Channels)"
msgstr "Seguidores (Canais)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Parceiros)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Formulário do Seguidor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Followers of"
msgstr "Seguidores de"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Seguidores apenas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Following"
msgstr "Seguindo"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Following invites are invalid as user groups do not match: %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ícone do Font awesome ex. fa-tasks"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_from
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
msgid "From"
msgstr "De"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Full composer"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Future"
msgstr "Futuro"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Atividades Futuras"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Gateway"
msgstr "Gateway"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Generic User From Record"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Ir para o menu de configuração"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Agrupar por"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Agrupar por..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "Grupos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_guidelines_msg
msgid "Guidelines"
msgstr "Linhas Condutoras"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Guidelines of channel %s"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "Roteamento HTTP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Gerido por Emails"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Gerido no Odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Tem erros"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Cabeçalhos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "Hello"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/invite.py:0
#, python-format
msgid "Hello,"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Ocultado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Esconder o subtipo nas opcções do seguidor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "History"
msgstr "Histórico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_address_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_moderation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_thread__id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID do registo parent que armazena o alias (Ex.: projeto que armazena o alias"
" de criação de tarefas)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Ícone"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ícone para indicar uma exceção na atividade."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id do recurso seguido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Idle"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se selecionado, as novas mensagens requerem a sua atenção."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selecionado, algumas mensagens tem um erro de distribuição."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""
"Se marcado, os parceiros receberão um aviso de e-mail que foram adicionados "
"nos seguidores do documento."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__user_signature
#: model:ir.model.fields,help:mail.field_mail_template__user_signature
msgid ""
"If checked, the user's signature will be appended to the text version of the"
" message"
msgstr ""
"Se marcada, a assinatura do utilizador será anexada à versão de texto da "
"mensagem"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked in the chatter. "
"Value is used to order tracking values."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__scheduled_date
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Jinja2 placeholders may be used."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Se tem a configuração de um catch-all domínio de e-mail redirecionado para o"
" servidor Odoo , digite o nome de domínio aqui."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid ""
"If you remove a follower, he won't be notified of any email or discussion on"
" this document. Do you really want to remove %s?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_128
#, python-format
msgid "Image"
msgstr "Imagem"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "Apelidos Inactivos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "Caixa de Entrada"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Info"
msgstr "Informação"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model_id
msgid "Initial model"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "Mensagem inicial do tópico."

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "Integrações "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Apenas Interno"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__recipient
msgid "Invalid email address"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Expressão inválida, deve ser uma definição literal de dicionário estilo "
"python como \"{'field': 'value'}\""

#. module: mail
#: code:addons/mail/models/mail_address_mixin.py:0
#: code:addons/mail/models/mail_address_mixin.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr ""

#. module: mail
#: code:addons/mail/controllers/main.py:0
#, python-format
msgid "Invalid token in route %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "Invitation"
msgstr "Convite"

#. module: mail
#: code:addons/mail/wizard/invite.py:0
#, python-format
msgid "Invitation to follow %s: %s"
msgstr "Convite para seguir %s: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Invite"
msgstr "Convidar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "Invite Follower"
msgstr "Convidar Seguidor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Invite people"
msgstr "Convidar Pessoas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Invite people to #%s"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Wizard de convites"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__private
msgid "Invited people only"
msgstr "Apenas pessoas convidadas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Is Allowed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Is Banned"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "É um Seguidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification
msgid "Is Notification"
msgstr "É notificação"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_subscribed
msgid "Is Subscribed"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_chat
msgid "Is a chat"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is a member"
msgstr "É um membro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__is_moderator
msgid "Is moderator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "está ligado à interface"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "Entrar"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "Seguir um grupo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Idioma"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fetched_message_id
msgid "Last Fetched"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_address_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_moderation____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread____last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_publisher_warranty_contract____last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "Última vez visto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_moderation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_moderation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Late"
msgstr "Atrasado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Atividades em Atraso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Aspeto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Leave"
msgstr "Ausência"

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Leave this channel"
msgstr "Sair deste canal"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_cc__email_cc
msgid "List of cc from incoming emails."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__channel_ids
msgid ""
"List of channels that will be added as listeners of the current document."
msgstr ""
"Lista de canais que serão adicionados como ouvintes do documento atual."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr ""
"Lista de parceiros que serão adicionados como seguidores do documento "
"actual."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "List users in the current channel"
msgstr "Listar utilizadores no canal atual"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__channel_id
msgid "Listener"
msgstr "Ouvinte"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
msgid "Listeners"
msgstr "Ouvintes"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading"
msgstr "A carregar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading older messages..."
msgstr "A carregar mensagens anteriores..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Loading..."
msgstr "A carregar..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/chatter_composer.js:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Registo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Log a note. Followers will not be notified."
msgstr "Registe uma nota. Seguidores não serão notificados."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "Registe uma nota..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "Registe uma Atividade"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "Registar uma Nota Interna"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Log note"
msgstr "Registar uma Nota"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Log or schedule an activity"
msgstr "Registe ou agende uma atividade"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_id
#, python-format
msgid "Mail"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Tipo de atividade do email"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Mail Body"
msgstr "Conteúdo do e-mail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "Formulário de Canal de Email"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Mail Delivery Failed"
msgstr "Entrega de Email Falhada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Valor de Monitorização do Correio"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"Mensagem foi criada para notificar as pessoas de uma mail.message existente"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_notify_channel_moderators_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_notify_channel_moderators
#: model:ir.cron,name:mail.ir_cron_mail_notify_channel_moderators
msgid "Mail: Notify channel moderators"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_users__message_main_attachment_id
msgid "Main Attachment"
msgstr "Anexos Principais"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Manage Messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Mark Done"
msgstr "Marcar como Concluído"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Mark all as read"
msgstr "Marcar todas como lidas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Mark all read"
msgstr "Marcar todos como lidos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "Marcar como Concluído"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Mark as Read"
msgstr "Marcar como Lido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "Marcar como a Fazer"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Mark as done"
msgstr "Marcar como feito"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Mark as todo"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
msgid "Mass Mail Blacklist"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Reunião"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "Membros"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Membros destes grupos serão adicionados automaticamente como seguidores. "
"Observe que eles poderão gerenciar sua inscrição manualmente se necessário."

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/base_partner_merge.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Fundido com os seguintes parceiros:"

#. module: mail
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Message"
msgstr "Mensagem"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Gerir erro de entrega"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "ID Mensagem"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Notificações de Mensagem"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Nome do Registro da Mensagem"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Tipo de mensagem"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Message are pending moderation"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Recipientes da Meensagem (emails)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr ""
"Referências de mensagens, tais como identificadores de mensagens anteriores"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Message sent in \""
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid Message instance"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Subtipos de mensagem proporcionam um tipo mais preciso nas mensagens, "
"especialmente para sistemas de notificação. Por exemplo, podem ser uma "
"notificação relacionada a um novo registro (Novo), ou a uma mudança de "
"estágio em um processo (Mudança de Estágio). Elas permitem refinar as "
"notificações que o usuário quer receber em ser mural."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Subtipos de Mensagem"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Subtipos de mensagem seguidos, são subtipos que serão enviados ao Mural do "
"usuário."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Tipo de mensagem: e-mail para mensagens de e-mail, notificação para "
"mensagens do sistema e comentário para outros tipos como respostas de "
"usuário."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Identificador de mensagem exclusivo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Id mensagem"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "Mensagens"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Pesquisar Mensagens"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Mensagens com subtipos internos serão visíveis apenas por funcionários, ou "
"seja membros do base_user group"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/mailbox.js:0
#, python-format
msgid "Missing domain for mailbox with ID '%s'"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "Modelo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Modelo do recurso seguido"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Modelo do subtipo a que se aplica. Se falso, este subtipo se aplica a todos "
"os modelos."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Modelos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
#, python-format
msgid "Moderate Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation
msgid "Moderate this channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__moderator_id
#: model:ir.model.fields,field_description:mail.field_mail_message__moderator_id
msgid "Moderated By"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_ids
msgid "Moderated Emails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__moderation_channel_ids
msgid "Moderated channels"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Moderated channels must have moderators."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_count
msgid "Moderated emails count"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_moderation_action
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Moderation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_tree
msgid "Moderation Lists"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Moderation Queue"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__moderation_status
#: model:ir.model.fields,field_description:mail.field_mail_message__moderation_status
msgid "Moderation Status"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__moderation_counter
msgid "Moderation count"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_moderator
msgid "Moderator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderator_ids
msgid "Moderators"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Moderators must have an email address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Moderators should be members of the channel they moderate."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Meses"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Name"
msgstr "Nome"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "Nome dado ao documento relacionado."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__report_name
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""
"Nome para usar para o ficheiro do relatório gerado (pode conter espaços reservados)\n"
"A extensão pode ser omitida e, então, vir a partir do tipo de relatório."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Ação Necessária"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__need_moderation
#: model:ir.model.fields,field_description:mail.field_mail_message__need_moderation
msgid "Need moderation"
msgstr "Precisa de moderação "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
msgid "Needaction Recipient"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "New Channel"
msgstr "Novo Canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "New Message"
msgstr "Nova Mensagem"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Novo valor de Texto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Novo Valor de Data e Hora: "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Data valor decimal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Novo Valor Inteiro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "Novo Valor Monetário"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Novo Valor de Texto: "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "New message"
msgstr "Nova Mensagem"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "New messages"
msgstr "Novas mensagens"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "New messages appear here."
msgstr "As novas mensagens aparecem aqui."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "New people"
msgstr "Novas pessoas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__moderation_guidelines
msgid ""
"Newcomers on this moderated channel will automatically receive the "
"guidelines."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Next"
msgstr "Seguinte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Próximas Atividades"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Prazo da Próxima Atividade"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Sumário da Próxima Atividade"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de atividade seguinte "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "No activities planned."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "No conversation yet..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "No data to display"
msgstr "Sem dados a mostrar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "No follower"
msgstr "Sem seguidor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "No history messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "No message available"
msgstr "Nenhuma mensagem disponível"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "No message matches your search. Try to change your search filters."
msgstr ""
"Nenhuma mensagem corresponde à sua procura. Tente alterar os filtros de "
"procura."

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "No starred messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_mail__no_auto_thread
#: model:ir.model.fields,field_description:mail.field_mail_message__no_auto_thread
msgid "No threading for answers"
msgstr "Nenhum tópico para respostas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
msgid "None"
msgstr "Nenhum(a)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_address_mixin__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
msgid "Note"
msgstr "Nota"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Note by"
msgstr "Nota de"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Notificação"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_notify_msg
msgid "Notification message"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_delete_notification
#: model:ir.cron,name:mail.ir_cron_delete_notification
msgid "Notification: Delete Notifications older than 6 Month"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
msgid "Notifications"
msgstr "Notificações"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "Notificar seguidores"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Notificar seguidores do documento (posts em massa apenas)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Ações"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Número de erros"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensagens que requerem uma ação"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero de mensagens com um Erro de entrega."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_users__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensagens não lidas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Offline"
msgstr "Desligado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Valor Antigo de Texto "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Valor Antigo de Data e Hora"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Valor Antigo Decimal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Valor Antigo Inteiro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "Valor Antigo Monetário"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Valor Antigo de Texto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "One follower"
msgstr "Um seguidor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Online"
msgstr "Online"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to use grouped read on message model"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Only an administrator or a moderator can send guidelines to channel members!"
msgstr ""

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Only mailing lists can be moderated."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__open
msgid "Open"
msgstr "Aberto"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Open Channel in Discuss to moderate"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "Abrir o Documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr "Abrir Documento Pai"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Open chat"
msgstr "Conversação aberta"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#, python-format
msgid "Open document"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#, python-format
msgid "Open in Discuss"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID opcional de uma linha (registo) à qual todas as mensagens de entrada "
"serão anexadas, mesmo que não tenham tido resposta. Se definido, irá "
"desativar completamente a criação de novos registos."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__mail_server_id
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Servidor preferencial opcional de mensagens a enviar. Se não for definido "
"será usado o de prioridade mais alta."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__report_template
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "Relatório opcional para imprimir e anexar"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. "
"${object.partner_id.lang}."
msgstr ""
"Língua de tradução Opcional (código ISO) para selecionar quando o envio de "
"um e-mail. Se não for definido, a versão Inglesa será usada. Isso geralmente"
" deve ser uma expressão de espaço reservado que fornecer a linguagem "
"apropriada, por exemplo, $ {} object.partner_id.lang."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Valor opcional para usar se o campo do destino estiver vazio"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/models/threads/dm_chat.js:0
#, python-format
msgid "Out of office until %s"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Entrega"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Servidor de Envio de Email"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Mensagens a Enviar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Servidor de Outgoing mail"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "Vencido"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Reescrever e-mail do autor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "Responsável"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "PDF file"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Ascendente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Mensagem Principal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "Modelo Parent"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID da Linha de Registo do Parent"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modelo parent que armazena o alias. O modelo que armazena a referência ao "
"alias não é necessariamente o modelo determinado por alias_model_id (Ex.: "
"project (parent_model) e task (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Subtipo pai, utilizado para subscrição automática. Este campo não está "
"correctamente nomeado. Por exemplo num projecto, o parent_id dos subtipos de"
" projecto refere-se a subtipos relacionados com tarefas."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
msgid "Partner"
msgstr "Parceiro"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "Perfil do Parceiro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additionnal information for mail resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Parceiros com Ação Necessária"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__moderation_status__pending_moderation
msgid "Pending Moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Pending moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Pending moderation messages appear here."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_moderation__status__ban
msgid "Permanent Ban"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid "Permanently delete this email after sending it, to save space"
msgstr ""
"Apagar permanentemente este email depois de enviado, para guardar espaço"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "Expressão de Conteúdo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#, python-format
msgid "Planned"
msgstr "Planeado "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Planned activities"
msgstr "Atividades planeadas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Ao fim de"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/chatter_composer.js:0
#, python-format
msgid "Please complete customer's informations"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "Please find below the guidelines of the"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Please wait"
msgstr "Por favor aguarde"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Please wait..."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:0
#, python-format
msgid "Please, wait while the file is uploading."
msgstr "Por favor, aguarde, enquanto o ficheiro está a ser enviado."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
#: model:ir.model.fields,help:mail.field_res_users__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Política de publicação de mensagens no documento usando o mailgateway.\n"
"- todos: todos podem publicar\n"
"- parceiros: apenas parceiros autenticados\n"
"- seguidores: apenas seguidores do documento relacionado ou membros de canais seguidores\n"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Post your message on the thread"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "Desenvolvido por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Preferred reply address"
msgstr "Email de resposta preferido"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__reply_to
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid "Preferred response address (placeholders may be used here)"
msgstr ""
"Endereço de resposta preferido (espaços reservados podem ser usados ​​aqui)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Pré-visualizar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "Preview of"
msgstr "Antevisão de"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Previous"
msgstr "Anterior"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "Privacidade"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Private channel"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Public Channels"
msgstr "Canais Públicos"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Quick search..."
msgstr ""

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Re:"
msgstr "Re:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#, python-format
msgid "Read less"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/thread_window.js:0
#: code:addons/mail/static/src/xml/thread_window.xml:0
#, python-format
msgid "Read more"
msgstr "Ler mais"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Ready"
msgstr "Pronto"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Pronto para Envio"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Recebido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Received by Everyone"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Received by:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Destinatário"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Recipients"
msgstr "Destinatários"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__next_type_ids
msgid "Recommended Next Activities"
msgstr "Próximas Atividades Recomendadas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID da Linha de Registo"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "Registos:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Referências"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Reject"
msgstr "Rejeitar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Reject selected messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reject |"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__moderation_status__rejected
msgid "Rejected"
msgstr "Rejeitado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "ID Documentos Relacionados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__model
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Modelo de documento relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Mensagens Relactionadas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Parceiro Relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Campo relacionado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Remove message with explanation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Remove message without explanation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Remover a ação contextual para utilizar este modelo nos documentos "
"relacionados"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Remove this follower"
msgstr "Remover este Seguidor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Rename"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Rename conversation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Responder"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Endereço de ''e-mail'' de resposta. Definir o \"responder a\" ignora a "
"criação de linha automática."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply-To"
msgstr "Responder- Para"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__report_name
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "Relatório nome do ficheiro"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reset Zoom"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
msgid "Responsible"
msgstr "Responsável"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Utilizador responsável"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Tentar Novamente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "Conteúdo Rich-text"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Mensagem com formatação Rich-text/HTML"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Rotate"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "Servidor SMTP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Vendedor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__res_id
msgid "Sample Document"
msgstr "Amostra de Documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Save"
msgstr "Guardar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Salve como um novo Template"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "Gravar como novo modelo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/abstract_thread_window.js:0
#, python-format
msgid "Say something"
msgstr "Diga algo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Planear"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Schedule Activity"
msgstr "Agendar Atividade"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Schedule activity"
msgstr "Agendar Atividade"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "Agendar Uma Atividade"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "Agendar uma atividade"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
msgid "Scheduled Date"
msgstr "Data Agendada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "Apelido de Pesquisa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "Pesquisar grupos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_moderation_view_search
msgid "Search Moderation List"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Seen by Everyone"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Seen by:"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Select All"
msgstr "Selecionar Todos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Select all messages to moderate"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Selecione o campo de destino a partir do modelo do documento relacionado.\n"
"Se for um campo de relação será capaz de selecionar um campo de destino no destino do relacionamento."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__groups
msgid "Selected group of users"
msgstr "Grupo de utilizadores selecionado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:0
#: code:addons/mail/static/src/js/discuss.js:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Enviar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__email
msgid "Send Email"
msgstr "Enviar mensagem"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "Enviar o correio (%s)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Enviar Agora"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Send a message"
msgstr "Envia uma mensagem"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Send by mail"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Send explanation to author"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Send guidelines"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Send message"
msgstr "Enviar mensagem"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__email_send
msgid "Send messages by email"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__email_from
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Endereço do remetente (espaços reservados podem ser usados ​​aqui). Se não "
"for definido, o valor padrão será de alias de e-mail do autor, se "
"configurado, ou o endereço de e-mail."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:0
#, python-format
msgid "Sending Error"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Sends messages by email"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "Enviado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Ação de servidor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Shortcodes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Atalho"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "Show"
msgstr "Mostrar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Mostrar todos os registos cuja data de ação é anterior à atual"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Show an helper message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__ref_ir_act_window
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Ação da barra lateral"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__ref_ir_act_window
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Ação da barra lateral para tornar este template disponível no registo do "
"modelo de documento relacionado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_id
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_manager.js:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Favoritos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
msgid "State"
msgstr "Estado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_moderation__status
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Estado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estados baseados nas atividades\n"
"Vencida: Ultrapassada a data planeada\n"
"Hoje: Data da atividade é a de hoje\n"
"Planeada: Atividades futuras."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "SubCampo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "Submodelo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/discuss.xml:0
#: model:ir.model.fields,field_description:mail.field_email_template_preview__subject
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#, python-format
msgid "Subject"
msgstr "Assunto"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__subject
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "Assunto (espaços reservados podem ser usados ​​aqui)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "Assunto..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Subject:"
msgstr "Assunto:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Substituição"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Subtipo"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Subtipos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
msgid "Summary"
msgstr "Sumário"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
msgid "System notification"
msgstr "Notificação do sistema"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__can_write
msgid "Technical field to hide buttons if the current user has no access."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model_id
msgid ""
"Technical field to keep trace of the model at the beginning of the edition "
"for UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.wizard_email_template_preview
msgid "Template Preview"
msgstr "Pré-Visualização dos Templates"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__preview_lang
msgid "Template Preview Language"
msgstr "Idioma de Pré-visualização do Modelo"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Modelos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
msgid "Thank you!"
msgstr "Obrigado!"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "A"

#. module: mail
#: code:addons/mail/models/ir_actions.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "O valor do 'Vencimento em' não pode ser negativo."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_moderation_channel_email_uniq
msgid "The email address must be unique per channel !"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"O modelo (Odoo Document Kind) a qual esta alias corresponde. Qualquer email "
"de entrada que não responda a um registo existente criará um novo registo "
"deste modelo (Ex.: uma Tarefa de Projeto)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"O nome do alias de email, ex.: 'jobs' se quiser receber emails de "
"<<EMAIL>>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"O dono dos registos criados depois da receção de emails neste alias. Se este"
" campo não estiver ativo, o sistema vai tentar encontrar o dono correto com "
"base no endereço do remetente (De), ou então usa a conta de Administrador se"
" não for encontrado nenhum utilizador do sistema nesse endereço."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "The partner can not join this channel"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"A operação solicitada não pode ser completada devido a restrições de segurança. Por favor, contacte o seu administrador de sistemas.\n"
"\n"
"(Tipo de Documento: %s, Operação: %s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__model_id
#: model:ir.model.fields,help:mail.field_mail_template__model_id
msgid "The type of document this template can be used with"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "This action will send an email."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_address_mixin__email_normalized
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""
"Este grupo é visível por não membros. Grupos invisíveis podem adicionar "
"membros através do botão convidar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "This record has an exception activity."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Thread"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
msgid "To"
msgstr "Para"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Para (E-mails)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_email_template_preview__partner_to
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Para (Parceiros)"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "Tarefas a realizar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#: code:addons/mail/static/src/xml/thread_window.xml:0
#, python-format
msgid "To:"
msgstr "Para:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:0
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#, python-format
msgid "Today"
msgstr "Hoje"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Atividades do Dia"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Tomorrow"
msgstr "Amanhã"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Tópicos discutidos neste grupo..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_form
msgid "Tracking"
msgstr "Rastrear"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Valor de Monitorização"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Valores de Monitorização"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__tracking_sequence
msgid "Tracking field sequence"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Valores de monitorização"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__force_next
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__force_next
msgid "Trigger Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Tipo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_windows/abstract_thread_window.js:0
#, python-format
msgid "Undefined"
msgstr "Indefinido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Unfollow"
msgstr "Não Participar"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_alias_unique
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr ""
"Infelizmente este apelido de e-mail já está em uso, por favor escolha um "
"outro que seja exclusivo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Unit"
msgstr "Unidade"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "Erro desconhecido"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread
msgid "Unread Messages"
msgstr "Mensagens Por Ler"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contador de Mensagens Não Lidas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/abstract_thread_window.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
#, python-format
msgid "Unread messages"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Unselect All"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Unselect all messages to moderate"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Unstar all"
msgstr "Remover estrelas de todas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Unstar all messages"
msgstr "Remover estrelas de todas as mensagens"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "Unsubscribe"
msgstr "Cancelar Subscrição"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "Unsubscribed"
msgstr "Subscrição cancelada"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Unsupported search filter on moderation status"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
#, python-format
msgid "Upload Document"
msgstr "Carregar Documento"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Upload file"
msgstr "Carregar ficheiro"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Uploaded"
msgstr "Carregado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Uploading"
msgstr "A Enviar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/composers/basic_composer.js:0
#, python-format
msgid "Uploading error"
msgstr "Erro de envio"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "Usar o domínio activo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Usar modelo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use your own email servers"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Usando para ordenar subtipos."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "User"
msgstr "Utilizador"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User field name"
msgstr "Nome do Campo de Utilizador"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "User is idle"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "User is offline"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "User is online"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#: code:addons/mail/static/src/xml/thread_window.xml:0
#, python-format
msgid "User name"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "Utilizador:"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "Utilizadores"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Users in this channel: %s %s and you."
msgstr "Utilizadores neste canal: %s %s e você."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Video"
msgstr "Vídeo"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "Ver"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"View \"mail.mail_channel_send_guidelines\" was not found. No email has been "
"sent. Please contact an administrator to fix this issue."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "Ver %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Tipo de Vista"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "View all the attachments of the current record"
msgstr "Veja todos os anexo do registo atual"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Viewer"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#: code:addons/mail/static/src/xml/followers.xml:0
#, python-format
msgid "Warning"
msgstr "Aviso"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Semanas"

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Quando um campo de relacionamento é selecionado como primeiro campo, este "
"campo permite que selecione o campo de destino dentro do modelo de documento"
" de destino (sub-modelo)."

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Quando um campo de relacionamento é selecionado como primeiro campo, este "
"campo mostra o modelo de documento e a relação."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "Se a mensagem é uma nota interna (modo de comentário só )"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_activity
msgid "Whether this model supports activities."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_blacklist
msgid "Whether this model supports blacklist."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "Quem pode seguir as actividades do grupo?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Write Feedback"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/composer.xml:0
#, python-format
msgid "Write something..."
msgstr "Escreva algo..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:0
#, python-format
msgid "Yesterday"
msgstr "Ontem"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "You added <b>%s</b> to the conversation."
msgstr "Adicionou <b>%s</b> à conversa."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "You are going to ban: %s. Do you confirm the action?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "You are going to discard %s messages. Do you confirm the action?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid "You are going to discard 1 message. Do you confirm the action?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid ""
"You are going to send the guidelines to all the subscribers. Do you confirm "
"the action?"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "Está no canal <b>#%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/attachment_box.js:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/discuss.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to "
"unsubscribe?"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You can not remove this partner from this channel"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You can not write on the record of other users"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You can not write on this field"
msgstr ""

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""
"Você não pode criar um novo utilizador a partir daqui.\n"
" Para criar novo utilizador aceda á configuração do painel ."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Não pode eliminar esses grupos, porque o grupo 'Empresa Completa' é "
"necessário por outros módulos."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You do not have the rights to modify fields related to moderation on one of "
"the channels you are modifying."
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "You have been invited to: %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "You have no message to moderate"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/user_menu.xml:0
#, python-format
msgid "You have set a chat message."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_email_template_preview__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Pode anexar ficheiros a este template, para ser adicionado a todos os "
"e-mails criados a partir desse template"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "You unpinned your conversation with <b>%s</b>."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/services/mail_notification_manager.js:0
#, python-format
msgid "You unsubscribed from <b>%s</b>."
msgstr "Cancelou a subscrição de <b>%s</b>."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "You:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr "O Seu Registo de"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_field.js:0
#, python-format
msgid "Your message has not been sent."
msgstr "A sua mensagem não foi enviada."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Your message is pending moderation"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/discuss.xml:0
#, python-format
msgid "Your message was rejected by moderator."
msgstr "A sua mensagem foi rejeitada pelo moderador."

#. module: mail
#: code:addons/mail/controllers/home.py:0
#, python-format
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom In"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom Out"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "após a data limite da atividade anterior"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after validation date"
msgstr "após a data de validação"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %s: %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr "board-meetings"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "by"
msgstr "por"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_send_guidelines
msgid "channel."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "created"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "dias"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "document"
msgstr "documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "concluído"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "por exemplo, Proposta de discussão"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/followers.js:0
#, python-format
msgid "followers"
msgstr "Seguidores"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "for"
msgstr "por"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "from"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "from:"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "geral"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "less"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "modified"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "meses"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "more"
msgstr "mais"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "mycompany.odoo.com"
msgstr "mycompany.odoo.com"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:0
#, python-format
msgid "now"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "ativo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "or"
msgstr "ou"

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_widget.js:0
#, python-format
msgid "read less"
msgstr "ler menos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/thread_widget.js:0
#, python-format
msgid "read more"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_preview_form
msgid "record:"
msgstr "record:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "reply to missing document (%s,%s), fall back on document creation"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "restricted to channel members"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "restricted to followers"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "restricted to known authors"
msgstr ""

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "vendas"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/chatter.xml:0
#, python-format
msgid "this document"
msgstr "este documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/attachment_box.js:0
#: code:addons/mail/static/src/js/models/messages/abstract_message.js:0
#, python-format
msgid "unnamed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "semanas"
