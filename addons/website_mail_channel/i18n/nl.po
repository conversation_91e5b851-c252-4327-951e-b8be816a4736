# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_mail_channel
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:16+0000\n"
"Last-Translator: <PERSON>, 2020\n"
"Language-Team: Dutch (https://www.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid "- <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "- <i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Datum\" title=\"Datum\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid ""
"- <i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Attachments\" "
"title=\"Attachments\"/>"
msgstr ""
"- <i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Bijlagen\" "
"title=\"Bijlagen\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
msgid ""
"<i class=\"fa fa-arrow-left\" role=\"img\" aria-label=\"Previous message\" "
"title=\"Previous message\"/>"
msgstr ""
"<i class=\"fa fa-arrow-left\" role=\"img\" aria-label=\"Vorig bericht\" "
"title=\"Vorig bericht\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
msgid ""
"<i class=\"fa fa-arrow-right\" role=\"img\" aria-label=\"Next message\" "
"title=\"Next message\"/>"
msgstr ""
"<i class=\"fa fa-arrow-right\" role=\"img\" aria-label=\"Volgende bericht\" "
"title=\"Volgende bericht\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
msgid ""
"<i class=\"fa fa-chevron-down\" role=\"img\" aria-label=\"Show attachments\""
" title=\"Show attachments\"/>"
msgstr ""
"<i class=\"fa fa-chevron-down\" role=\"img\" aria-label=\"Bijlagen "
"weergeven\" title=\"Bijlagen weergeven\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid ""
"<i class=\"fa fa-chevron-down\" role=\"img\" aria-label=\"Show replies\" "
"title=\"Show replies\"/>"
msgstr ""
"<i class=\"fa fa-chevron-down\" role=\"img\" aria-label=\"Reactie "
"weergeven\" title=\"Reactie weergeven\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
msgid ""
"<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Hide "
"attachments\" title=\"Hide attachments\"/>"
msgstr ""
"<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Bijlagen "
"verbergen\" title=\"Bijlagen verbergen\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid ""
"<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Hide replies\" "
"title=\"Hide replies\"/>"
msgstr ""
"<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Reacties "
"verbergen\" title=\"Reacties verbergen\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_messages
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
#: model_terms:ir.ui.view,arch_db:website_mail_channel.subscribe
msgid "<i class=\"fa fa-envelope-o\"/> send mail"
msgstr "<i class=\"fa fa-envelope-o\"/>Verzend e-mail"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
#: model_terms:ir.ui.view,arch_db:website_mail_channel.subscribe
msgid "<i class=\"fa fa-file-o\"/> archives"
msgstr "<i class=\"fa fa-file-o\"/> archieven"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
msgid ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"Recipients\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Ontvangers\" "
"title=\"Ontvangers\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
#: model_terms:ir.ui.view,arch_db:website_mail_channel.subscribe
msgid "<i class=\"fa fa-times\"/> unsubscribe"
msgstr "<i class=\"fa fa-times\"/> uitschrijven"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.subscribe
msgid "<span class=\"oe_snippet_thumbnail_title\">Discussion Group</span>"
msgstr "<span class=\"oe_snippet_thumbnail_title\">Discussiegroep</span>"

#. module: website_mail_channel
#: model:mail.template,body_html:website_mail_channel.mail_template_list_subscribe
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Channel</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">${object.name}</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${user.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px;\">\n"
"                        Hello,<br/><br/>\n"
"                        You have requested to be subscribed to the mailing list <strong>${object.name}</strong>.\n"
"                        <br/><br/>\n"
"                        To confirm, please visit the following link: <strong><a href=\"${ctx['token_url']}\">${ctx['token_url']}</a></strong>\n"
"                        <br/><br/>\n"
"                        If this was a mistake or you did not requested this action, please ignore this message.\n"
"                        % if user.signature\n"
"                        <br/>\n"
"                        ${user.signature | safe}\n"
"                        % endif\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                        ${user.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    % if user.company_id.phone\n"
"                        ${user.company_id.phone} |\n"
"                    %endif\n"
"                    % if user.company_id.email\n"
"                        <a href=\"'mailto:%s' % ${user.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.email}</a> |\n"
"                    % endif\n"
"                    % if user.company_id.website\n"
"                        <a href=\"'%s' % ${user.company_id.website}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.website}\n"
"                        </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Jouw kanaal</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">${object.name}</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${user.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px;\">\n"
"                        Hallo,<br/><br/>\n"
"                        U hebt gevraagd om geabonneerd te zijn op de mailinglijst <strong>${object.name}</strong>.\n"
"                        <br/><br/>\n"
"                        Om te bevestigen bezoekt u de volgende link: <strong><a href=\"${ctx['token_url']}\">${ctx['token_url']}</a></strong>\n"
"                        <br/><br/>\n"
"                        Als dit een vergissing was of u deze actie niet heeft aangevraagd, negeert u dit bericht.\n"
"                        % if user.signature\n"
"                        <br/>\n"
"                        ${user.signature | safe}\n"
"                        % endif\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                        ${user.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    % if user.company_id.phone\n"
"                        ${user.company_id.phone} |\n"
"                    %endif\n"
"                    % if user.company_id.email\n"
"                        <a href=\"'mailto:%s' % ${user.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.email}</a> |\n"
"                    % endif\n"
"                    % if user.company_id.website\n"
"                        <a href=\"'%s' % ${user.company_id.website}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.website}\n"
"                        </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Aangeboden door <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: website_mail_channel
#: model:mail.template,body_html:website_mail_channel.mail_template_list_unsubscribe
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Channel</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">${object.name}</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${user.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px;\">\n"
"                        Hello,<br/><br/>\n"
"                        You have requested to be unsubscribed to the mailing list <strong>${object.name}</strong>.\n"
"                        <br/><br/>\n"
"                        To confirm, please visit the following link: <strong><a href=\"${ctx['token_url']}\">${ctx['token_url']}</a></strong>.\n"
"                        <br/><br/>\n"
"                        If this was a mistake or you did not requested this action, please ignore this message.\n"
"                        % if user.signature:\n"
"                        <br/>\n"
"                        ${user.signature | safe}\n"
"                        % endif\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                        ${user.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    % if user.company_id.phone\n"
"                        ${user.company_id.phone} |\n"
"                    %endif\n"
"                    % if user.company_id.email\n"
"                        <a href=\"'mailto:%s' % ${user.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.email}</a> |\n"
"                    % endif\n"
"                    % if user.company_id.website\n"
"                        <a href=\"'%s' % ${user.company_id.website}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.website}\n"
"                        </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Jouw kanaal</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">${object.name}</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${user.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px;\">\n"
"                        Hallo,<br/><br/>\n"
"                        U hebt gevraagd om geabonneerd te zijn op de mailinglijst <strong>${object.name}</strong>.\n"
"                        <br/><br/>\n"
"                        Om te bevestigen bezoekt u de volgende link: <strong><a href=\"${ctx['token_url']}\">${ctx['token_url']}</a></strong>.\n"
"                        <br/><br/>\n"
"                        Als dit een vergissing was of u deze actie niet heeft aangevraagd, negeert u dit bericht.\n"
"                        % if user.signature:\n"
"                        <br/>\n"
"                        ${user.signature | safe}\n"
"                        % endif\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                        ${user.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    % if user.company_id.phone\n"
"                        ${user.company_id.phone} |\n"
"                    %endif\n"
"                    % if user.company_id.email\n"
"                        <a href=\"'mailto:%s' % ${user.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.email}</a> |\n"
"                    % endif\n"
"                    % if user.company_id.website\n"
"                        <a href=\"'%s' % ${user.company_id.website}\" style=\"text-decoration:none; color: #454748;\">${user.company_id.website}\n"
"                        </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Aangeboden door<a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: website_mail_channel
#. openerp-web
#: code:addons/website_mail_channel/static/src/js/website_mail_channel.editor.js:15
#, python-format
msgid "Add a Subscribe Button"
msgstr "Voeg een aanmeldingsknop toe"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
msgid "Alone we can do so little, together we can do so much"
msgstr "Alleen bereiken we zo weinig, samen bereiken we zo veel"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_messages
msgid "Archives"
msgstr "Archieven"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid "Avatar"
msgstr "Avatar"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
msgid "Browse archives"
msgstr "Door archief bladeren"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_messages
msgid "By date"
msgstr "Op datum"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_messages
msgid "By thread"
msgstr "Op discussie"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.subscribe
msgid "Change Discussion List"
msgstr "Wijzig discussie lijst"

#. module: website_mail_channel
#: model:mail.template,subject:website_mail_channel.mail_template_list_subscribe
msgid "Confirm subscription to ${object.name}"
msgstr "Bevestig abonnement op ${object.name}"

#. module: website_mail_channel
#: model:mail.template,subject:website_mail_channel.mail_template_list_unsubscribe
msgid "Confirm unsubscription to ${object.name}"
msgstr "Bevestig uitschrijving op ${object.name}"

#. module: website_mail_channel
#: model:ir.model,name:website_mail_channel.model_mail_channel
msgid "Discussion Channel"
msgstr "Discussiekanaal"

#. module: website_mail_channel
#. openerp-web
#: code:addons/website_mail_channel/static/src/js/website_mail_channel.editor.js:16
#, python-format
msgid "Discussion List"
msgstr "Discussielijst"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
msgid "Follow-Ups"
msgstr "Opvolgingen"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
msgid "Group"
msgstr "Groep"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.invalid_token_subscription
msgid "Invalid or expired confirmation link."
msgstr "Ongeldige of verlopen bevesttigingslink."

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_messages
#: model:website.menu,name:website_mail_channel.menu_mailing_list
msgid "Mailing Lists"
msgstr "Mailinglijsten"

#. module: website_mail_channel
#: code:addons/website_mail_channel/models/mail_mail.py:20
#, python-format
msgid "Mailing-List"
msgstr "Mailinglijst"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
msgid ""
"Need to unsubscribe? It's right here! <span class=\"fa fa-2x fa-arrow-down "
"float-right\" role=\"img\" aria-label=\"\" title=\"Read this !\"/>"
msgstr ""
"Wilt u zich uitschrijven? Dat kan hier! <span class=\"fa fa-2x fa-arrow-down"
" float-right\" role=\"img\" aria-label=\"\" title=\"Lees dit!\"/>"

#. module: website_mail_channel
#: model:ir.model,name:website_mail_channel.model_mail_mail
msgid "Outgoing Mails"
msgstr "Uitgaande e-mails"

#. module: website_mail_channel
#: code:addons/website_mail_channel/models/mail_mail.py:21
#, python-format
msgid "Post to"
msgstr "Versturen naar"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
msgid "Reference"
msgstr "Referentie"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
msgid "Stay in touch with our Community"
msgstr "Blijf op de hoogte van onze community"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
#: model_terms:ir.ui.view,arch_db:website_mail_channel.subscribe
msgid "Subscribe"
msgstr "Inschrijven"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.not_subscribed
msgid "The address"
msgstr "Het adres"

#. module: website_mail_channel
#: code:addons/website_mail_channel/controllers/main.py:245
#, python-format
msgid ""
"The address %s is already unsubscribed or was never subscribed to any "
"mailing list"
msgstr ""
"Het adres %s is is al uitgeschreven of was nooit ingeschreven voor een "
"mailinglijst"

#. module: website_mail_channel
#: code:addons/website_mail_channel/models/mail_mail.py:22
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
#, python-format
msgid "Unsubscribe"
msgstr "Uitschrijven"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.confirmation_subscription
msgid "You have been correctly"
msgstr "U bent correct"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.subscribe
msgid "a confirmation email has been sent."
msgstr "een bevestigingsmail  is verzonden."

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
msgid "attachments"
msgstr "Bijlages"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid "by"
msgstr "door"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.not_subscribed
msgid ""
"is already\n"
"                    unsubscribed or was never subscribed to the mailing\n"
"                    list, you may want to check that the address was\n"
"                    correct."
msgstr ""
"Is reeds uitgeschreven of was nooit ingeschreven voor de mailinglijst. "
"Controleer of het adres juist is."

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_message
#: model_terms:ir.ui.view,arch_db:website_mail_channel.group_messages
msgid "mailing list archives"
msgstr "mailinglijst archieven"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
msgid ""
"members<br/>\n"
"                    <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"Traffic\"/>"
msgstr ""
"leden<br/>\n"
"                    <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Verkeer\" title=\"Verkeer\"/>"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
msgid "messages / month"
msgstr "berichten / maand"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid "more replies"
msgstr "meer reacties"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid "replies"
msgstr "reacties"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.messages_short
msgid "show"
msgstr "toon"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.confirmation_subscription
msgid "subscribed"
msgstr "ingeschreven"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.confirmation_subscription
msgid "to the mailing list."
msgstr "aan de mailinglijst."

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.confirmation_subscription
msgid "unsubscribed"
msgstr "uitgeschreven"

#. module: website_mail_channel
#: model_terms:ir.ui.view,arch_db:website_mail_channel.mail_channels
#: model_terms:ir.ui.view,arch_db:website_mail_channel.subscribe
msgid "your email..."
msgstr "Uw e-mail..."
