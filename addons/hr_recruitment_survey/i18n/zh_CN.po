# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_survey
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:12+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">Print</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""
"<span class=\"o_stat_text\">打印</span>\n"
"                        <span class=\"o_stat_text\">面谈</span>"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">Start</span>\n"
"                        <span class=\"o_stat_text\">Interview</span>"
msgstr ""
"<span class=\"o_stat_text\">开始</span>\n"
"                        <span class=\"o_stat_text\">面谈</span>"

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1
msgid "About you"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid "Activities"
msgstr "活动"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "Answer related job question"
msgstr "回答有关就业问题"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_applicant
msgid "Applicant"
msgstr "申请人"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_survey_survey__category
msgid "Category"
msgstr "类别"

#. module: hr_recruitment_survey
#: model:ir.model.fields,help:hr_recruitment_survey.field_survey_survey__category
msgid ""
"Category is used to know in which context the survey is used. Various apps "
"may define their own categories when they use survey like jobs recruitment "
"or employee appraisal surveys."
msgstr "类别用于了解在何种上下文中使用了调查。不同的应用程序在使用招聘或员工评估调查等调查时，可能会定义自己的类别。"

#. module: hr_recruitment_survey
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_applicant__survey_id
#: model:ir.model.fields,help:hr_recruitment_survey.field_hr_job__survey_id
msgid ""
"Choose an interview form for this job position and you will be able to "
"print/answer this interview from all applicants who apply for this job"
msgstr "选择一个此岗位的面试表格，你可以把它打印出来让所有此岗位的应聘者回答这个表格的问题"

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q3
msgid "Did you apply from an employee ?"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_job_survey_inherit
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Display Interview Form"
msgstr "显示面试界面"

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q4
msgid "Education"
msgstr "教育"

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q2
msgid "From which university did or will you graduate ?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row2
msgid "Getting on with colleagues"
msgstr "与同事相处"

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row8
msgid "Getting perks such as free parking, gym passes"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row1
msgid "Having a good pay"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row3
msgid "Having a nice office environment"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row7
msgid "Having freebies such as tea, coffee and stationery"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,comments_message:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "If other, please specify:"
msgstr "如果有其它，请指定"

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col2
msgid "Important"
msgstr "重要"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_job__survey_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "Interview Form"
msgstr "面试表单"

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.res_config_settings_view_form
msgid "Interview Forms"
msgstr "面试表单"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_hr_job
msgid "Job Position"
msgstr "工作岗位"

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "Knowledge"
msgstr "知识"

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row6
msgid "Management quality"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.view_hr_job_kanban_inherit
msgid "No Interview Form"
msgstr "没有面试表格"

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col1
msgid "Not important"
msgstr "不重要的"

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row5
msgid "Office location"
msgstr "办公地点"

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid "Past work experiences"
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.survey,description:hr_recruitment_survey.survey_recruitment_form
msgid ""
"Please answer those questions to help recruitment officers to preprocess "
"your application."
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1
msgid ""
"Please fill information about you: who you are, what are your education, experience, and activities.\n"
"    It will help us managing your application."
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q5
msgid ""
"Please summarize your education history: schools, location, diplomas, ..."
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q7
msgid ""
"Please tell us a bit more about yourself: what are your main activities, ..."
msgstr ""

#. module: hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment_survey.hr_applicant_view_form_inherit
msgid "Print interview report"
msgstr "打印面试表单"

#. module: hr_recruitment_survey
#: model:ir.model.fields.selection,name:hr_recruitment_survey.selection__survey_survey__category__hr_recruitment
msgid "Recruitment"
msgstr "招聘"

#. module: hr_recruitment_survey
#: model:survey.survey,title:hr_recruitment_survey.survey_recruitment_form
msgid "Recruitment Form"
msgstr "招聘表格"

#. module: hr_recruitment_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__response_id
msgid "Response"
msgstr "回复"

#. module: hr_recruitment_survey
#: model:ir.model,name:hr_recruitment_survey.model_survey_survey
#: model:ir.model.fields,field_description:hr_recruitment_survey.field_hr_applicant__survey_id
msgid "Survey"
msgstr "问卷"

#. module: hr_recruitment_survey
#: model_terms:survey.survey,thank_you_message:hr_recruitment_survey.survey_recruitment_form
msgid "Thank you for answering this survey. We will come back to you soon."
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,validation_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "The answer you entered is not valid."
msgstr "您输入的答案无效。"

#. module: hr_recruitment_survey
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q2
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q3
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q4
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q5
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q6
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q7
#: model:survey.question,constr_error_msg:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "This question requires an answer."
msgstr "此问题需要答案。"

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_col3
msgid "Very important"
msgstr "非常重要"

#. module: hr_recruitment_survey
#: model_terms:survey.question,description:hr_recruitment_survey.survey_recruitment_form_p1_q6
msgid "What are your main knowledge regarding the job you are applying to ?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1_q8
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q8
msgid "What is important for you ?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.question,question:hr_recruitment_survey.survey_recruitment_form_p1_q1
#: model:survey.question,title:hr_recruitment_survey.survey_recruitment_form_p1_q1
msgid "Which country are you from ?"
msgstr ""

#. module: hr_recruitment_survey
#: model:survey.label,value:hr_recruitment_survey.survey_recruitment_form_p1_q8_row4
msgid "Working with state of the art technology"
msgstr ""
