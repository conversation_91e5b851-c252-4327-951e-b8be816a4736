# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:12+0000\n"
"PO-Revision-Date: 2019-10-07 07:12+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Apply"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_availability
msgid "Available Seat"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_available
msgid "Available Seats"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before confirming"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__campaign_id
msgid "Campaign"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Cancel"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__company_id
msgid "Company"
msgstr ""

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Configure an event"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket__seats_max
msgid ""
"Define the number of available tickets. If you have too much registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
msgid "Event"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_type
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__event_type_id
msgid "Event Category"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_form_view
msgid "Event Name"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:product.product,name:event_sale.product_product_event
#: model:product.template,name:event_sale.product_product_event_product_template
msgid "Event Registration"
msgstr ""

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event__event_ticket_ids
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_event_registration__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
msgid "Event Ticket"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_form_view
msgid "Event's Ticket"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_sale_product_template_form
msgid "Events"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "Free"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_4_ticket_1
msgid "General Admission"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product__event_ok
#: model:ir.model.fields,help:event_sale.field_product_template__event_ok
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ok
msgid ""
"If checked this product automatically creates an event registration at the "
"sales order confirmation."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__is_expired
msgid "Is Expired"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__event_ok
#: model:ir.model.fields,field_description:event_sale.field_product_template__event_ok
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ok
msgid "Is an Event Ticket"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_account_move
msgid "Journal Entries"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator____last_update
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line____last_update
msgid "Last Modified on"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_event_ticket__seats_availability__limited
msgid "Limited"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_form
msgid "Marketing"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_max
msgid "Maximum Available Seats"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__medium_id
msgid "Medium"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__mobile
msgid "Mobile"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
#, python-format
msgid "Name"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "No more available seats for this ticket"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "No more available seats for this ticket type."
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "None"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Ok"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_form
msgid "Origin"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "Paid"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "Payment"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price
msgid "Price"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce
msgid "Price Reduce"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__product_id
msgid "Product"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
msgid "Product Template"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#: code:addons/event_sale/models/event.py:0
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
#, python-format
msgid "Registration"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "Registration for %s"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__registration_ids
msgid "Registrations"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_reserved
msgid "Reserved Seats"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__deadline
msgid "Sales End"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
msgid "Sales Order"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_used
msgid "Seats Used"
msgstr ""

#. module: event_sale
#: model:event.type,name:event_sale.event_type_data_sale
msgid "Sell Online"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Skip"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__source_id
msgid "Source"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
msgid "Source Sales Order"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_0_ticket_1
#: model:event.event.ticket,name:event_sale.event_2_ticket_1
msgid "Standard"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "The registration must be paid"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_registration__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_registration__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_registration__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "Ticket"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_search
msgid "Ticket Type"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "Ticket cannot belong to both the event category and the event itself."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_type__use_ticketing
msgid "Ticketing"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_type__event_ticket_ids
#: model_terms:ir.ui.view,arch_db:event_sale.event_type_view_form_inherit_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Tickets"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:0
#, python-format
msgid "To pay"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr ""

#. module: event_sale
#: model:product.product,uom_name:event_sale.product_product_event
#: model:product.template,uom_name:event_sale.product_product_event_product_template
msgid "Units"
msgstr ""

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_event_ticket__seats_availability__unlimited
msgid "Unlimited"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_0_ticket_2
#: model:event.event.ticket,name:event_sale.event_2_ticket_2
msgid "VIP"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give details about the registrations"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "reserved +"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "unconfirmed"
msgstr ""
