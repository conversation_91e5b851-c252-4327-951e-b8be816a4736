# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <ma<PERSON><EMAIL>>, 2020
# 61590936fa9bf290362ee306eeabf363_944dd10 <a8bfd5a0b49b9c8455f33fc521764cc3_680674>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 10:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: 61590936fa9bf290362ee306eeabf363_944dd10 <a8bfd5a0b49b9c8455f33fc521764cc3_680674>, 2021\n"
"Language-Team: Basque (https://www.transifex.com/odoo/teams/41243/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid " [Me]"
msgstr " [Me]"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid " and "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#, python-format
msgid " or "
msgstr "edo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr "erregistroak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "%(field)s %(operator)s"
msgstr "%(field)s %(operator)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr "%(field)s %(operator)s \"%(value)s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "Duela %d egun"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "Duela %d ordu "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "Duela %d minutu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "Duela %d hilabete"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "Duela %d urte"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid ""
"&lt;!--[if lt IE 10]&gt;\n"
"                        &lt;body class=\"ie9\"&gt;\n"
"                    &lt;![endif]--&gt;"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/notification.xml:0
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' ez da data zuzena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' ez da data edo ordu zuzena "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' ez da zenbaki oso zuzena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "(change)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(no string)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(etiketarik ez)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "1 record"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-at\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Fiscal number\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-globe\" role=\"img\" aria-label=\"Website\" title=\"Website\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid "<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "<span>Copyright &amp;copy;</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "A name for your favorite is required."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"A popup window with your report was blocked. You may need to change your "
"browser settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Access Denied"
msgstr "Sarbidea ukatua"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Access Error"
msgstr "Sarbide errorea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Konpainiaren aplikazioetarako sarbidea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:0
#, python-format
msgid "Action"
msgstr "Ekintza"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Action ID:"
msgstr "Ekintzaren ID:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Activate Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add"
msgstr "Gehitu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Add %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "Gehitu iragazki pertsonalizatua"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add Custom Group"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add a Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add a condition"
msgstr "Baldintza bat gehitu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Add a line"
msgstr "Gehitu lerroa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Add column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Add..."
msgstr "Gehitu.."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "Gehitu:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Adopt and Sign"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Advanced Search..."
msgstr "Bilaketa aurreratua..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "Alerta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "All"
msgstr "Denak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "All day"
msgstr "Egun osoa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "Erabiltzaile guztiak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Alt"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Among the"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "An error occurred"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid ""
"An unknown CORS error occured. The error probably originates from a "
"JavaScript file served from a different origin. (Opening your browser "
"console might give you a hint on the error.)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "And more"
msgstr "eta gehiago"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Any"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Apply"
msgstr "Aplikatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "Artxibo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Archive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "Ziur zutabe hau ezabatu nahi duzula?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "Ziur iragazki hau kendu nahi duzula?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Ziur zaude erregistro hau ezabatu nahi duzula ?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Attach"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Attachment"
msgstr "Eranskin "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Attachment :"
msgstr "Eranskina:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr "Automatiko "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Available fields"
msgstr "Eremu eskuragarriak "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Avatar"
msgstr "Avatarra"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Badges"
msgstr "Intsigniak "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Bar Chart"
msgstr "Diagrama grafikoa"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "Oinarri"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Based On"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Become Superuser"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Binary file"
msgstr "Fitxategi bitarra"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Blue"
msgstr "Urdina"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Button"
msgstr "Botoia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Button Type:"
msgstr "Botoi mota:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "CLEAR"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar"
msgstr "Egutegia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Calendar toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr "Egutegi ikuspegiak ez du definitu 'date_start'-ezaugarririk."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Cancel"
msgstr "Ezeztatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Cannot render chart with mode : "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr "Txartelaren kolorea: %s"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/widgets/change_password.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change Password"
msgstr "Pasahitza Aldatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change default:"
msgstr "Lehenetsitakoak aldatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Change graph"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Checkbox"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Checkboxes"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#, python-format
msgid "Choose"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Clear"
msgstr "Garbitu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Clear Events"
msgstr "Gertakizunak garbitu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr "Sinadura garbia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/dialog.xml:0
#: code:addons/web/static/src/xml/notification.xml:0
#, python-format
msgid "Close"
msgstr "Itxi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Column title"
msgstr "Zutabe izenburua"

#. module: web
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr "Enpresak"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "Enpresaren izena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Compare To"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "Baldintza:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Confirm New Password"
msgstr "Pasahitz berria baieztatu "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Baieztapena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Connection lost"
msgstr "Konexioa galdu da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Connection restored"
msgstr "Konexioa leheneratu da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Context:"
msgstr "Testuingurua:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Control"
msgstr "Kontrola"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Control panel toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Copied !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Copy Text"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Could not display the selected image."
msgstr "Aukeratutako irudia ezin izan da bistaratu."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Count"
msgstr "Kontua "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Create"
msgstr "Sortu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Sortu \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create a %s"
msgstr "%s sortu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Create a new record"
msgstr "Erregistro berria sortu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "Sortu eta editatu..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create and edit"
msgstr "Sortu eta editatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Create: "
msgstr "Sortu:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Create: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Created by :"
msgstr "Nork sortua:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Creation Date:"
msgstr "Sortze data:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Creation User:"
msgstr "Sortze erabiltzailea:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr "Uneko egoera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Dark blue"
msgstr "Urdin iluna"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Dark purple"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Datu-basea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Date"
msgstr "Data"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Date & Time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Day"
msgstr "Eguna"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.debug_icon
msgid ""
"Debug mode is activated#{debug_mode_help}. Click here to exit debug mode."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Decimal"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Default:"
msgstr "Lehenetsia:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Delete"
msgstr "Ezabatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Delete this attachment"
msgstr "Eranskin hau ezabatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Description"
msgstr "Deskribapena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Discard"
msgstr "Baztertu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Discard a record modification"
msgstr "Erregistroaren aldaketa baztertu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Do you really want to delete this filter from favorites ?"
msgstr "Gustokoenetatik ezabatu nahi duzu iragazki hau?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Documentation"
msgstr "Dokumentazioa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain"
msgstr "Domeinua"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Domain:"
msgstr "Domeinua:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "Ez utzi orain, <br /> oraindik kargatzen ari da..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Download"
msgstr "Deskargatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Download xls"
msgstr "Deskargatu xls"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr "Marraztu "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Goitibeherako menua"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "Bikoiztu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "Editatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit Action"
msgstr "Ekintza editatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "Zutabea editatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit ControlPanelView"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Edit Domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Edit Stage"
msgstr "Etapa editatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Edit View:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Edit a record"
msgstr "Editatu erregistroa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "Eposta"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Email:"
msgstr "Email:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "Error"
msgstr "Akatsa"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Error, password not changed !"
msgstr "Errorea, pasahitza ez da aldatu!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Esc to discard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "Denon egutegia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Expand all"
msgstr "Zabaldu dena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Export"
msgstr "Esportatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Export All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Export Data"
msgstr "Datuak Esportatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Export Format:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "External ID"
msgstr "Kanpo ID"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "External link"
msgstr "Kanpoko Lotura"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "FILTER"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/control_panel_model.js:0
#, python-format
msgid "Failed to evaluate search criterions"
msgstr "Ezin izan dira irizpideak aurkitu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "Okerra"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid "Favorites"
msgstr "Gustukoenak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Field:"
msgstr "Eremua:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Fields View Get"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Fields to export"
msgstr "Esportatzeko eremuak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File"
msgstr "Fitxategia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File Upload"
msgstr "Fitxategi igoera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "Fitxategi igoera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Filter on: %s"
msgstr "Iragazkia: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/add_new_favorite_menu.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "Izen bereko iragazkia existitzen da jada."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/filter_menu.js:0
#, python-format
msgid "Filters"
msgstr "Iragazkiak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Flip axis"
msgstr "Irauli ardatza"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Fold"
msgstr "Tolestu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_controller.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 256 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "Formularioa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "Izen Osoa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Fushia"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "Lortu funtzio hau eta askoz gehiago Odoo Enterprise-rekin!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Global Business Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Got it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "Grafo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Green"
msgstr "Berdea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/groupby_menu.js:0
#, python-format
msgid "Group By"
msgstr "Taldekatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Group by: %s"
msgstr "Taldekatu: %s"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP bideratzea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Handle"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Hue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Image"
msgstr "Irudia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Integer"
msgstr "Zenbaki oso"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Invalid data"
msgstr "Baliogabeko data"

#. module: web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""
"Datu-basearen izena baliogabea da. Karaktere alfanumerikoak, azpi-marra, "
"marratxoa eta puntua soilik onartzen dira."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Invalid domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Invalid inherit mode. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Invalid mode for chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "JS Tests"
msgstr "JS probak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/user_menu.js:0
#, python-format
msgid "Keyboard Shortcuts"
msgstr "Teklatuko lasterbideak "

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Languages"
msgstr "Hizkuntzak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 30 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 365 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 5 Years"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last 7 Days"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Month"
msgstr "Aurreko hilabetea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Week"
msgstr "Iragan astea "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Last Year"
msgstr "Iragan urtea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "Azkeneko aldaketa data:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "Utzi garatzaileen tresnak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Light blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Lightness %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Line Chart"
msgstr "Lerrodun grafikoa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "Zerrenda"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "Kargatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Load more... ("
msgstr "Gehiago kargatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:0
#, python-format
msgid "Loading"
msgstr "Kargatzen"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:0
#, python-format
msgid "Loading (%d)"
msgstr "Kargatzen (%d)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Loading..."
msgstr "Kargatzen..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Hasi saioa"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Log out"
msgstr "Amaitu saioa"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Logoa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Mac"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
msgid "Mail:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Main actions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Manage Attachments"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Datubaseak kudeatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Manage Filters"
msgstr "Iragazkiak kudeatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Many2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Many2one"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Measures"
msgstr "Neurriak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Medium blue"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Metadata (%s)"
msgstr "Metadatuak ( %s )"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Method:"
msgstr "Metodoa:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Missing Record"
msgstr "Erregistro falta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Mobile support"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Modified by :"
msgstr "Nork aldatua:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Modifiers:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Module %s not loaded or inexistent, or templates of addon being loaded (%s) "
"are misordered"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Monetary"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Month"
msgstr "Hilabetea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "More"
msgstr "Gehiago"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Multiline Text"
msgstr "Lerro anitzeko testua "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "My Odoo.com account"
msgstr "Nire Odoo.com kontua"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Name:"
msgstr "Izena:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_model.js:0
#, python-format
msgid "New"
msgstr "Berria"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New Password"
msgstr "Pasahitz Berria"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New design"
msgstr "Diseinu berria"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "New template"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Next"
msgstr "Hurrengoa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "No"
msgstr "Ez"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "No Update:"
msgstr "Ez eguneratua:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "No attachment available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "No color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "No data to display"
msgstr "Ez dago bistaratzeko daturik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "No match found."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "No metadata available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "No records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "No results to show..."
msgstr "Ez dago emaitzarik erakusteko..."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "No template found to inherit from. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "No valid record to save"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "Node [%s] is not a JSONified XML node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "None"
msgstr "Hutsa"

#. module: web
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Object:"
msgstr "Objektu:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Odoo-ko aplikazioak laster erabilgarri egongo dira"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Client Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo enpresa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Odoo Warning"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/core/dialog.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Ok"
msgstr "Ados"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Old Password"
msgstr "Pasahitz zaharra "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "Aldaketak:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Only Integer or Float Value should be valid."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Only employee can access this database. Please contact the administrator."
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "Zuk bakarrik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Opacity %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open Developer Tools"
msgstr "Ireki garapen tresnak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open Developer Tools#{widget.debug_mode_help}"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Open View"
msgstr "Ireki ikuspegia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open the next record"
msgstr "Ireki hurrengo erregistroa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open the previous record"
msgstr "Ireki aurreko erregistroa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open to kanban view"
msgstr "Ireki kanban ikuspegia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Open to list view"
msgstr "Ikusi zerrenda ikuspegia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "Ireki:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Orange"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column_progressbar.js:0
#, python-format
msgid "Other"
msgstr "Beste"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "PDF Viewer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Pasahitza"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage"
msgstr "Percentage"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Phone"
msgstr "Telefonoa"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Phone:"
msgstr "Telefonoa:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/colorpicker_dialog.js:0
#, python-format
msgid "Pick a color"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pie Chart"
msgstr "Grafiko biribila"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""
"Grafiko biribilean ezin dira zero zenbakiak bistaratu. Saiatu zure domeinua "
"aldatzen emaitza positiboak erakusteko."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"Grafiko biribilean ezin dira zenbaki positiboak eta negatiboak nahastu. "
"Saiatu zure domeinua aldatzen emaitza positiboak bakarrik erakusteko. "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "Pibote"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Pivot settings"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first."
msgstr "Lehenik, sakatu \"gorde\" botoian mesedez."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please enter save field list name"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "Mesedez, esportatzeko fitxategiak aukeratu..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "Mesedez, esportazioen zerrendan gordetzeko fitxategiak aukeratu..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Preferences"
msgstr "Lehentasunak"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr "Kanpoko txostenaren aurreikuspena"

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr "Barne txostenaren aurreikuspena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Previous"
msgstr "Aurrekoa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Previous Period"
msgstr "Aurreko denboraldia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Previous Year"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:0
#: code:addons/web/static/src/xml/report.xml:0
#, python-format
msgid "Print"
msgstr "Inprimatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Priority"
msgstr "Lehentasuna"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Progress Bar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Purple"
msgstr "Morea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q1"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q2"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q3"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Q4"
msgstr ""

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Quarter"
msgstr "Hiruhileko "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Quick add"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Radio"
msgstr "Irratia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Range"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Red"
msgstr "Gorria"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid "Relation not allowed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Relation:"
msgstr "Erlazioa: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Remove"
msgstr "Ezabatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Remove Cover Image"
msgstr "Kendu azaleko irudia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:0
#, python-format
msgid "Remove field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "Ezabatu gogoko hau zerrendatik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid "Report"
msgstr "Txostena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Request timeout"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Run JS Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "SEE RESULT"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "SIGNATURE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Salmon pink"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/colorpicker_dialog.xml:0
#, python-format
msgid "Saturation %"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save"
msgstr "Gorde"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Save & Close"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#, python-format
msgid "Save & New"
msgstr "Gorde eta berria"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Save As..."
msgstr "Gorde honela..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save Current Search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Save a record"
msgstr "Erregistroa gorde"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Save default"
msgstr "Gorde lehenetsia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Search"
msgstr "Bilatu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr "Bilatu %(field)s hemen: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr "Bilatu %(field)s: %(value)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Search More..."
msgstr "Bilatu gehiago..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Search..."
msgstr "Bilatu..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Search: "
msgstr "Bilatu:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/crash_manager.xml:0
#, python-format
msgid "See details"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "See examples"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/js/views/view_dialogs.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select"
msgstr "Aukeratu "

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr "Sinadura estiloa aukeratu "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Select a view"
msgstr "Aukeratu ikuspegia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Select field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Selected records"
msgstr "Aukeratutako erregistroak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Selection"
msgstr "Aukeraketa "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Selection:"
msgstr "Aukeratu:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#, python-format
msgid "Set Default"
msgstr "Ezarri lehenetsia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Set Defaults"
msgstr "Ezarri lehenetsiak "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr "Ezarri azaleko irudia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Settings"
msgstr "Ezarpenak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Share with all users"
msgstr "Erabiltzaile guztiekin partekatu "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Shortcuts"
msgstr "Lasterbidea "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Show sub-fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "Eskuragarri dauden moduluak erakusten"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "Sinadura "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "Tamaina: "

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Something horrible happened"
msgstr "Zerbait ikaragarria gertatu da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Something went wrong !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Special:"
msgstr "Berezia:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Stacked"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "Kargatzen oraindik..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Kargatzen oraindik... <br /> Mesedez, pazientzia eduki. "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "Estiloa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "Estiloak "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "Summary"
msgstr "Laburpena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Summary:"
msgstr "Laburpena:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Support"
msgstr "Laguntza"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Switch to this company"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Tags"
msgstr "Etiketak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr ""
"Hartu minutu bat kafe bat edateko, <br /> kargatzen ari da oraindik..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Technical Translation"
msgstr "Itzulpen teknikoa"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
msgid "Tel:"
msgstr "Tel:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Template %s already exists in module %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Template:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Text"
msgstr "Textua"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "The domain you entered is not properly formed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid ""
"The field chain is not valid. Did you maybe use a non-existing field name or"
" followed a non-relational field?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr "Eremua hutsik dago, ez dago ezer gordetzeko!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "The following field is invalid:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/widgets/attach_document.js:0
#, python-format
msgid "The following fields are invalid:"
msgstr "Ondorengo eremuak baliogabeak dira:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "Pasahitz berria eta baieztapena berdinak izan behar dira."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "Idatzitako pasahitz zaharra okerra da, pasahitza ez da aldatu."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Do you want to"
" proceed?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr ""
"Hautatutako fitxategiak gehienezko fitxategien tamaina gainditzen du %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Arazo bat egon da zure fitxategia kargatzean"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Month"
msgstr "Hilabete hau"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Week"
msgstr "Aste honetan"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "This Year"
msgstr "This Year"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/date_picker.js:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""
"Iragazki hau globala da eta guztiontzat ezabatuko da jarraitzen baduzu."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr "Kanpoko txosten baten lagina da."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr "Barne txosten baten lagina da."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Time"
msgstr "Ordua"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Time Ranges"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Today"
msgstr "Gaur"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Toggle"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "Toggle Timelines"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Traceback:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/translation_dialog.js:0
#, python-format
msgid "Translate: "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_renderer.js:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is at least one measure and"
" no active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "Trying to reconnect..."
msgstr "Berriro konektatzen saiatzen..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Type:"
msgstr "Mota:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "URL"
msgstr "URL"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:0
#: code:addons/web/static/src/js/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "Desartxibatu "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Unarchive All"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/graph/graph_model.js:0
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/js/views/kanban/kanban_renderer_mobile.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Undefined"
msgstr "Definitu gabea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Unfold"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Unknown CORS error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/_deprecated/data.js:0
#, python-format
msgid "Unnamed"
msgstr "Izengabea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Update to:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:0
#, python-format
msgid "Upgrade now"
msgstr "Eguneratu orain"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr "Enpresa eguneratu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "Etorkizuneko bertsioetara eguneratu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Upload and Set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Upload your file"
msgstr "Fitxategia igo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploaded"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploading"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "Errorea igotzean"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Uploading..."
msgstr "Igotzen..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Use by default"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "User"
msgstr "Erabiltzailea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "User Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Users"
msgstr "Erabiltzaileak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Validation Error"
msgstr "Balidazio errorea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/tools/debug_manager_backend.js:0
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "View Fields"
msgstr "Fitxategiak ikusi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "View Metadata"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager.js:0
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#: code:addons/web/static/src/js/services/crash_manager.js:0
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/js/views/control_panel/search/favorite_menu.js:0
#: code:addons/web/static/src/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Warning"
msgstr "Abisua"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/attach_document.js:0
#, python-format
msgid "Warning : You have to save first before attaching a file."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Web:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/web_calendar.xml:0
#, python-format
msgid "Week"
msgstr "Astea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/rainbow_man.js:0
#, python-format
msgid "Well Done!"
msgstr "Ongi egina!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Widget:"
msgstr "Tresna:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/menu.xml:0
#, python-format
msgid "Windows/Linux"
msgstr "Windows/Linux"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Wrong login/password"
msgstr "Saio/pasahitz okerra"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:0
#, python-format
msgid "Wrong value entered!"
msgstr "Balore okerra sartua!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/debug.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Year"
msgstr "Urtea"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "Yellow"
msgstr "Horia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "Yes"
msgstr "Bai"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/control_panel_view_parameters.js:0
#, python-format
msgid "Yesterday"
msgstr "Atzo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:0
#, python-format
msgid "You are back online"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:0
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr ""
"%s berri bat sortzen ari zara, ziur al zaude oraindik ez dela existitzen?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "Ezin duzu pasahitza hutsik utzi."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "Agian ez duzu sinesten, <br /> baina aplikazioa kargatzen ari da..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:0
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/action_manager_report.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/name_and_signature.js:0
#: code:addons/web/static/src/xml/name_and_signature.xml:0
#, python-format
msgid "Your name"
msgstr "Zure izena "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "Duela egun bat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "Duela minutu bat"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "Duela hilabete bat inguru"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "Duela urte bat inguru"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "Duela ordu bat inguru"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "all records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "are valid for this update."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "contains"
msgstr "dauka"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "doesn't contain"
msgstr "ez dauka"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "greater than"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "greater than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "sarrera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "is"
msgstr "da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is after"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is after or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is before"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is before or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is between"
msgstr "tartean dago"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is equal to"
msgstr "berdina da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is false"
msgstr "faltsua da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not"
msgstr "ez da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is not equal to"
msgstr "ez da berdina"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is not set"
msgstr "ez da ezarri"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "is set"
msgstr "ezarri da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "is true"
msgstr "egia da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "less than"
msgstr "baino gutxiago"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "duela minutu bat baino gutxiago"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_filters.js:0
#, python-format
msgid "less than or equal to"
msgstr "gutxiago edo berdin"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "not"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "not a valid integer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_bar_autocomplete_sources.js:0
#, python-format
msgid "not a valid number"
msgstr "ez da baliozko zenbakia "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "of:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#: code:addons/web/static/src/js/views/control_panel/search/search_facet.js:0
#: code:addons/web/static/src/xml/base.xml:0
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "or"
msgstr "edo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion
msgid "portal"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "record(s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "records ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:0
#, python-format
msgid "remaining)"
msgstr "gainerako)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "selected records,"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:0
#, python-format
msgid "set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr ""
