# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_setup
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON>, 2020
# <PERSON><PERSON>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 11:27+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Persian (https://www.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "(Community Edition)"
msgstr "(نسخه عمومی)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"مقادیر ست شده در اینجا خاص "
"شرکت هستند.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&gt;', '1')]}\">\n"
"                                            Active User\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&lt;=', '1')]}\">\n"
"                                            Active Users\n"
"                                        </span>\n"
"\n"
"                                        <br/>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&gt;', '1')]}\">\n"
"                                            کاربر فعال\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&lt;=', '1')]}\">\n"
"                                            کاربران فعال\n"
"                                        </span>\n"
"\n"
"                                        <br/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">\n"
"                                                Languages\n"
"                                            </span>"
msgstr ""
"<span class=\"o_form_label\">\n"
"                                                زبان‌ها\n"
"                                            </span>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">\n"
"                                            Companies\n"
"                                        </span>\n"
"                                        <br/>"
msgstr ""
"<span class=\"o_form_label\">\n"
"                                            شرکت‌ها\n"
"                                        </span>\n"
"                                        <br/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Document Layout</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">طرح‌بندی سند</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Format</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">فرمت</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to choose your Geo "
"Provider."
msgstr ""
"<strong>ذخیره</strong> این صفحه و برگشتن به اینجا برای انتخاب فراهم کننده "
"اطلاعات جغرافیایی."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>ذخیره</strong> کنید این صفحه و بعدا برای نصب خصوصیات اینجا برگردید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "About"
msgstr "درباره"

#. module: base_setup
#: code:addons/base_setup/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "دسترسی دریغ شد"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode"
msgstr "فعال سازی حالت گسترش نرم افزار"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with assets)"
msgstr "فعال کردن حالت توسعه دهنده(همراه با دارایی)"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with tests assets)"
msgstr "فعال کردن حالت گسترش نرم افزار (با تست های دارایی)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add fun feedback and motivate your employees"
msgstr "بازخورد جذاب بدهید و کارمندانتان را انگیزه دهید"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr "به کاربران اجازه بده تقویم خود با تقویم گوگل هماهنگ سازی کنند"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_import
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr "به کاربران اجازه ورود داده از فایلهای  CSV/XLS/XLSX/ODS بده"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr "اجازه کار در محیط چند ارزه بده"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_voip
msgid "Asterisk (VoIP)"
msgstr "ستاره (VoIP)"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_drive
msgid "Attach Google documents to any record"
msgstr "پیوست اسناد گوگل به هر رکورد"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Automatically enrich your contact base with company data"
msgstr "به صورت خودکار لیست مخاطبان خود را غنی کنید با داده شرکت"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Automatically generate counterpart documents for orders/invoices between "
"companies"
msgstr ""
"به طور خودکار اسناد شرکت‌های همکار بین شرکتی برای سفارش‌ها/فاکتورها ایجاد کن"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Business Documents"
msgstr "اسناد تجاری"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"By default, new users get highest access rights for all installed apps."
msgstr ""
"به صورت پیش فرض کاربران جدید بالاترین حق دسترسی برای همه‌ی برنامه‌های نصب "
"شده می گیرند."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Choose the layout of your documents"
msgstr "طرح‌بندی اسناد خود را انتخاب کن"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_pad
msgid "Collaborative Pads"
msgstr "پدهای تعاملی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Companies"
msgstr "شرکت‌ها"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_id
msgid "Company"
msgstr "شرکت"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_informations
msgid "Company Informations"
msgstr "اطلاعات شرکت"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_name
msgid "Company Name"
msgstr "نام شرکت"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Configure Document Layout"
msgstr "پیکربندی طرح‌بندی سند"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Configure company rules to automatically create SO/PO when one of your "
"company sells/buys to another of your company."
msgstr ""
"قوانین شرکت را تنظیم کنید تا به طور خودکار SO / PO را ایجاد کنید وقتی یکی از"
" شرکت شما به دیگری از شرکت شما می خرید / خریداری می کند."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Contacts"
msgstr "مخاطبان"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Copyright © 2004"
msgstr "حق تالیف © 2004"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Create and attach Google Drive documents to any record"
msgstr "ایجاد و پیوست اسناد گوگل درایو به هر رکورد"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__report_footer
msgid "Custom Report Footer"
msgstr "پا ورقی گزارش سفارشی"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Deactivate the developer mode"
msgstr "غیر فعال کردن حالت توسعه دهنده"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__user_default_rights
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "حق دسترسی های پیش فرض"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Developer Tools"
msgstr "ابزارهای توسعه گران"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_report_layout_id
msgid "Document Template"
msgstr "قالب سند"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Edit Layout"
msgstr "ویرایش طرح بندی"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Enter e-mail address"
msgstr "نشانی ایمیل وارد کن"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_email_server_default
msgid "External Email Servers"
msgstr "سرورهای ایمیل خارجی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Extract and analyze Odoo data from Google Spreadsheet"
msgstr "داده های Odoo را از صفحه گسترده Google استخراج و تجزیه و تحلیل کنید"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Find free high-resolution images from Unsplash"
msgstr "تصاویر با وضوح بالا را از Unsplash پیدا کنید"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "متن پاورقی نمایش داده شده در همه گزارشها."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Format"
msgstr "قالب"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "GNU LGPL Licensed"
msgstr "گواهینامه GNU LGPL"

#. module: base_setup
#: model:ir.ui.menu,name:base_setup.menu_config
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "General Settings"
msgstr "تنظیمات عمومی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Gengo Translations"
msgstr "ترجمه‌های جنگو"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Geo Localization"
msgstr "محلی سازی جغرافیایی"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_geolocalize
msgid "GeoLocalize"
msgstr "محل یابی جغرافیایی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "GeoLocalize your partners"
msgstr "محل یابی کن مشتریان خود"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Calendar"
msgstr "تقویم گوگل"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Drive"
msgstr "درایو گوگل"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_spreadsheet
msgid "Google Spreadsheet"
msgstr "صفحه گسترده گوگل"

#. module: base_setup
#: model:ir.model,name:base_setup.model_ir_http
msgid "HTTP Routing"
msgstr "مسیریابی HTTP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Import & Export"
msgstr "ورود و خروج فایل"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrations"
msgstr "ادغامها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Inter-Company Transactions"
msgstr "تراکنشهای درون شرکتی"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Invite"
msgstr "دعوت"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Invite New Users"
msgstr "کاربران جدید دعوت کن"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_ldap
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "LDAP Authentication"
msgstr "احراز هویت LDAP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Languages"
msgstr "زبان‌ها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Layout"
msgstr "طرح بندی"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Load demo data"
msgstr "بارگذاری داده نمایشی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Companies"
msgstr "شرکت‌ها مدیریت کن"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_inter_company_rules
msgid "Manage Inter Company"
msgstr "مدیریت بین شرکتی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Languages"
msgstr "زبان‌ها مدیریت کن"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Users"
msgstr "کاربران مدیریت کن"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Multi-Company"
msgstr "چند شرکتی"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__group_multi_currency
msgid "Multi-Currencies"
msgstr "چند ارزی"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__active_user_count
msgid "Number of Active Users"
msgstr "تعداد کاربران فعال"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_count
msgid "Number of Companies"
msgstr "تعداد شرکتها"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__language_count
msgid "Number of Languages"
msgstr "تعداد زبان‌ها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "OAuth Authentication"
msgstr "احراز هویت OAuth"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Odoo S.A."
msgstr "سایت اودوو "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Apple Store"
msgstr "روی فروشگاه اپل"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Google Play"
msgstr "روی گوگل پلی"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__paperformat_id
msgid "Paper format"
msgstr "قالب کاغذ"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_partner_autocomplete
msgid "Partner Autocomplete"
msgstr "پر کردن خودکار همکار"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Pending Invitations:"
msgstr "دعوت های در انتظار:"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Preview Document"
msgstr "پیش نمایش سند"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send SMS"
msgstr "ارسال اس ام اس"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "پیام متنی برای مخاطبان خود بفرست"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set custom access rights for new users"
msgstr "تعیین حق دسترسی سفارشی برای کاربران جدید"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set the paper format of printed documents"
msgstr "تعیین قالب کاغذ برای اسناد پریت شده"

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_general_configuration
msgid "Settings"
msgstr "تنظیمات"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__show_effect
msgid "Show Effect"
msgstr "نمایش تاثیر"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Statistics"
msgstr "آمار"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr "همگام سازی گاهشمار شما با گاهشمار گوگل"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/js/res_config_invite_users.js:0
#, python-format
msgid "The following email addresses already exist: %s."
msgstr "نشانی‌های ایمیل زیر از قبل وجود دارد: %s."

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/js/res_config_invite_users.js:0
#, python-format
msgid "The following email addresses are invalid: %s."
msgstr "نشانی‌های ایمیل زیر نامعتبر هستند: %s."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_gengo
msgid "Translate Your Website with Gengo"
msgstr "ترجمه وبسایتتان با استفاده از جنگو"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Translate your website with Gengo"
msgstr "ترجمه وبسایتتان با استفاده از جنگو"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_web_unsplash
msgid "Unsplash Image Library"
msgstr "آن اسپلش کتابخانه تصویر"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Update Info"
msgstr "به روز کردن اطلاعات"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use LDAP credentials to log in"
msgstr "برای ورود به سیستم از اعتبارنامه LDAP استفاده کنید"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external accounts to log in (Google, Facebook, etc.)"
msgstr "استفاده از حسابهای خارجی برای ورود (گوگل، فیسبوک، غیره ...)"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr "از ارائه دهندگان احراز هویت خارجی استفاده کنید (OAuth)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external pads in Odoo Notes"
msgstr "استفاده از پدهای خارجی در یادداشت اودو"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/js/res_config_invite_users.js:0
#: model:ir.model,name:base_setup.model_res_users
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
#, python-format
msgid "Users"
msgstr "کاربران"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"When populating your address book, Odoo provides a list of matching "
"companies. When selecting one item, the company data and logo are auto-"
"filled."
msgstr ""
"Odoo هنگام جمع آوری دفترچه آدرس ، لیستی از شرکتهای منطبق را ارائه می دهد. "
"هنگام انتخاب یک مورد ، داده های شرکت و آرم به طور خودکار پر می شوند."

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "more"
msgstr "بیشتر"
