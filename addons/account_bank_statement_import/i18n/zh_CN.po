# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import
# 
# Translators:
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# bower <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>/杨孟泽 <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON>ery CHEN Fan <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:21+0000\n"
"PO-Revision-Date: 2019-08-26 09:08+0000\n"
"Last-Translator: Jeffery CHEN Fan <<EMAIL>>, 2020\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_bank_statement_import
#. openerp-web
#: code:addons/account_bank_statement_import/static/src/js/account_bank_statement_import.js:0
#, python-format
msgid " Import Template for Bank Statements"
msgstr "银行对账单导入模板."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "%d transactions had already been imported and were ignored."
msgstr "%d 已导入的交易将被忽略"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "1 transaction had already been imported and was ignored."
msgstr "1个 已导入的交易将被忽略."

#. module: account_bank_statement_import
#: model:ir.model.constraint,message:account_bank_statement_import.constraint_account_bank_statement_line_unique_import_id
msgid "A bank account transactions can be imported only once !"
msgstr "一个银行账户交易只能导入一次."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_partner_id
msgid "Account Holder"
msgstr "账户持有人"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_acc_number
msgid "Account Number"
msgstr "账户号码"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type_control_ids
msgid "Account Types Allowed"
msgstr "允许的科目类型"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__account_control_ids
msgid "Accounts Allowed"
msgstr "允许的科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction
msgid "Action Needed"
msgstr "需要行动"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__active
msgid "Active"
msgstr "启用"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_ids
msgid "Activities"
msgstr "活动"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动例外勋章"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_id
msgid "Alias"
msgstr "别名"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_name
msgid "Alias Name"
msgstr "别名"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_domain
msgid "Alias domain"
msgstr "域名别名"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "Already imported items"
msgstr "已导入的项目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__at_least_one_inbound
msgid "At Least One Inbound"
msgstr "至少一个转入"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__at_least_one_outbound
msgid "At Least One Outbound"
msgstr "至少一个转出"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_id
#, python-format
msgid "Bank"
msgstr "银行"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_account_id
msgid "Bank Account"
msgstr "银行账户"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_statements_source
msgid "Bank Feeds"
msgstr "银行费用"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "Bank Journal Name"
msgstr "银行日记账名称"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "银行对账单明细"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr "银行设置手动配置"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Cancel"
msgstr "取消"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr "不能找到所需导入的账簿. 请手动选择账簿."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr "勾选此框，则此日记账下的会计发票或退款不使用同一个序列。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__color
msgid "Color Index"
msgstr "颜色索引"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_model
msgid "Communication Standard"
msgstr "通讯标准"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_type
msgid "Communication Type"
msgstr "讯息类型"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_id
msgid "Company"
msgstr "公司"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_id
msgid "Company related to this journal"
msgstr "日记账相关的公司"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file ?"
msgstr ""
"提供的文件不对.\n"
"你安装了模块来支持这种类型的文件吗？"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__create_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__create_uid
msgid "Created by"
msgstr "创建者"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__create_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__create_date
msgid "Created on"
msgstr "创建时间"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr "退款分录序列"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence_number_next
msgid "Credit Notes Next Number"
msgstr "退款据下一个号码"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__currency_id
msgid "Currency"
msgstr "币种"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "专用的退款序列"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__default_credit_account_id
msgid "Default Credit Account"
msgstr "默认贷方科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__default_debit_account_id
msgid "Default Debit Account"
msgstr "默认借方科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_statements_source
msgid "Defines how the bank statements will be registered"
msgstr "定义银行对账单的注册方式"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__display_name
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence_id
msgid "Entry Sequence"
msgstr "分录序列"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__attachment_ids
msgid "Files"
msgstr "文件"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_channel_ids
msgid "Followers (Channels)"
msgstr "关注者(渠道)"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__inbound_payment_method_ids
msgid "For Incoming Payments"
msgstr "未收款"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__outbound_payment_method_ids
msgid "For Outgoing Payments"
msgstr "为付款"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import__attachment_ids
msgid ""
"Get you bank statements in electronic format from your bank and select them "
"here."
msgstr "从银行拿到电子格式的银行对账单并且在这里选择他们"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__id
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__id
msgid "ID"
msgstr "ID"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "表示一个例外活动的图标。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"If it contains transactions for more than one account, it must be imported "
"on each of them."
msgstr "如果它包含多个账户的交易，它必须对应进行导入。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__restrict_mode_hash_table
msgid ""
"If ticked, the accounting entry or invoice receives a hash as soon as it is "
"posted and cannot be modified anymore."
msgstr "如果勾選，會計分錄或應收付憑單在過帳後立即完成沖帳/對帳，並且無法再修改。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
#, python-format
msgid "Import"
msgstr "导入"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "导入银行对账单"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line__unique_import_id
msgid "Import ID"
msgstr "导入ID"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import Statement"
msgstr "导入对账单"

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.install_more_import_formats_action
msgid "Install Import Format"
msgstr "安装导入格式"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__default_credit_account_id
msgid "It acts as a default account for credit amount"
msgstr "它将充当贷方金额的默认科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__default_debit_account_id
msgid "It acts as a default account for debit amount"
msgstr "它将作为一个默认借方科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_name
msgid "It creates draft invoices and bills by sending an email."
msgstr "它通过发送电子邮件创建草稿发票和账单。"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__journal_id
msgid "Journal"
msgstr "日记账"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#, python-format
msgid "Journal Creation"
msgstr "日记账创建"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import_journal_creation
msgid "Journal Creation on Bank Statement Import"
msgstr "在银行对账单导入创建日记账"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__journal_group_ids
msgid "Journal Groups"
msgstr "日记账组"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__name
msgid "Journal Name"
msgstr "日记账名称"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__json_activity_data
msgid "Json Activity Data"
msgstr "Json活动数据"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"Just click OK to create the account/journal and finish the upload. If this "
"was a mistake, hit cancel to abort the upload."
msgstr "只需单击“确定”即可创建账户/日记账并完成上载。如果这是一个错误，请点击取消以中止上传。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__kanban_dashboard
msgid "Kanban Dashboard"
msgstr "看板仪表板"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "看板仪表板图表"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import____last_update
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation____last_update
msgid "Last Modified on"
msgstr "最后更改日"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__write_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__write_uid
msgid "Last Updated by"
msgstr "最后更新者"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__write_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__write_date
msgid "Last Updated on"
msgstr "更新时间"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__restrict_mode_hash_table
msgid "Lock Posted Entries with Hash"
msgstr "使用Hash鎖定已過帳分錄"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__loss_account_id
msgid "Loss Account"
msgstr "损失科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_main_attachment_id
msgid "Main Attachment"
msgstr "附件"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""
"手动：通过现金、支票或除 Odoo 以外的其他方法获取报酬.\n"
"电子方式：在客户线上购买或订阅时（支付令），在已保存的卡中请求交易，通过付款受让人自动获取报酬.\n"
"批量存款：几个客户同时支票付款时，可生成批量存款提交到银行中。当在 Odoo 进行编码银行对账单时，建议您对账批量存款与交易之间的关系。从设置中启用该选项."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""
"手动：现金支付或使用系统外的方式支付。\n"
"支票：使用支票支付并在系统打印。\n"
"SEPA转账：使用SEPA付款并提交文件到银行，从设置中启用此功能。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_ids
msgid "Messages"
msgstr "消息"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence_number_next
msgid "Next Number"
msgstr "下一号码"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "No currency found matching '%s'."
msgstr "找不到与'%s'匹配的币种"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction_counter
msgid "Number of Actions"
msgstr "动作个数"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要操作消息数量"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "递送错误消息数量"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "OK"
msgstr "OK"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__post_at
msgid "Post At"
msgstr "发布于"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__profit_account_id
msgid "Profit Account"
msgstr "利润科目"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__secure_sequence_id
msgid "Secure Sequence"
msgstr "安全序号"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"给客户发票日记账选择 '销售' .\n"
"给供应商发票选 '采购' .\n"
"在客户或者供应商付款的日记账中选择 '现金' 或者 '银行' .\n"
"给其余操作的日记账选择 '通用' ."

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Select Files"
msgstr "选择文件夾"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence
msgid "Sequence"
msgstr "序号"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__secure_sequence_id
msgid "Sequence to use to ensure the securisation of data"
msgstr "用於確保資料安全的序列"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__active
msgid "Set active to false to hide the Journal without removing it."
msgstr "设置为未启用，可以隐藏日记账而不用删除它。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__code
msgid "Short Code"
msgstr "简码"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__show_on_dashboard
msgid "Show journal on dashboard"
msgstr "在仪表板显示日记账"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态 \n"
" 逾期：已经超过截止日期 \n"
" 现今：活动日期是当天 \n"
" 计划：未来活动。"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"The account of the statement you are uploading is not yet recorded in Odoo. "
"In order to proceed with the upload, you need to create a bank journal for "
"this account."
msgstr "你上传的对账单的账户尚未记录在Odoo中。要继续上传，你需要为此账户创建银行日记账。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"The account of this statement (%s) is not the same as the journal (%s)."
msgstr "对账单中的科目(%s)和日记账(%s)中的不一样"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"The currency of the bank statement (%s) is not the same as the currency of "
"the journal (%s)."
msgstr "银行对账单 (%s) 的币种和日记账 (%s)的币种不一样."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__currency_id
msgid "The currency used to enter statement"
msgstr "用来输入对账单的币种"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__code
msgid "The journal entries of this journal will be named using this prefix."
msgstr "这个日记账中的会计凭证会用这个前缀命名"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr "下一号码将用于下一张退款."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr "下一号码将用于下一张发票."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr "该字段包含此日记账中与信用清单目录数量相关的信息。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr "该字段包含和这个日记中会计凭证数量相关的信息"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "This file doesn't contain any statement for account %s."
msgstr "这个文件不包含这个账户的任何报告%s。"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "This file doesn't contain any transaction for account %s."
msgstr "这个文件不包含这个账户的任何交易%s。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type
msgid "Type"
msgstr "类型"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录的例外活动类型。"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息计数器"

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.action_account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Upload"
msgstr "上传"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Upload Bank Statements"
msgstr "上传银行对账单"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence
msgid "Used to order Journals in the dashboard view"
msgstr "用于仪表板视图中的日记账排序"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr "收银机的期末余额与系统计算的有差异时候用来记录损失"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr "当收银机的关账余额与系统计算的有差异时，用于记录利润"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "无论这个日记账是否显示在仪表板"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "You already have imported that file."
msgstr "你已经导入文件。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr "你可以为每种参考类型选择不同的模型。默认值是Odoo引用。"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_type
msgid ""
"You can set here the default communication that will appear on customer "
"invoices, once validated, to help the customer to refer to that particular "
"invoice when making the payment."
msgstr "您可以在这里设置默认联系方式，该联系方式将显示在客户发票上，一旦验证，以帮助客户在付款时参考该发票联系。"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "You can upload your bank statement using:"
msgstr "你可以使用以下方式上传银行对账单："

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"You have to set a Default Debit Account and a Default Credit Account for the"
" journal: %s"
msgstr "您必须为日记账设置默认借记账户和默认信用账户：%s"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "or"
msgstr "或"
