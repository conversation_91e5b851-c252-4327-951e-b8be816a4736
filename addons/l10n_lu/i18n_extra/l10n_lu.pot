# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_lu
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-06-19 09:05+0000\n"
"PO-Revision-Date: 2020-06-19 09:05+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-IC-EX
msgid " EX-IC-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_V-ART-43_60b
msgid "0-E-Art.43&60b"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_V-ART-44_56q
msgid "0-E-Art.44&56q"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-PA-0
msgid "0-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-PA-0
msgid "0-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-0
msgid "0-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-EC-0
msgid "0-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-EC-0
msgid "0-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-EC-0
msgid "0-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-EC-0
msgid "0-EC-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-EC-0
msgid "0-EC-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-EC-Tab
msgid "0-EC-ST-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-IC-0
msgid "0-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-IC-0
msgid "0-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-IC-0
msgid "0-IC-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-IC-0
msgid "0-IC-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-IC-Tab
msgid "0-IC-ST-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VB-TR-0
msgid "0-ICT-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-PA-0
msgid "0-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-PA-0
msgid "0-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-PA-0
msgid "0-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-PA-0
msgid "0-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_SANS
msgid "0-P-Tax-Free"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-PA-0
msgid "0-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-PA-0
msgid "0-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_SANS_sale
msgid "0-S-Tax-Free"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-PA-Tab
msgid "0-ST-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-ECP-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-ECP-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-ECP-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-EC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-IC-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_VB-PA-14
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_VP-PA-14
msgid "14%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-PA-14
msgid "14-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-PA-14
msgid "14-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-14
msgid "14-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-14
msgid "14-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-14
msgid "14-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-EC-14
msgid "14-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-EC-14
msgid "14-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-EC-14
msgid "14-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-EC-14
msgid "14-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-EC-14
msgid "14-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-EC-14
msgid "14-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-IC-14
msgid "14-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-IC-14
msgid "14-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-IC-14
msgid "14-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-IC-14
msgid "14-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-IC-14
msgid "14-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-IC-14
msgid "14-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-PA-14
msgid "14-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-PA-14
msgid "14-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-PA-14
msgid "14-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-PA-14
msgid "14-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VB-PA-14
msgid "14-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VP-PA-14
msgid "14-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-ECP-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AB-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_AP-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-ECP-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FB-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_FP-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-ECP-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IB-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-EC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-IC-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_IP-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_VB-PA-17
#: model:account.tax.template,description:l10n_lu.lu_2015_tax_VP-PA-17
msgid "17%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-PA-17
msgid "17-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-PA-17
msgid "17-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-17
msgid "17-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-17
msgid "17-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-17
msgid "17-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-EC-17
msgid "17-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-EC-17
msgid "17-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-EC-17
msgid "17-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-EC-17
msgid "17-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-EC-17
msgid "17-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-EC-17
msgid "17-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-IC-17
msgid "17-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-IC-17
msgid "17-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-IC-17
msgid "17-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-IC-17
msgid "17-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-IC-17
msgid "17-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-IC-17
msgid "17-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-PA-17
msgid "17-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-PA-17
msgid "17-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-PA-17
msgid "17-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-PA-17
msgid "17-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VB-PA-17
msgid "17-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VP-PA-17
msgid "17-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-PA-3
msgid "3-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-PA-3
msgid "3-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-3
msgid "3-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-3
msgid "3-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-3
msgid "3-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-EC-3
msgid "3-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-EC-3
msgid "3-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-EC-3
msgid "3-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-EC-3
msgid "3-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-EC-3
msgid "3-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-EC-3
msgid "3-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-IC-3
msgid "3-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-IC-3
msgid "3-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-IC-3
msgid "3-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-IC-3
msgid "3-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-IC-3
msgid "3-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-IC-3
msgid "3-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IB-PA-3
msgid "3-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_IP-PA-3
msgid "3-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-PA-3
msgid "3-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-PA-3
msgid "3-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VB-PA-3
msgid "3-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_VP-PA-3
msgid "3-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-PA-8
msgid "8-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-PA-8
msgid "8-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-8
msgid "8-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-ECP-8
msgid "8-EC(P)-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-8
msgid "8-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-EC-8
msgid "8-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-EC-8
msgid "8-EC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-EC-8
msgid "8-EC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-EC-8
msgid "8-EC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-EC-8
msgid "8-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-EC-8
msgid "8-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-IC-8
msgid "8-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FP-IC-8
msgid "8-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-IC-8
msgid "8-IC-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-IC-8
msgid "8-IC-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-IC-8
msgid "8-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-IC-8
msgid "8-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IB-PA-8
msgid "8-IG"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_IP-PA-8
msgid "8-IS"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-PA-8
msgid "8-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AP-PA-8
msgid "8-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VB-PA-8
msgid "8-S-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_VP-PA-8
msgid "8-S-S"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_46128
msgid "ACD - Other amounts payable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42148
msgid "ACD - Other amounts receivable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_46148
msgid "AED - Other debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65112
msgid "AVA on amounts owed by affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6352
msgid ""
"AVA on amounts owed by affiliated undertakings and undertakings with which "
"the undertaking is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65114
msgid ""
"AVA on amounts owed by undertakings with which the undertaking is linked by "
"virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_63313
msgid "AVA on buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6322
msgid ""
"AVA on concessions, patents, licences, trademarks and similar rights and "
"assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6321
msgid "AVA on development costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6324
msgid "AVA on down payments and intangible fixed assets under development"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6334
msgid "AVA on down payments and tangible fixed assets under development"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6345
msgid "AVA on down payments on inventories"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6313
msgid ""
"AVA on expenses for capital increases and various operations (mergers, "
"demergers, changes of legal form)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_63314
msgid "AVA on fixtures and fittings-out of buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_63312
msgid "AVA on fixtures and fittings-out of land"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6323
msgid "AVA on goodwill acquired for consideration"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6343
msgid "AVA on inventories of goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6344
msgid "AVA on inventories of merchandise and other goods for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6341
msgid "AVA on inventories of raw materials and consumables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6342
msgid "AVA on inventories of work and contracts in progress"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_63311
msgid "AVA on land"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6314
msgid "AVA on loan-issuance expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65116
msgid "AVA on loans, deposits and claims held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6333
msgid ""
"AVA on other fixtures and fittings, tools and equipment (including rolling "
"stock)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6353
msgid "AVA on other receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6318
msgid "AVA on other similar expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65318
msgid "AVA on other transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65312
msgid "AVA on own shares or own corporate units"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65113
msgid "AVA on participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6332
msgid "AVA on plant and machinery"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65115
msgid "AVA on securities held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6311
msgid "AVA on set-up and start-up costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65111
#: model:account.account.template,name:l10n_lu.lu_2011_account_65311
msgid "AVA on shares in affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65313
msgid ""
"AVA on shares in undertakings with which the undertaking is linked by virtue"
" of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6351
msgid "AVA on trade receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106142
msgid "Accident insurance"
msgstr ""

#. module: l10n_lu
#: model:ir.model,name:l10n_lu.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61342
msgid "Accounting, tax consulting, auditing and similar fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_16121
msgid "Acquired against payment (except Goodwill)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_771
msgid "Adjustments of corporate income tax (CIT)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_773
msgid "Adjustments of foreign income taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_783
msgid "Adjustments of foreign taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_772
msgid "Adjustments of municipal business tax (MBT)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_781
msgid "Adjustments of net wealth tax (NWT)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_788
msgid "Adjustments of other taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_782
msgid "Adjustments of subscription tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42111
msgid "Advances and down payments"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6591
msgid "Allocations to financial provisions - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6592
msgid "Allocations to financial provisions - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6492
msgid "Allocations to operating provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_679
msgid "Allocations to provisions for deferred taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6491
msgid "Allocations to tax provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_647
msgid "Allocations to tax-exempt capital gains"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_232
#: model:account.account.template,name:l10n_lu.lu_2011_account_6452
#: model:account.account.template,name:l10n_lu.lu_2011_account_75112
#: model:account.account.template,name:l10n_lu.lu_2020_account_65212
#: model:account.account.template,name:l10n_lu.lu_2020_account_75212
msgid "Amounts owed by affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4212
#: model:account.account.template,name:l10n_lu.lu_2020_account_4222
msgid ""
"Amounts owed by partners and shareholders (others than from affiliated "
"undertakings)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75114
msgid ""
"Amounts owed by undertakings with which the company is linked by virtue of "
"participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_234
#: model:account.account.template,name:l10n_lu.lu_2011_account_6453
#: model:account.account.template,name:l10n_lu.lu_2020_account_65214
#: model:account.account.template,name:l10n_lu.lu_2020_account_75214
msgid ""
"Amounts owed by undertakings with which the undertaking is linked by virtue "
"of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4713
#: model:account.account.template,name:l10n_lu.lu_2011_account_4723
msgid "Amounts payable to directors, managers, statutory auditors and similar"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4712
#: model:account.account.template,name:l10n_lu.lu_2020_account_4722
msgid ""
"Amounts payable to partners and shareholders (others than from affiliated "
"undertakings)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4714
#: model:account.account.template,name:l10n_lu.lu_2020_account_4724
msgid "Amounts payable to staff"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60813
msgid "Architects' and engineers' fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6431
msgid "Attendance fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_743
msgid "Attendance fees, director's fees and similar remunerations"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61333
msgid ""
"Bank account charges and bank commissions (included custody fees on "
"securities)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65521
msgid "Banking interest on current accounts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65522
msgid "Banking interest on financing operations"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_5131
msgid "Banks and CCP : available balance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_5132
msgid "Banks and CCP : overdraft"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6467
msgid "Bar licence tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62111
msgid "Base wages"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62115
#: model:account.account.template,name:l10n_lu.lu_2011_account_746
msgid "Benefits in kind"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4422
msgid "Bills of exchange payable after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4421
msgid "Bills of exchange payable within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652221
#: model:account.account.template,name:l10n_lu.lu_2020_account_752221
msgid "Book value of yielded amounts owed by affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652241
#: model:account.account.template,name:l10n_lu.lu_2020_account_752241
msgid ""
"Book value of yielded amounts owed by undertakings with which the "
"undertaking is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_64411
#: model:account.account.template,name:l10n_lu.lu_2020_account_74411
msgid "Book value of yielded intangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652261
#: model:account.account.template,name:l10n_lu.lu_2020_account_752261
msgid "Book value of yielded loans, deposits and claims held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652231
#: model:account.account.template,name:l10n_lu.lu_2020_account_752231
msgid "Book value of yielded participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652251
#: model:account.account.template,name:l10n_lu.lu_2020_account_752251
msgid "Book value of yielded securities held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652211
#: model:account.account.template,name:l10n_lu.lu_2020_account_752211
msgid "Book value of yielded shares in affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_64421
#: model:account.account.template,name:l10n_lu.lu_2020_account_74421
msgid "Book value of yielded tangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61112
#: model:account.account.template,name:l10n_lu.lu_2011_account_61221
#: model:account.account.template,name:l10n_lu.lu_2011_account_61411
msgid "Buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60763
msgid "Buildings for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22132
msgid "Buildings in foreign countries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_314
msgid "Buildings under construction"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61523
msgid "Business assignments"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6144
msgid "Business risk insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10629
msgid "Business share in private expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461212
msgid "CIT - Tax payable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6711
msgid "CIT - current financial year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6712
msgid "CIT - previous financial years"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_115
msgid "Capital contribution without issue of shares"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7473
msgid "Capital investment subsidies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_104
msgid "Capital of individual companies, corporate partnerships and similar"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106166
msgid "Car"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_516
msgid "Cash in hand"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10611
msgid "Cash withdrawals (daily life)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6222
msgid "Casual workers"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61515
msgid "Catalogues, printed materials and publications"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7121
msgid "Change in inventories of finished goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7123
msgid "Change in inventories of residual goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7122
msgid "Change in inventories of semi-finished goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7111
msgid "Change in inventories of work in progress"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7114
msgid "Change in inventories: buildings under construction"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7112
msgid "Change in inventories: contracts in progress - goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7113
msgid "Change in inventories: contracts in progress - services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6073
msgid "Changes in inventory of consumable materials and supplies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6074
msgid "Changes in inventory of packaging"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6071
msgid "Changes in inventory of raw materials"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62412
msgid "Changes to provisions for complementary pensions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61334
msgid "Charges for electronic means of payment"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106152
msgid "Child benefit office"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6165
msgid "Collective staff transportation"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6131
#: model:account.account.template,name:l10n_lu.lu_2020_account_705
msgid "Commissions and brokerage fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7453
msgid "Compensatory allowances"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62415
msgid "Complementary pensions paid by the employer"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2235
msgid "Computer equipment"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_21211
#: model:account.account.template,name:l10n_lu.lu_2011_account_21221
#: model:account.account.template,name:l10n_lu.lu_2011_account_6411
#: model:account.account.template,name:l10n_lu.lu_2011_account_72121
#: model:account.account.template,name:l10n_lu.lu_2011_account_7411
#: model:account.account.template,name:l10n_lu.lu_2020_account_70311
msgid "Concessions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_312
msgid "Contracts in progress - goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_313
msgid "Contracts in progress - services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_113
msgid "Contribution premium"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6187
msgid "Contributions to professional associations"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_212151
#: model:account.account.template,name:l10n_lu.lu_2011_account_212251
#: model:account.account.template,name:l10n_lu.lu_2011_account_64151
#: model:account.account.template,name:l10n_lu.lu_2011_account_721251
#: model:account.account.template,name:l10n_lu.lu_2011_account_74151
#: model:account.account.template,name:l10n_lu.lu_2020_account_703151
msgid "Copyrights and reproduction rights"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42141
msgid "Corporate income tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461211
msgid "Corporate income tax - Tax accrual"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6182
msgid "Costs of training, symposiums, seminars, conferences"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_16122
msgid "Created by the undertaking itself"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_67321
msgid "Current financial year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4011
#: model:account.account.template,name:l10n_lu.lu_2011_account_4021
msgid "Customers"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_40111
msgid "Customers (PoS)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4012
msgid "Customers - Receivable bills of exchange"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4014
msgid "Customers - Unbilled sales"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6145
msgid "Customers credit insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4015
msgid "Customers with a credit balance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4025
msgid "Customers with creditor balance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4215
#: model:account.account.template,name:l10n_lu.lu_2020_account_4613
msgid "Customs and Excise Authority (ADA)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106154
msgid "Death and other health insurance funds"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5083
msgid "Debenture loans and other notes issued and repurchased by the company"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_23521
msgid "Debentures"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_481
msgid "Deferred charges (on one or more financial years)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_482
msgid "Deferred income (on one or more financial years)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_183
msgid "Deferred tax provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_2362
msgid "Deposits and guarantees paid"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106192
msgid "Deposits on private financial accounts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42187
#: model:account.account.template,name:l10n_lu.lu_2020_account_42287
#: model:account.account.template,name:l10n_lu.lu_2020_account_4717
#: model:account.account.template,name:l10n_lu.lu_2020_account_4727
msgid "Derivative financial instruments"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221111
msgid "Developed land"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_211
#: model:account.account.template,name:l10n_lu.lu_2011_account_7211
#: model:account.account.template,name:l10n_lu.lu_2020_account_1611
msgid "Development costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6432
msgid "Director's fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65551
msgid "Discounts and charges on bills of exchange - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65552
msgid "Discounts and charges on bills of exchange - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75551
msgid "Discounts on bills of exchange - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75552
msgid "Discounts on bills of exchange - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75561
msgid "Discounts received - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75562
msgid "Discounts received - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_752262
msgid "Disposal proceed of loans, deposits and claims held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652222
#: model:account.account.template,name:l10n_lu.lu_2020_account_752222
msgid "Disposal proceeds of amounts owed by affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652242
#: model:account.account.template,name:l10n_lu.lu_2020_account_752242
msgid ""
"Disposal proceeds of amounts owed by undertakings with which the undertaking"
" is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_64412
#: model:account.account.template,name:l10n_lu.lu_2020_account_74412
msgid "Disposal proceeds of intangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652262
msgid "Disposal proceeds of loans, deposits and claims held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652232
#: model:account.account.template,name:l10n_lu.lu_2020_account_752232
msgid "Disposal proceeds of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652252
#: model:account.account.template,name:l10n_lu.lu_2020_account_752252
msgid "Disposal proceeds of securities held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_652212
#: model:account.account.template,name:l10n_lu.lu_2020_account_752212
msgid "Disposal proceeds of shares in affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_64422
#: model:account.account.template,name:l10n_lu.lu_2020_account_74422
msgid "Disposal proceeds of tangible fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6181
msgid "Documentation"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61516
msgid "Donations"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4013
msgid "Doubtful or disputed customers"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_214
msgid "Down payments and intangible fixed assets under development"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_37
msgid "Down payments on account on inventories"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_432
msgid "Down payments received after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_431
msgid "Down payments received within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1922
#: model:account.account.template,name:l10n_lu.lu_2020_account_1932
#: model:account.account.template,name:l10n_lu.lu_2020_account_1942
msgid "Due and payable after more than one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1921
#: model:account.account.template,name:l10n_lu.lu_2020_account_1931
#: model:account.account.template,name:l10n_lu.lu_2020_account_1941
msgid "Due and payable within one year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6463
msgid "Duties on imported merchandise"
msgstr ""

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_private_LU_IC
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_private_LU_IC
msgid "EU private"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_FB-ECP-0
msgid "EX-EC(P)-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2015_tax_AB-ECP-0
msgid "EX-EC(P)-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-EC-0
msgid "EX-EC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-EC-0
msgid "EX-EC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-EC-0
msgid "EX-EC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FB-IC-0
msgid "EX-IC-E-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_FP-IC-0
msgid "EX-IC-E-S"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AB-IC-0
msgid "EX-IC-P-G"
msgstr ""

#. module: l10n_lu
#: model:account.tax.template,name:l10n_lu.lu_2011_tax_AP-IC-0
msgid "EX-IC-P-S"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60315
#: model:account.account.template,name:l10n_lu.lu_2020_account_61845
msgid "Electricity"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_105
msgid "Endowment of branches"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6464
msgid "Excise duties on production and tax on consumption"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_203
msgid ""
"Expenses for increases in capital and for various operations (merger, "
"demerger, change of legal form)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6172
msgid "External staff on secondment"
msgstr ""

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_LU_EC
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_LU_EC
msgid "Extra-Community Taxable Person"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6512
#: model:account.account.template,name:l10n_lu.lu_2011_account_7512
msgid "FVA on financial fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_63315
#: model:account.account.template,name:l10n_lu.lu_2020_account_73315
msgid "FVA on investment properties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6354
#: model:account.account.template,name:l10n_lu.lu_2020_account_7354
msgid "FVA on receivables from current assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6532
msgid "FVA on transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61336
msgid "Factoring services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7532
msgid "Fair value adjustments on transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61513
msgid "Fairs and exhibitions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6114
msgid "Financial leasing on real property"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_1882
msgid "Financial provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6481
msgid "Fines, sanctions and penalties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106143
msgid "Fire insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_22141
msgid "Fixtures and fitting-outs of buildings in Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_22142
msgid "Fixtures and fitting-outs of buildings in foreign countries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22121
msgid "Fixtures and fitting-outs of land in Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22122
msgid "Fixtures and fitting-outs of land in foreign countries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4622
msgid "Foreign Social Security offices"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421811
#: model:account.account.template,name:l10n_lu.lu_2020_account_46151
msgid "Foreign VAT"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7561
msgid "Foreign currency exchange gains - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7562
msgid "Foreign currency exchange gains - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6561
msgid "Foreign currency exchange losses - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6562
msgid "Foreign currency exchange losses - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42172
msgid "Foreign social security offices"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_683
msgid "Foreign taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65411
#: model:account.account.template,name:l10n_lu.lu_2020_account_755231
msgid "From affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_755232
msgid "From other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65413
msgid "From other receivables from current assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65412
msgid ""
"From undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106145
msgid "Full coverage insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2234
msgid "Furniture"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60313
#: model:account.account.template,name:l10n_lu.lu_2020_account_61843
msgid "Gas"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6121
msgid ""
"General subcontracting (not included in the production of goods and "
"services)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106194
msgid "Gifts and allowance to children"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61514
msgid "Gifts to customers"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_213
#: model:account.account.template,name:l10n_lu.lu_2020_account_1613
msgid "Goodwill acquired for consideration"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65561
msgid "Granted discounts - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65562
msgid "Granted discounts - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_212152
msgid "Greenhouse gas and similar emission quotas"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106153
msgid "Health insurance funds"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106163
msgid "Heating, gas, electricity"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1_assessment_taxable_turnover
msgid "I. ASSESSMENT OF TAXABLE TURNOVER"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_overall_turnover
msgid "I.A. Overall turnover (012)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_vat_acc_scheme
msgid "I.A.1. VAT accounting scheme"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_total_sale
msgid "I.A.2. Total Sales / Receipts (454)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_telecom_service
msgid ""
"I.A.2.a). Telecommunications services, radio and television broadcasting "
"services... (471)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_other_sales
msgid "I.A.2.b). Other sales / receipts (472)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_app_goods_non_bus
msgid ""
"I.A.3. Application of goods for non-business use and for business purposes "
"(Art.13) (455)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1a_non_bus_gs
msgid ""
"I.A.4. Non-business use of goods and supply of services free of charge "
"(Art.16) (456)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_exemptions_deductible_amounts
msgid "I.B. Exemptions and deductible amounts (021)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_1_intra_community_goods_pi_vat
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_1_intra_community_goods_pi_vat
msgid ""
"I.B.1. Intra-Community supply of goods to persons identified for VAT "
"purposes in another Member State (MS) (Art.43(1)(d),(e) and (f)) (3) (457)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_2_export
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_2_export
msgid "I.B.2. Exports (Art.43(1)(a) and (b)) (014)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_3_other_exemptions_art_43
msgid "I.B.3. Other exemptions (Art.43 and 60bis) (015)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_3_other_exemptions_art_43
msgid "I.B.3. Other exemptions (art.43 et 60bis) (015)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater
msgid "I.B.4. Other exemptions (Art.44 and 56quater) (016)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_5_manufactured_tobacco_vat_collected
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_5_manufactured_tobacco_vat_collected
msgid ""
"I.B.5. Manufactured tobacco whose VAT was collected at the source or at the "
"exit of the tax... (017)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_a_subsequent_to_intra_community
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_a_subsequent_to_intra_community
msgid ""
"I.B.6.a) Supply, subsequent to intra-Community acquisitions of goods, in the"
" context of triangular transactions, when the customer identified,... (018)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_b1_non_exempt_customer_vat
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_b1_non_exempt_customer_vat
msgid ""
"I.B.6.b)1) not exempt in the MS where the customer is liable for payment of "
"VAT (Art.17(1)(b)) (5) (423)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_b2_exempt_ms_customer
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_b2_exempt_ms_customer
msgid ""
"I.B.6.b)2) exempt in the MS where the customer is identified (Art.17(1)(b)) "
"(424)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_c_supplies_scope_special_arrangement
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_c_supplies_scope_special_arrangement
msgid ""
"I.B.6.c) Supplies carried out within the scope of the special arrangement of"
" art. 56sexies (226)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_6_d_supplies_other_referred
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_d_supplies_other_referred
msgid "I.B.6.d) Supplies other than referred to in (6)(a) and (6)(b) (019)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1b_7_inland_supplies_for_customer
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_1b_7_inland_supplies_for_customer
msgid ""
"I.B.7. Inland supplies for which the customer is liable for the payment of "
"VAT (419)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_1c_taxable_turnover
msgid "I.C. Taxable turnover (022)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2_assesment_of_tax_due
msgid "II. ASSESSMENT OF TAX DUE (output tax)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_breakdown_taxable_turnover_base
msgid "II.A. Breakdown of taxable turnover – base (037)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_breakdown_taxable_turnover_tax
msgid "II.A. Breakdown of taxable turnover – tax (046)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_0
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_0
msgid "II.A. base 0%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_14
msgid "II.A. base 14% (703)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_17
msgid "II.A. base 17% (701)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_3
msgid "II.A. base 3% (031)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_base_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_8
msgid "II.A. base 8% (705)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_0
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_0
msgid "II.A. tax 0%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_14
msgid "II.A. tax 14% (704)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_17
msgid "II.A. tax 17% (702)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_3
msgid "II.A. tax 3% (040)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2a_tax_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_8
msgid "II.A. tax 8% (706)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_intra_community_acqui_of_goods_base
msgid "II.B. Intra-Community acquisitions of goods – base (051)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_intra_community_acquisitions_goods_tax
msgid "II.B. Intra-Community acquisitions of goods – tax (056)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_14
msgid "II.B. base 14% (713)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_17
msgid "II.B. base 17% (711)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_3
msgid "II.B. base 3% (049)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_8
msgid "II.B. base 8% (715)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_base_exempt
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_exempt
msgid "II.B. base exempt (194)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_14
msgid "II.B. tax 14% (714)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_17
msgid "II.B. tax 17% (712)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_3
msgid "II.B. tax 3% (054)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2b_tax_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_8
msgid "II.B. tax 8% (716)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2c_acquisitions_triangular_transactions_base
msgid ""
"II.C. Acquisitions, in the context of triangular transactions – base (152)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_importation_of_goods_base
msgid "II.D. Importation of goods – base (065)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_importation_of_goods_tax
msgid "II.D. Importation of goods – tax (407)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_14
msgid "II.D.1. for business purposes: base 14% (723)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_17
msgid "II.D.1. for business purposes: base 17% (721)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_3
msgid "II.D.1. for business purposes: base 3% (059)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_8
msgid "II.D.1. for business purposes: base 8% (725)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_exempt
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_exempt
msgid "II.D.1. for business purposes: base exempt (195)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_14
msgid "II.D.1. for business purposes: tax 14% (724)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_17
msgid "II.D.1. for business purposes: tax 17% (722)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_3
msgid "II.D.1. for business purposes: tax 3% (068)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_8
msgid "II.D.1. for business purposes: tax 8% (726)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_14
msgid "II.D.2. for non-business purposes: base 14% (733)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_17
msgid "II.D.2. for non-business purposes: base 17% (731)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_3
msgid "II.D.2. for non-business purposes: base 3% (063)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_8
msgid "II.D.2. for non-business purposes: base 8% (735)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_exempt
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_exempt
msgid "II.D.2. for non-business purposes: base exempt (196)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_14
msgid "II.D.2. for non-business purposes: tax 14% (734)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_17
msgid "II.D.2. for non-business purposes: tax 17% (732)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_3
msgid "II.D.2. for non-business purposes: tax 3% (073)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_8
msgid "II.D.2. for non-business purposes: tax 8% (736)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_supply_of_service_for_customer
msgid ""
"II.E. Supply of services for which the customer is liable for the payment of"
" VAT – base (409)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_supply_of_service_for_customer_liable_for_payment_tax
msgid ""
"II.E. Supply of services for which the customer is liable for the payment of"
" VAT – tax (410)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_base
msgid "II.E.1. base (436)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_14
msgid "II.E.1.a) not exempt within the territory: base 14% (743)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_17
msgid "II.E.1.a) not exempt within the territory: base 17% (741)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_3
msgid "II.E.1.a) not exempt within the territory: base 3% (431)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_8
msgid "II.E.1.a) not exempt within the territory: base 8% (745)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_14
msgid "II.E.1.a) not exempt within the territory: tax 14% (744)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_17
msgid "II.E.1.a) not exempt within the territory: tax 17% (742)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_3
msgid "II.E.1.a) not exempt within the territory: tax 3% (432)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_8
msgid "II.E.1.a) not exempt within the territory: tax 8% (746)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax
msgid "II.E.1.a) tax (462)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_1_b_exempt
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_b_exempt
msgid "II.E.1.b) exempt within the territory: exempt (435)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base
msgid "II.E.2. base (463)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_14
msgid ""
"II.E.2. not established or residing within the Community: base 14% (753)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_17
msgid ""
"II.E.2. not established or residing within the Community: base 17% (751)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_3
msgid ""
"II.E.2. not established or residing within the Community: base 3% (441)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_8
msgid ""
"II.E.2. not established or residing within the Community: base 8% (755)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_exempt
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_exempt
msgid "II.E.2. not established or residing within the Community: exempt (445)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_14
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_14
msgid ""
"II.E.2. not established or residing within the Community: tax 14% (754)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_17
msgid ""
"II.E.2. not established or residing within the Community: tax 17% (752)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_3
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_3
msgid "II.E.2. not established or residing within the Community: tax 3% (442)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_8
msgid "II.E.2. not established or residing within the Community: tax 8% (756)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax
msgid "II.E.2. tax (464)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_base
msgid "II.E.3. base (765)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_base_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_3_base_17
msgid "II.E.3. suppliers established within the territory: base 17% (761)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax_17
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2e_3_tax_17
msgid "II.E.3. suppliers established within the territory: tax 17% (762)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax
msgid "II.E.3. tax (766)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base
msgid ""
"II.F. Supply of goods for which the purchaser is liable for the payment of "
"VAT - base (767)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax
msgid ""
"II.F. Supply of goods for which the purchaser is liable for the payment of "
"VAT - tax (768)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2f_supply_goods_base_8
msgid "II.F. base 8% (763)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_8
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_8
msgid "II.F. tax 8% (764)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2g_special_arrangement
msgid ""
"II.G. Special arrangement for tax suspension: adjustment (Art.60bis, (5) and"
" (8)) (227)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_2h_total_tax_due
msgid "II.H. Total tax due (076)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3_assessment_deducible_tax
msgid "III. ASSESSMENT OF DEDUCTIBLE TAX (input tax)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_total_input_tax
msgid "III.A. Total input tax (093)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_1_invoiced_by_other_taxable_person
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_1_invoiced_by_other_taxable_person
msgid ""
"III.A.1. Invoiced by other taxable persons for goods or services supplied "
"(Art.48(1)(a)) (458)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_2_due_respect_intra_comm_goods
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_2_due_respect_intra_comm_goods
msgid ""
"III.A.2. Due in respect of intra-Community acquisitions of goods "
"(Art.48(1)(b)) (459)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_3_due_paid_respect_importation_goods
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_3_due_paid_respect_importation_goods
msgid ""
"III.A.3. Due or paid in respect of importation of goods (Art.48(1)(c)) (460)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_4_due_respect_application_goods
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_4_due_respect_application_goods
msgid ""
"III.A.4. Due in respect of the application of goods for business purposes "
"(Art.48(1)(d)) (090)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_5_due_under_reverse_charge
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_5_due_under_reverse_charge
msgid "III.A.5. Due under the reverse charge (see points II.E and F) (461)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_6_paid_joint_several_guarantee
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_6_paid_joint_several_guarantee
msgid "III.A.6. Paid as joint and several guarantee (092)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3a_7_adjusted_tax_special_arrangement
#: model:account.tax.report.line,tag_name:l10n_lu.account_tax_report_line_3a_7_adjusted_tax_special_arrangement
msgid ""
"III.A.7. Adjusted tax - special arrangement for tax suspension "
"(Art.60bis(9), subpar. 2) (228)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3b_total_input_tax_nd
msgid "III.B. Total input tax non-deductible (097)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3b1_rel_trans
msgid ""
"III.B.1. relating to transactions which are exempt pursuant to articles 44 "
"and 56quater (094)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3b2_ded_prop
msgid ""
"III.B.2. where the deductible proportion determined in accordance to article"
" 50 is applied (095)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_3c_total_input_tax_deductible
msgid "III.C. Total input tax deductible (102)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6132
msgid "IT services"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_4_tax_tobe_paid_or_reclaimed
msgid "IV. TAX TO BE PAID OR TO BE RECLAIMED"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_4a_total_tax_due
msgid "IV.A. Total tax due (103)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_4a_total_input_tax_deductible
msgid "IV.B. Total input tax deductible (104)"
msgstr ""

#. module: l10n_lu
#: model:account.tax.report.line,name:l10n_lu.account_tax_report_line_4c_exceeding_amount
msgid "IV.C. Exceeding amount (105)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62114
msgid "Incentives, bonuses and commissions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106281
msgid "Income tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106181
msgid "Income tax paid"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_642
msgid "Indemnities, damages and interest"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6183
msgid "Industrial and non-industrial waste treatment"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10621
msgid "Inheritance or donation"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106195
msgid "Inheritance taxes and mutation tax due to death"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62414
msgid "Insolvency insurance premiums"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7481
msgid "Insurance indemnities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6142
msgid "Insurance on rented assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75541
msgid "Interest on amounts owed by affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75542
msgid ""
"Interest on amounts owed by undertakings with which the undertaking is "
"linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75521
msgid "Interest on bank accounts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65511
msgid "Interest on debenture loans - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_65512
msgid "Interest on debenture loans - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_655231
msgid "Interest on financial leases - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_655232
msgid "Interest on financial leases - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75581
msgid "Interest on other amounts receivable - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_75582
msgid "Interest on other amounts receivable - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6553
msgid "Interest on trade payables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7553
msgid "Interest on trade receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65581
msgid "Interest payable on other loans and debts - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65582
msgid "Interest payable on other loans and debts - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65541
msgid "Interest payable to affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_65542
msgid ""
"Interest payable to undertakings with which the undertaking is linked by "
"virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7452
msgid "Interest subsidies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_15
msgid "Interim dividends"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_5172
msgid "Internal transfers : credit balance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_5171
msgid "Internal transfers : debit balance"
msgstr ""

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_LU_IC
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_LU_IC
msgid "Intra-Community Taxable Person"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_3631
msgid "Inventories of buildings for resale in Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_3632
msgid "Inventories of buildings for resale in foreign countries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_303
msgid "Inventories of consumable materials and supplies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_321
msgid "Inventories of finished goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_3621
msgid "Inventories of land for resale in Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_3622
msgid "Inventories of land for resale in foreign countries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_361
msgid "Inventories of merchandise"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_304
msgid "Inventories of packaging"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_301
msgid "Inventories of raw materials"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_323
msgid ""
"Inventories of residual goods (waste, rejected and recuperable material)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_322
msgid "Inventories of semi-finished goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_311
msgid "Inventories of work in progress"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_22151
msgid "Investment properties in Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_22152
msgid "Investment properties in foreign countries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42131
#: model:account.account.template,name:l10n_lu.lu_2011_account_42231
msgid "Investment subsidies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61111
msgid "Land"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60762
msgid "Land for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22112
msgid "Land in foreign countries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22411
msgid "Land, fitting-outs and buildings in Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_22412
msgid "Land, fitting-outs and buildings in foreign countries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7221
msgid "Land, fittings and buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_47162
#: model:account.account.template,name:l10n_lu.lu_2020_account_47262
msgid "Lease debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_131
msgid "Legal reserve"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61341
msgid "Legal, litigation and similar fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_47163
#: model:account.account.template,name:l10n_lu.lu_2020_account_47263
msgid "Life annuities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106141
msgid "Life insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_486
msgid "Linking accounts (branches) - Assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_487
msgid "Linking accounts (branches) - Liabilities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60312
msgid "Liquid fuels"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_61842
msgid "Liquid fuels (oil, motor fuel, etc.)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_chart_1_liquidity_transfer
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5084
msgid "Listed debenture loans"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_235111
msgid "Listed shares"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2236
msgid "Livestock"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_204
msgid "Loan issuances expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_2361
msgid "Loans"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_41112
#: model:account.account.template,name:l10n_lu.lu_2011_account_41122
#: model:account.account.template,name:l10n_lu.lu_2011_account_41212
#: model:account.account.template,name:l10n_lu.lu_2011_account_41222
#: model:account.account.template,name:l10n_lu.lu_2011_account_45112
#: model:account.account.template,name:l10n_lu.lu_2011_account_45122
#: model:account.account.template,name:l10n_lu.lu_2011_account_45212
#: model:account.account.template,name:l10n_lu.lu_2011_account_45222
msgid "Loans and advances"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61332
msgid "Loans' issuance expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75116
#: model:account.account.template,name:l10n_lu.lu_2020_account_65216
#: model:account.account.template,name:l10n_lu.lu_2020_account_75216
msgid "Loans, deposits and claims held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2363
msgid "Long-term receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6037
msgid "Lubricants"
msgstr ""

#. module: l10n_lu
#: model:ir.ui.menu,name:l10n_lu.account_reports_lu_statements_menu
msgid "Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_LU_LU
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_LU_LU
msgid "Luxembourgish Taxable Person"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461221
msgid "MBT - Tax accrual"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461222
msgid "MBT - Tax payable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6721
msgid "MBT - current financial year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6722
msgid "MBT - previous financial years"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2222
msgid "Machinery"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6032
#: model:account.account.template,name:l10n_lu.lu_2020_account_61854
msgid "Maintenance supplies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_615211
msgid "Management (respectively owner and partner)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60761
msgid "Merchandise"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_112
msgid "Merger premium"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6488
msgid "Miscellaneous operating charges"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7488
msgid "Miscellaneous operating income"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221313
msgid "Mixed-use buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6036
msgid "Motor fuels"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2232
msgid "Motor vehicles"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6466
msgid "Motor-vehicle taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4611
msgid "Municipal authorities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42142
msgid "Municipal business tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106284
msgid "Municipal business tax (MBT)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106183
msgid "Municipal business tax - payment in arrears"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461231
msgid "NWT - Tax accrual"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461232
msgid "NWT - Tax payable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6811
msgid "NWT - current financial year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6812
msgid "NWT - previous financial years"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_42143
msgid "Net wealth tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6462
msgid "Non-refundable VAT"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221312
msgid "Non-residential buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7099
msgid "Not allocated rebates, discounts and refunds"
msgstr ""

#. module: l10n_lu
#: model:account.fiscal.position,name:l10n_lu.1_account_fiscal_position_template_LU_NO
#: model:account.fiscal.position.template,name:l10n_lu.account_fiscal_position_template_LU_NO
msgid "Not liable to VAT"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6135
msgid "Notarial and similar fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6035
msgid "Office and administrative supplies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_61851
msgid "Office supplies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75411
msgid "On affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75413
msgid "On other current receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75412
msgid ""
"On undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_1881
msgid "Operating provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42132
#: model:account.account.template,name:l10n_lu.lu_2011_account_42232
msgid "Operating subsidies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61418
#: model:account.account.template,name:l10n_lu.lu_2011_account_6228
#: model:account.account.template,name:l10n_lu.lu_2020_account_61128
#: model:account.account.template,name:l10n_lu.lu_2020_account_61158
#: model:account.account.template,name:l10n_lu.lu_2020_account_61228
#: model:account.account.template,name:l10n_lu.lu_2020_account_61858
msgid "Other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106178
msgid "Other acquisitions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61338
msgid ""
"Other banking and similar services (except interest and similar expenses)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6218
msgid "Other benefits"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221318
msgid "Other buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_168
msgid "Other capital investment subsidies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_518
msgid "Other cash amounts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_708
msgid "Other components of turnover"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6038
msgid "Other consumable supplies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106158
msgid "Other contributions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106248
msgid "Other disposals"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6468
msgid "Other duties and taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6581
msgid "Other financial charges - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6582
msgid "Other financial charges - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7581
msgid "Other financial income - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7582
msgid "Other financial income - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2238
msgid "Other fixtures"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7223
#: model:account.account.template,name:l10n_lu.lu_2011_account_7333
msgid ""
"Other fixtures and fittings, tools and equipment (included motor vehicles)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2243
msgid ""
"Other fixtures and fittings, tools and equipment (including rolling stock)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6738
msgid "Other foreign income taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421818
#: model:account.account.template,name:l10n_lu.lu_2020_account_46158
msgid "Other foreign taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106168
msgid "Other in kind withdrawals"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421628
#: model:account.account.template,name:l10n_lu.lu_2011_account_461428
msgid "Other indirect taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6148
msgid "Other insurances"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221118
msgid "Other land"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_47161
#: model:account.account.template,name:l10n_lu.lu_2020_account_47261
msgid "Other loans"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4718
#: model:account.account.template,name:l10n_lu.lu_2011_account_4728
msgid "Other miscellaneous debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6188
msgid "Other miscellaneous external charges"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42188
#: model:account.account.template,name:l10n_lu.lu_2011_account_42288
msgid "Other miscellaneous receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5088
msgid "Other miscellaneous transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_45118
#: model:account.account.template,name:l10n_lu.lu_2011_account_45128
#: model:account.account.template,name:l10n_lu.lu_2011_account_45218
#: model:account.account.template,name:l10n_lu.lu_2011_account_45228
msgid "Other payables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106148
msgid "Other private insurance premiums"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61348
msgid "Other professional fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6088
msgid "Other purchases included in the production of goods and services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61518
msgid "Other purchases of advertising services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6082
msgid ""
"Other purchases of material included in the production of goods and services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_41118
#: model:account.account.template,name:l10n_lu.lu_2011_account_41128
#: model:account.account.template,name:l10n_lu.lu_2011_account_41218
#: model:account.account.template,name:l10n_lu.lu_2011_account_41228
#: model:account.account.template,name:l10n_lu.lu_2011_account_42168
#: model:account.account.template,name:l10n_lu.lu_2020_account_6454
msgid "Other receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_64658
msgid "Other registration fees, stamp duties and mortgage duties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6138
msgid "Other remuneration of intermediaries and professional fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_1381
msgid "Other reserves available for distribution"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_128
msgid "Other revaluation reserves"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2358
msgid "Other securities held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_23528
msgid "Other securities held as fixed assets (creditor's right)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_23518
msgid "Other securities held as fixed assets (equity right)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_47164
#: model:account.account.template,name:l10n_lu.lu_2020_account_47264
msgid "Other similar debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_208
msgid "Other similar expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6438
msgid "Other similar remuneration"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_64158
#: model:account.account.template,name:l10n_lu.lu_2011_account_721258
#: model:account.account.template,name:l10n_lu.lu_2011_account_74158
#: model:account.account.template,name:l10n_lu.lu_2020_account_703158
msgid "Other similar rights and assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_212158
msgid "Other similar rights and assets acquired for consideration"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_212258
msgid "Other similar rights and assets created by the undertaking itself"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42178
#: model:account.account.template,name:l10n_lu.lu_2011_account_4628
msgid "Other social bodies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6232
msgid "Other social security costs (including illness, accidents, a.s.o.)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106198
msgid "Other special private withdrawals"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6248
msgid "Other staff expenses not mentioned above"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42138
#: model:account.account.template,name:l10n_lu.lu_2011_account_42238
msgid "Other subsidies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7458
msgid "Other subsidies for operating activities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_621128
msgid "Other supplements"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106288
msgid "Other tax refunds"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106188
#: model:account.account.template,name:l10n_lu.lu_2011_account_688
msgid "Other taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75488
#: model:account.account.template,name:l10n_lu.lu_2020_account_65428
#: model:account.account.template,name:l10n_lu.lu_2020_account_75318
#: model:account.account.template,name:l10n_lu.lu_2020_account_75428
msgid "Other transferable securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6168
msgid "Other transportation"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60814
msgid "Outsourcing included in the production of goods and services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_621123
msgid "Overtime"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75482
#: model:account.account.template,name:l10n_lu.lu_2020_account_65422
#: model:account.account.template,name:l10n_lu.lu_2020_account_75312
#: model:account.account.template,name:l10n_lu.lu_2020_account_75422
msgid "Own shares or corporate units"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_502
msgid "Own shares or own corporate units"
msgstr ""

#. module: l10n_lu
#: model:account.chart.template,name:l10n_lu.lu_2011_chart_1
msgid "PCMN Luxembourg"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_233
#: model:account.account.template,name:l10n_lu.lu_2011_account_75113
#: model:account.account.template,name:l10n_lu.lu_2020_account_65213
#: model:account.account.template,name:l10n_lu.lu_2020_account_75213
msgid "Participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_21212
#: model:account.account.template,name:l10n_lu.lu_2011_account_21222
#: model:account.account.template,name:l10n_lu.lu_2011_account_6412
#: model:account.account.template,name:l10n_lu.lu_2011_account_72122
#: model:account.account.template,name:l10n_lu.lu_2011_account_7412
#: model:account.account.template,name:l10n_lu.lu_2020_account_70312
msgid "Patents"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10622
msgid "Personal holdings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2221
msgid "Plant"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2242
#: model:account.account.template,name:l10n_lu.lu_2011_account_7222
msgid "Plant and machinery"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61531
msgid "Postal charges"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62411
msgid "Premiums for external pensions funds"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_114
msgid "Premiums on conversion of bonds into shares"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61511
msgid "Press advertising"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_67322
msgid "Previous financial years"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106174
#: model:account.account.template,name:l10n_lu.lu_2011_account_106244
msgid "Private buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106172
#: model:account.account.template,name:l10n_lu.lu_2011_account_106242
msgid "Private car"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106171
#: model:account.account.template,name:l10n_lu.lu_2011_account_106241
msgid "Private furniture"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106173
msgid "Private held securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10623
msgid "Private loans"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10613
msgid "Private share of medical services expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106243
msgid "Private shares / bonds"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7451
msgid "Product subsidies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221112
msgid "Property rights and similar"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_181
msgid "Provisions for pensions and similar obligations"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_182
msgid "Provisions for taxation"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_621122
msgid "Public holidays"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6083
msgid "Purchase of greenhouse gas and similar emission quotas"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_45111
#: model:account.account.template,name:l10n_lu.lu_2011_account_45121
#: model:account.account.template,name:l10n_lu.lu_2011_account_45211
#: model:account.account.template,name:l10n_lu.lu_2011_account_45221
msgid "Purchases and services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6063
msgid "Purchases of buildings for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6062
msgid "Purchases of land for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6061
msgid "Purchases of merchandise"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_604
msgid "Purchases of packaging"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_601
msgid "Purchases of raw materials"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7095
msgid "RDR on commissions and brokerage fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7098
msgid "RDR on other components of turnover"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6098
msgid "RDR on purchases included in the production of goods and services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6093
msgid "RDR on purchases of consumable materials and supplies"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6096
msgid "RDR on purchases of merchandise and other goods for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6094
msgid "RDR on purchases of packaging"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6091
msgid "RDR on purchases of raw materials"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7092
msgid "RDR on sales of goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7096
msgid "RDR on sales of merchandise and other goods for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7094
msgid "RDR on sales of packages"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7093
msgid "RDR on sales of services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7352
msgid ""
"RVA on amounts owed by affiliated undertakings and undertakings with which "
"the undertaking is linked by virtue of participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_73313
msgid "RVA on buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7322
msgid ""
"RVA on concessions, patents, licences, trademarks and similar rights and "
"assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7321
msgid "RVA on development costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7324
msgid "RVA on down payments and intangible fixed assets under development"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7334
msgid "RVA on down payments and tangible fixed assets under development"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7345
msgid "RVA on down payments on inventories"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_73314
msgid "RVA on fixtures and fittings-out of buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_73312
msgid "RVA on fixtures and fittings-out of land"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7343
msgid "RVA on inventories of goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7344
msgid "RVA on inventories of merchandise and other goods for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7341
msgid "RVA on inventories of raw materials and consumables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7342
msgid "RVA on inventories of work and contracts in progress"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_73311
msgid "RVA on land"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7353
msgid "RVA on other receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7332
msgid "RVA on plant and machinery"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7351
msgid "RVA on trade receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6461
msgid "Real property tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_619
msgid "Rebates, discounts and refunds received on other external charges"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10627
msgid "Received child benefit"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4711
#: model:account.account.template,name:l10n_lu.lu_2020_account_4721
msgid "Received deposits and guarantees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10625
msgid "Received rents"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10626
msgid "Received wages or pensions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61524
msgid "Receptions and entertainment costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106193
msgid "Refund of private debts"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6219
msgid "Refunds on wages paid"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421621
#: model:account.account.template,name:l10n_lu.lu_2011_account_461421
msgid "Registration duties"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_64651
msgid "Registration fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61522
msgid "Relocation expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106162
msgid "Rent"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_70322
msgid "Rental income from movable property"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_70321
msgid "Rental income from real property"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7422
msgid "Rental income on movable property"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7421
msgid "Rental income on real property"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106191
msgid "Repairs to private buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60812
msgid "Research and development"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_13821
msgid "Reserve for net wealth tax (NWT)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_132
msgid "Reserves for own shares or own corporate units"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_13822
msgid "Reserves in application of fair value"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_122
msgid "Reserves in application of the equity method"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_13828
msgid "Reserves not available for distribution not mentioned above"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_133
msgid "Reserves provided for by the articles of association"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_221311
msgid "Residential buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_142
msgid "Result for the financial year"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1412
msgid "Results brought forward (assigned)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1411
msgid "Results brought forward in the process of assignment"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2237
msgid "Returnable packaging"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7591
msgid "Reversals of financial provisions - affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7592
msgid "Reversals of financial provisions - other"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7492
msgid "Reversals of operating provisions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_779
msgid "Reversals of provisions for deferred taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7491
msgid "Reversals of provisions for taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61123
#: model:account.account.template,name:l10n_lu.lu_2011_account_61153
#: model:account.account.template,name:l10n_lu.lu_2011_account_61223
#: model:account.account.template,name:l10n_lu.lu_2011_account_61412
msgid "Rolling stock"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_703001
msgid "Sale of Services"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7063
msgid "Sales of buildings for resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7021
msgid "Sales of finished goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7062
msgid "Sales of land resale"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7061
msgid "Sales of merchandise"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_704
msgid "Sales of packaging"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7023
msgid "Sales of residual products"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7022
msgid "Sales of semi-finished goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7039
msgid "Sales of services in the course of completion"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7033
msgid "Sales of services not mentioned above"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7029
msgid "Sales of work in progress"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61512
msgid "Samples"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_75115
#: model:account.account.template,name:l10n_lu.lu_2020_account_65215
#: model:account.account.template,name:l10n_lu.lu_2020_account_75215
msgid "Securities held as fixed assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6113
msgid "Service charges and co-ownership expenses"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_201
msgid "Set-up and start-up costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62116
msgid "Severance pay"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_657
msgid ""
"Share in the losses of undertakings accounted for under the equity method"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_757
msgid ""
"Share of profit from undertakings accounted for under the equity method"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_111
msgid "Share premium"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5081
msgid "Shares - listed securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5082
msgid "Shares - unlisted securities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_231
#: model:account.account.template,name:l10n_lu.lu_2011_account_501
#: model:account.account.template,name:l10n_lu.lu_2011_account_75111
#: model:account.account.template,name:l10n_lu.lu_2011_account_75481
#: model:account.account.template,name:l10n_lu.lu_2020_account_65211
#: model:account.account.template,name:l10n_lu.lu_2020_account_65421
#: model:account.account.template,name:l10n_lu.lu_2020_account_75211
#: model:account.account.template,name:l10n_lu.lu_2020_account_75311
#: model:account.account.template,name:l10n_lu.lu_2020_account_75421
msgid "Shares in affiliated undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_503
#: model:account.account.template,name:l10n_lu.lu_2011_account_75483
#: model:account.account.template,name:l10n_lu.lu_2020_account_65423
#: model:account.account.template,name:l10n_lu.lu_2020_account_75313
#: model:account.account.template,name:l10n_lu.lu_2020_account_75423
msgid ""
"Shares in undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_2353
msgid "Shares of collective investment funds"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_61852
msgid "Small equipment"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106151
msgid "Social Security"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_42171
#: model:account.account.template,name:l10n_lu.lu_2011_account_4621
msgid "Social Security office (CCSS)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6231
msgid "Social security on pensions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_21213
#: model:account.account.template,name:l10n_lu.lu_2011_account_21223
#: model:account.account.template,name:l10n_lu.lu_2011_account_6413
#: model:account.account.template,name:l10n_lu.lu_2011_account_72123
#: model:account.account.template,name:l10n_lu.lu_2011_account_7413
#: model:account.account.template,name:l10n_lu.lu_2020_account_70313
msgid "Software licences"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_60311
#: model:account.account.template,name:l10n_lu.lu_2020_account_61841
msgid "Solid fuels"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61517
msgid "Sponsorship"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_615212
msgid "Staff"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_4221
msgid "Staff - advances and down payments"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4715
msgid ""
"State - Greenhous gas and similar emission quotas to be returned or acquired"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_483
msgid "State - Greenhouse gas and similar emission quotas received"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6221
msgid "Students"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_101
msgid "Subscribed capital"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_103
msgid "Subscribed capital called but unpaid"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_102
msgid "Subscribed capital not called"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421622
#: model:account.account.template,name:l10n_lu.lu_2011_account_461422
#: model:account.account.template,name:l10n_lu.lu_2011_account_682
msgid "Subscription tax"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_7454
msgid "Subsidies in favour of employment development"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1621
msgid "Subsidies on land, fitting-outs and buildings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1623
msgid ""
"Subsidies on other fixtures, fittings, tools and equipment (including "
"rolling stock)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_1622
msgid "Subsidies on plant and machinery"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_621121
msgid "Sunday"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_44111
#: model:account.account.template,name:l10n_lu.lu_2011_account_44121
msgid "Suppliers"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_44112
msgid "Suppliers - invoices not yet received"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_44113
#: model:account.account.template,name:l10n_lu.lu_2020_account_44123
msgid "Suppliers with a debit balance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6186
msgid "Surveillance and security charges"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_62117
msgid "Survivor's pay"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_0
msgid "TVA 0%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_10
msgid "TVA 10%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_12
msgid "TVA 12%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_14
msgid "TVA 14%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_15
msgid "TVA 15%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_17
msgid "TVA 17%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_3
msgid "TVA 3%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_6
msgid "TVA 6%"
msgstr ""

#. module: l10n_lu
#: model:account.tax.group,name:l10n_lu.tax_group_8
msgid "TVA 8%"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60811
msgid "Tailoring"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6733
msgid "Taxes levied on non-resident undertakings"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_61532
msgid "Telecommunication costs"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106165
msgid "Telephone"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7471
msgid "Temporarily not taxable capital gains not reinvested"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_7472
#: model:account.account.template,name:l10n_lu.lu_2020_account_138232
msgid "Temporarily not taxable capital gains reinvested"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_138231
msgid "Temporarily not taxable capital gains to reinvest"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_123
msgid "Temporarily not taxable currency translation adjustments"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6171
msgid "Temporary staff"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106144
#: model:account.account.template,name:l10n_lu.lu_2011_account_6146
msgid "Third-party insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2233
msgid "Tools"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_41111
#: model:account.account.template,name:l10n_lu.lu_2011_account_41121
#: model:account.account.template,name:l10n_lu.lu_2011_account_41211
#: model:account.account.template,name:l10n_lu.lu_2011_account_41221
#: model:account.account.template,name:l10n_lu.lu_2011_account_6451
msgid "Trade receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6414
msgid "Trademarks and franchise"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_21214
#: model:account.account.template,name:l10n_lu.lu_2011_account_21224
#: model:account.account.template,name:l10n_lu.lu_2011_account_72124
#: model:account.account.template,name:l10n_lu.lu_2011_account_7414
#: model:account.account.template,name:l10n_lu.lu_2020_account_70314
msgid "Trademarks and franchises"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_484
msgid "Transitory or suspense accounts - Assets"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_485
msgid "Transitory or suspense accounts - Liabilities"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_6143
msgid "Transport insurance"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_2231
msgid "Transportation and handling equipment"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6161
msgid "Transportation of purchased goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6162
msgid "Transportation of sold goods"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6099
msgid "Unallocated RDR"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_5085
msgid "Unlisted debenture loans"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_235112
msgid "Unlisted shares"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461418
msgid "VAT - Other payables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421618
msgid "VAT - Other receivables"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421613
msgid "VAT down payments made"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461413
msgid "VAT down payments received"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_421611
msgid "VAT paid and recoverable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_461412
msgid "VAT payable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_421612
msgid "VAT receivable"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_461411
msgid "VAT received"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_4019
#: model:account.account.template,name:l10n_lu.lu_2011_account_4029
#: model:account.account.template,name:l10n_lu.lu_2011_account_41119
#: model:account.account.template,name:l10n_lu.lu_2011_account_41129
#: model:account.account.template,name:l10n_lu.lu_2011_account_41219
#: model:account.account.template,name:l10n_lu.lu_2011_account_41229
#: model:account.account.template,name:l10n_lu.lu_2011_account_42119
#: model:account.account.template,name:l10n_lu.lu_2011_account_42189
#: model:account.account.template,name:l10n_lu.lu_2011_account_42289
msgid "Value adjustments"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106161
msgid "Wages"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_106164
msgid "Water"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_60314
msgid "Water and sewage"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_61844
msgid "Water and waste water"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_10612
msgid "Withdrawals of merchandise, finished products and services (at cost)"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2020_account_62413
msgid "Withholding tax on complementary pensions"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_46126
#: model:account.account.template,name:l10n_lu.lu_2020_account_42146
msgid "Withholding tax on director's fees"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_46125
#: model:account.account.template,name:l10n_lu.lu_2020_account_42145
msgid "Withholding tax on financial investment income"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_46124
#: model:account.account.template,name:l10n_lu.lu_2020_account_42144
msgid "Withholding tax on wages and salaries"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6731
msgid "Withholding taxes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6034
#: model:account.account.template,name:l10n_lu.lu_2020_account_61853
msgid "Work clothes"
msgstr ""

#. module: l10n_lu
#: model:account.account.template,name:l10n_lu.lu_2011_account_6033
msgid "Workshop, factory and store supplies and small equipment"
msgstr ""
