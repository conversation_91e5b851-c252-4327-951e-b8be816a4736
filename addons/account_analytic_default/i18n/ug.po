# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_analytic_default
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-05 12:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:07+0000\n"
"Language-Team: Uighur (https://www.transifex.com/odoo/teams/41243/ug/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ug\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<i class=\"fa fa-calendar\"/> From"
msgstr ""

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Customer</strong>"
msgstr ""

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "<strong>Product</strong>"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__account_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Account"
msgstr ""

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Accounts"
msgstr ""

#. module: account_analytic_default
#: code:addons/account_analytic_default/models/account_analytic_default.py:0
#, python-format
msgid ""
"An analytic default requires at least an analytic account or an analytic "
"tag."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__analytic_id
#: model:ir.model.fields,field_description:account_analytic_default.field_account_move_line__analytic_account_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Analytic Account"
msgstr ""

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Analytic Default Rule"
msgstr ""

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_analytic_default_list
#: model:ir.ui.menu,name:account_analytic_default.menu_analytic_default_list
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_tree
msgid "Analytic Defaults"
msgstr ""

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_analytic_default
msgid "Analytic Distribution"
msgstr ""

#. module: account_analytic_default
#: model:ir.actions.act_window,name:account_analytic_default.action_product_default_list
#: model:ir.actions.act_window,name:account_analytic_default.analytic_rule_action_user
msgid "Analytic Rules"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__analytic_tag_ids
#: model:ir.model.fields,field_description:account_analytic_default.field_account_move_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__company_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Company"
msgstr ""

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Conditions"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__create_uid
msgid "Created by"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__create_date
msgid "Created on"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__date_stop
msgid "Default end date for this Analytic Account."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__date_start
msgid "Default start date for this Analytic Account."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__display_name
msgid "Display Name"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__date_stop
msgid "End Date"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__sequence
msgid ""
"Gives the sequence order when displaying a list of analytic distribution"
msgstr ""

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Group By"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__id
msgid "ID"
msgstr ""

#. module: account_analytic_default
#: model:ir.model,name:account_analytic_default.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__partner_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Partner"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__product_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "Product"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__company_id
msgid ""
"Select a company which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"company, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__partner_id
msgid ""
"Select a partner which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"partner, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__product_id
msgid ""
"Select a product which will use analytic account specified in analytic "
"default (e.g. create new customer invoice or Sales order if we select this "
"product, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__user_id
msgid ""
"Select a user which will use analytic account specified in analytic default."
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,help:account_analytic_default.field_account_analytic_default__account_id
msgid ""
"Select an accounting account which will use analytic account specified in "
"analytic default (e.g. create new customer invoice or Sales order if we "
"select this account, it will automatically take this as an analytic account)"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__sequence
msgid "Sequence"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__date_start
msgid "Start Date"
msgstr ""

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form
msgid "Tags"
msgstr ""

#. module: account_analytic_default
#: model:ir.model.fields,field_description:account_analytic_default.field_account_analytic_default__user_id
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_form_search
msgid "User"
msgstr ""

#. module: account_analytic_default
#: model_terms:ir.ui.view,arch_db:account_analytic_default.view_account_analytic_default_kanban
msgid "to"
msgstr ""
