<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="demo_bank_statement_1" model="account.bank.statement">
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'bank'),
                ('company_id', '=', obj().env.company.id)]"/>
            <field name="date" eval="time.strftime('%Y')+'-01-01'"/>
            <field name="name" eval="'BNK/%s/0001' % time.strftime('%Y')"/>
            <field name="balance_end_real">8998.2</field>
            <field name="balance_start">5103.0</field>
        </record>

        <record id="demo_bank_statement_line_1" model="account.bank.statement.line">
            <field name="ref"></field>
            <field name="statement_id" ref="l10n_generic_coa.demo_bank_statement_1"/>
            <field name="sequence">1</field>
            <field name="name" eval="'INV/%s/0002 and INV/%s/0003' % (time.strftime('%Y'), time.strftime('%Y'))"/>
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'bank'),
                ('company_id', '=', obj().env.company.id)]"/>
            <field name="amount">1275.0</field>
            <field name="date" eval="time.strftime('%Y')+'-01-01'"/>
            <field name="partner_id" ref="base.res_partner_2"/>
        </record>

        <record id="demo_bank_statement_line_2" model="account.bank.statement.line">
            <field name="ref"></field>
            <field name="statement_id" ref="l10n_generic_coa.demo_bank_statement_1"/>
            <field name="sequence">2</field>
            <field name="name">Bank fees</field>
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'bank'),
                ('company_id', '=', obj().env.company.id)]"/>
            <field name="amount">-32.58</field>
            <field name="date" eval="time.strftime('%Y')+'-01-01'"/>
        </record>

        <record id="demo_bank_statement_line_3" model="account.bank.statement.line">
            <field name="ref"></field>
            <field name="statement_id" ref="l10n_generic_coa.demo_bank_statement_1"/>
            <field name="sequence">3</field>
            <field name="name">Prepayment</field>
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'bank'),
                ('company_id', '=', obj().env.company.id)]"/>
            <field name="amount">650.0</field>
            <field name="date" eval="time.strftime('%Y')+'-01-01'"/>
            <field name="partner_id" ref="base.res_partner_12"/>
        </record>

        <record id="demo_bank_statement_line_4" model="account.bank.statement.line">
            <field name="ref"></field>
            <field name="statement_id" ref="l10n_generic_coa.demo_bank_statement_1"/>
            <field name="sequence">4</field>
            <field name="name" eval="'First 2000 $ of invoice %s/0001' % time.strftime('%Y')"/>
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'bank'),
                ('company_id', '=', obj().env.company.id)]"/>
            <field name="amount">2000.0</field>
            <field name="date" eval="time.strftime('%Y')+'-01-01'"/>
            <field name="partner_id" ref="base.res_partner_12"/>
        </record>

        <record id="demo_bank_statement_line_5" model="account.bank.statement.line">
            <field name="ref"></field>
            <field name="statement_id" ref="l10n_generic_coa.demo_bank_statement_1"/>
            <field name="sequence">5</field>
            <field name="name">Last Year Interests</field>
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'bank'),
                ('company_id', '=', obj().env.company.id)]"/>
            <field name="amount">102.78</field>
            <field name="date" eval="time.strftime('%Y')+'-01-01'"/>
            <field name="account_id" model="account.account" search="[
              ('user_type_id', '=', ref('account.data_account_type_revenue')),
              ('tag_ids', 'in', [ref('account.account_tag_financing')]),
              ('company_id', '=', obj().env.company.id)]"/>
        </record>

        <record id="demo_bank_statement_line_6" model="account.bank.statement.line">
            <field name="ref"></field>
            <field name="statement_id" ref="l10n_generic_coa.demo_bank_statement_1"/>
            <field name="sequence">1</field>
            <field name="name" eval="'INV/'+time.strftime('%Y')+'/0002'"/>
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'bank'),
                ('company_id', '=', obj().env.company.id)]"/>
            <field name="amount">750.0</field>
            <field name="date" eval="time.strftime('%Y')+'-01-01'"/>
            <field name="partner_id" ref="base.res_partner_2"/>
        </record>

        <record id="demo_bank_statement_line_7" model="account.bank.statement.line">
            <field name="ref"></field>
            <field name="statement_id" ref="l10n_generic_coa.demo_bank_statement_1"/>
            <field name="sequence">7</field>
            <field name="name">R:9772938  10/07 AX ********** T:5 BRT: 100,00€ C/ croip</field>
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'bank'),
                ('company_id', '=', obj().env.company.id)]"/>
            <field name="amount">96.67</field>
            <field name="date" eval="time.strftime('%Y')+'-01-01'"/>
            <field name="partner_id" ref="base.res_partner_2"/>
        </record>

        <function model="account.bank.statement.line" name="fast_counterpart_creation">
            <value eval="[ref('demo_bank_statement_line_5')]"/>
        </function>

        <!-- Also create a pdf attachment in chatter -->

        <record id="ir_attachment_bank_statement_1" model="ir.attachment">
            <field name="type">binary</field>
            <field name="datas" type="base64" file="l10n_generic_coa/static/src/demo/bank_statement_yourcompany_1.pdf"/>
            <field name="name">bank_statement_yourcompany_demo.pdf</field>
            <field name="res_model">account.bank.statement</field>
            <field name="res_id" ref="demo_bank_statement_1"/>
        </record>

        <record id="demo_bank_statement_1" model="account.bank.statement">
            <field name="message_main_attachment_id" ref="ir_attachment_bank_statement_1"/>
        </record>

        <record id="mail_message_bank_statement_1" model="mail.message">
            <field name="model">account.bank.statement</field>
            <field name="res_id" ref="l10n_generic_coa.demo_bank_statement_1"/>
            <field name="body">Bank statement attachment</field>
            <field name="message_type">comment</field>
            <field name="author_id" ref="base.partner_demo"/>
            <field name="attachment_ids" eval="[(6, 0, [ref('ir_attachment_bank_statement_1')])]"/>
        </record>

    </data>
</odoo>
