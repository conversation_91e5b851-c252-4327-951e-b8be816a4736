# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_margin
# 
# Translators:
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:14+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2019\n"
"Language-Team: Hebrew (https://www.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: sale_margin
#: model:ir.model.fields,field_description:sale_margin.field_sale_order_line__purchase_price
msgid "Cost"
msgstr "עלות"

#. module: sale_margin
#: model:ir.model.fields,help:sale_margin.field_sale_order__margin
msgid ""
"It gives profitability by calculating the difference between the Unit Price "
"and the cost."
msgstr "זה נותן את הרווחיות על ידי חישוב ההפרש בין מחיר היחידה והעלות."

#. module: sale_margin
#: model:ir.model.fields,field_description:sale_margin.field_sale_order__margin
#: model:ir.model.fields,field_description:sale_margin.field_sale_order_line__margin
#: model:ir.model.fields,field_description:sale_margin.field_sale_report__margin
msgid "Margin"
msgstr "שוליים"

#. module: sale_margin
#: model:ir.model,name:sale_margin.model_sale_report
msgid "Sales Analysis Report"
msgstr "דוח ניתוח נתוני מכירות"

#. module: sale_margin
#: model:ir.model,name:sale_margin.model_sale_order
msgid "Sales Order"
msgstr "הזמנת לקוח"

#. module: sale_margin
#: model:ir.model,name:sale_margin.model_sale_order_line
msgid "Sales Order Line"
msgstr "שורת הזמנת לקוח"
