# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* utm
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> islam <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-23 11:33+0000\n"
"PO-Revision-Date: 2019-08-26 09:15+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Bengali (https://www.transifex.com/odoo/teams/41243/bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "সক্রিয়"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Add a new tag"
msgstr ""

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_campaign__is_website
msgid "Allows us to filter relevant Campaign"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Archived"
msgstr "আর্কাইভ করা"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
msgid "Campaign"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "Campaign Name"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_stage
msgid "Campaign Stage"
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_tag
#: model_terms:ir.ui.view,arch_db:utm.utm_tag_view_tree
msgid "Campaign Tags"
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_action
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns"
msgstr ""

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__color
#: model:ir.model.fields,field_description:utm.field_utm_tag__color
msgid "Color Index"
msgstr "রঙ সূচক"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Colour"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid "Create a stage for your campaigns"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_uid
msgid "Created by"
msgstr "দ্বারা সৃষ্টি"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_date
msgid "Created on"
msgstr "তৈরি"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid "Define a new UTM campaign"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Define a new UTM medium"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "Define a new UTM source"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Delete"
msgstr "মুছে ফেলুন"

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_2
msgid "Design"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_mixin__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
#: model:ir.model.fields,field_description:utm.field_utm_stage__display_name
#: model:ir.model.fields,field_description:utm.field_utm_tag__display_name
msgid "Display Name"
msgstr "প্রদর্শন নাম"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Dropdown menu"
msgstr "ড্রপডাউন মেনু"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_products
msgid "Email Campaign - Products"
msgstr ""

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_facebook
msgid "Facebook"
msgstr "ফেসবুক"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Group By"
msgstr "গ্রুপ দ্বারা"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr "এইচটিটিপি রাউটিং"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_mixin__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
#: model:ir.model.fields,field_description:utm.field_utm_stage__id
#: model:ir.model.fields,field_description:utm.field_utm_tag__id
msgid "ID"
msgstr "আইডি "

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__is_website
msgid "Is Website"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign____last_update
#: model:ir.model.fields,field_description:utm.field_utm_medium____last_update
#: model:ir.model.fields,field_description:utm.field_utm_mixin____last_update
#: model:ir.model.fields,field_description:utm.field_utm_source____last_update
#: model:ir.model.fields,field_description:utm.field_utm_stage____last_update
#: model:ir.model.fields,field_description:utm.field_utm_tag____last_update
msgid "Last Modified on"
msgstr "সর্বশেষ সংশোধিত"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_uid
msgid "Last Updated by"
msgstr "সর্বশেষ আপডেট করেছেন"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_date
msgid "Last Updated on"
msgstr "সর্বশেষ আপডেট হয়েছে"

#. module: utm
#: model:utm.source,name:utm.utm_source_mailing
msgid "Lead Recall"
msgstr ""

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr ""

#. module: utm
#: model:utm.tag,name:utm.utm_tag_1
msgid "Marketing"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
msgid "Medium"
msgstr "মধ্যম"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Medium Name"
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "Mediums"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__name
#: model:ir.model.fields,field_description:utm.field_utm_tag__name
msgid "Name"
msgstr "নাম"

#. module: utm
#: model:utm.stage,name:utm.default_utm_stage
msgid "New"
msgstr "নতুন "

#. module: utm
#: model:utm.source,name:utm.utm_source_newsletter
msgid "Newsletter"
msgstr "নিউজলেটার"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__user_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Responsible"
msgstr "দায়িত্বপ্রাপ্ত"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr ""

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_1
msgid "Schedule"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Search UTM Medium"
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_search_engine
msgid "Search engine"
msgstr ""

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_3
msgid "Sent"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__sequence
msgid "Sequence"
msgstr "ক্রম"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Settings"
msgstr "সেটিংস"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Source"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Stage"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_search
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_tree
msgid "Stages"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid ""
"Stages allow you to organize your workflow  (e.g. : plan, design, in "
"progress,  done, …)."
msgstr ""

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_tag_name_uniq
msgid "Tag name already exists !"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__tag_ids
msgid "Tags"
msgstr ""

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_twitter
msgid "Twitter"
msgstr "টুইটার"

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "UTM Campaign"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "UTM Campaigns"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_stage
msgid "UTM Stages"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_tag
msgid "UTM Tag"
msgstr ""

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Use tags to organize campaigns and easily filter them."
msgstr ""
