# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_expense_check
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Spanish (Peru) (https://www.transifex.com/odoo/teams/41243/es_PE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_PE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_expense_check
#: model:ir.model.fields,field_description:hr_expense_check.field_hr_expense_sheet_register_payment_wizard_check_amount_in_words
msgid "Amount in Words"
msgstr ""

#. module: hr_expense_check
#: model:ir.model.fields,field_description:hr_expense_check.field_hr_expense_sheet_register_payment_wizard_check_number
msgid "Check Number"
msgstr ""

#. module: hr_expense_check
#: model:ir.model.fields,help:hr_expense_check.field_hr_expense_sheet_register_payment_wizard_check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""

#. module: hr_expense_check
#: model:ir.model.fields,field_description:hr_expense_check.field_hr_expense_sheet_register_payment_wizard_payment_method_code_2
msgid "Code"
msgstr ""

#. module: hr_expense_check
#: model:ir.model,name:hr_expense_check.model_hr_expense_sheet_register_payment_wizard
msgid "Expense Report Register Payment wizard"
msgstr ""

#. module: hr_expense_check
#: model:ir.model.fields,field_description:hr_expense_check.field_hr_expense_sheet_register_payment_wizard_check_manual_sequencing
msgid "Manual Numbering"
msgstr ""

#. module: hr_expense_check
#: model:ir.model.fields,help:hr_expense_check.field_hr_expense_sheet_register_payment_wizard_check_number
msgid ""
"Number of the check corresponding to this payment. If your pre-printed check"
" are not already numbered, you can manage the numbering in the journal "
"configuration page."
msgstr ""

#. module: hr_expense_check
#: model:ir.model.fields,help:hr_expense_check.field_hr_expense_sheet_register_payment_wizard_payment_method_code_2
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""
