# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_holidays
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:17+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "Ligne analytique"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
msgid "Analytic Lines"
msgstr "Lignes analytiques"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#, python-format
msgid ""
"Both the internal project and task are required to generate a timesheet for "
"the time off. If you don't want a timesheet, you should leave the internal "
"project and task empty."
msgstr ""
"Le projet interne et la tâche sont requis pour générer des feuilles de temps"
" pour les congés. Si vous ne souhaitez pas de feuille de temps, vous devriez"
" laisser le projet interne et la tâche vides."

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_company__leave_timesheet_project_id
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__leave_timesheet_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr ""
"Valeur de projet par défaut pour les feuilles de temps générées à partir de "
"ce type de congés."

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheet"
msgstr "Générer une feuille de temps"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a time off, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""
"Si coché, une feuille de temps sera générée dans le projet Vacances de la "
"société lorsque le congé sera validé."

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_project_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_project_id
#, python-format
msgid "Internal Project"
msgstr "Projet interne"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__holiday_id
msgid "Leave Request"
msgstr "Demande de congé"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "Projet"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "Tâche"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
msgid "Task for timesheet"
msgstr "Tâche pour la feuille de temps"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#, python-format
msgid "The Internal Project of a company should be in that company."
msgstr "Le projet interne de la société doit être dans cette société."

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
msgid ""
"The project will contain the timesheet generated when a time off is "
"validated."
msgstr ""
"Le projet contiendra des feuilles de temps générées lorsqu'un congé est "
"validé."

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
#, python-format
msgid "Time Off"
msgstr "Congés"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Time Off Task"
msgstr "Tâche pour les Congés"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Time Off Type"
msgstr "Type de congés"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheet"
msgstr "Feuille de présence"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid ""
"You cannot delete timesheet lines attached to a leaves. Please cancel the "
"leaves instead."
msgstr ""
"Vous ne pouvez pas supprimer une ligne de feuille de temps associée à un "
"congé. Annulez plutôt le congé."
