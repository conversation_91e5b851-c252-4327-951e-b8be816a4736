# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_picking_batch
# 
# Translators:
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_to_batch
msgid "Add pickings to a batch picking"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add pickings to batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action_stock_picking
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add to Batch"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_picking_internal_search_inherit_stock_picking_batch
msgid "Batch"
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:64
#: model:ir.model,name:stock_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_id_8938
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch_batch_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
#, python-format
msgid "Batch Picking"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_name
msgid "Batch Picking Name"
msgstr ""

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_batch_action
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_batch_menu
msgid "Batch Pickings"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Batch Pickings not finished"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch_id_8938
msgid "Batch associated to this picking"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Cancel"
msgstr "Odustani"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Cancel picking"
msgstr ""

#. module: stock_picking_batch
#: selection:stock.picking.batch,state:0
msgid "Cancelled"
msgstr "Poništeno"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Click to create a Batch Picking."
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Confirm"
msgstr "Potvrdi"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Confirm picking"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: selection:stock.picking.batch,state:0
msgid "Done"
msgstr "Završeno"

#. module: stock_picking_batch
#: selection:stock.picking.batch,state:0
msgid "Draft"
msgstr "Nacrt"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch_id
msgid "ID"
msgstr "ID"

#. module: stock_picking_batch
#: model:product.product,name:stock_picking_batch.product_product_ice_cream_choco
#: model:product.template,name:stock_picking_batch.product_product_ice_cream_choco_product_template
msgid "Ice Cream Chocolate"
msgstr ""

#. module: stock_picking_batch
#: model:product.product,description_sale:stock_picking_batch.product_product_ice_cream_choco
#: model:product.template,description_sale:stock_picking_batch.product_product_ice_cream_choco_product_template
msgid "Ice Cream Chocolate with sticks"
msgstr ""

#. module: stock_picking_batch
#: model:product.product,description_sale:stock_picking_batch.product_product_ice_cream_vani
#: model:product.product,name:stock_picking_batch.product_product_ice_cream_vani
#: model:product.template,description_sale:stock_picking_batch.product_product_ice_cream_vani_product_template
#: model:product.template,name:stock_picking_batch.product_product_ice_cream_vani_product_template
msgid "Ice Cream Vanilla"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_immediate_transfer
msgid "Immediate Transfer"
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:92
#, python-format
msgid "Immediate Transfer?"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "In Progress"
msgstr "U toku"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch___last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch_picking_ids
msgid "List of picking associated to this batch"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch_name
msgid "Name of the batch picking"
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:52
#, python-format
msgid "Nothing to print."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch_user_id
msgid "Person responsible for this batch picking"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_immediate_transfer_pick_to_backorder_ids
msgid "Pick To Backorder"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_immediate_transfer_pick_to_backorder_ids
msgid "Picking to backorder"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Pickings"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print"
msgstr "Štampaj"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_user_id
msgid "Responsible"
msgstr "Odgovoran"

#. module: stock_picking_batch
#: selection:stock.picking.batch,state:0
msgid "Running"
msgstr "Pokrenut"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Search Batch Picking"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Select a batch"
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:59
#, python-format
msgid ""
"Some pickings are still waiting for goods. Please check or force their "
"availability before setting this batch to done."
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:77
#, python-format
msgid ""
"Some products require lots/serial numbers, so you need to specify those "
"first!"
msgstr ""

#. module: stock_picking_batch
#: model:product.product,name:stock_picking_batch.product_product_dry_specu
#: model:product.template,name:stock_picking_batch.product_product_dry_specu_product_template
msgid "Speculoos"
msgstr ""

#. module: stock_picking_batch
#: model:product.product,description_sale:stock_picking_batch.product_product_dry_specu
#: model:product.template,description_sale:stock_picking_batch.product_product_dry_specu_product_template
msgid "Speculoos - A belgian speciality"
msgstr ""

#. module: stock_picking_batch
#: model:mail.message.subtype,description:stock_picking_batch.mt_batch_state
#: model:mail.message.subtype,name:stock_picking_batch.mt_batch_state
msgid "Stage Changed"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch_state
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "State"
msgstr "Status"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_tree
msgid "Stock Batch Picking"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"The goal of the batch picking is to group operations that may\n"
"            (needs to) be done together in order to increase their efficiency.\n"
"            It may also be useful to assign jobs (one person = one batch) or\n"
"            help the timing management of operations (tasks to be done at 1pm)."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:63
#, python-format
msgid "Transferred by"
msgstr ""

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "User"
msgstr "Korisnik"
