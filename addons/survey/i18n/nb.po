# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON><PERSON>, 2019
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-05 12:34+0000\n"
"PO-Revision-Date: 2019-08-26 09:14+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: <PERSON>l (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopi)"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s challenge certificate"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_header
msgid "00:00"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "100$"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "20$"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "200$"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "300$"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.403
msgid "403: Forbidden"
msgstr "403: Forbudt"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "50$"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "80$"
msgstr ""

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"                    <table style=\"width:600px;margin:5px auto;\">\n"
"                        <tbody>\n"
"                            <tr><td>\n"
"                                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                                <a href=\"/\"><img src=\"/logo.png?company=${object.survey_id.create_uid.company_id.id}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"                            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                                    Certification: ${object.survey_id.display_name}\n"
"                            </td></tr>\n"
"                        </tbody>\n"
"                    </table>\n"
"                    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"                        <tbody>\n"
"                            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                                <p>Dear <span>${object.partner_id.name or 'participant'}</span></p>\n"
"                                <p>\n"
"                                    Here is, in attachment, your certification document for\n"
"                                        <strong>${object.survey_id.display_name}</strong>\n"
"                                </p>\n"
"                                <p>Congratulations for succeeding the test!</p>\n"
"                            </td></tr>\n"
"                        </tbody>\n"
"                    </table>\n"
"                </div>\n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<b>Question </b>"
msgstr "<b>Spørsmål</b>"

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name or 'participant'}<br/><br/>\n"
"        % if object.survey_id.certificate:\n"
"            You have been invited to take a new certification.\n"
"        % else:\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        % endif\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a href=\"${('%s?answer_token=%s' % (object.survey_id.public_url, object.token)) | safe}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                % if object.survey_id.certificate:\n"
"                    Start Certification\n"
"                % else:\n"
"                    Start Survey\n"
"                % endif\n"
"            </a>\n"
"        </div>\n"
"        % if object.deadline:\n"
"            Please answer the survey for ${format_date(object.deadline)}.<br/><br/>\n"
"        % endif\n"
"        Thank you for your participation.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple "
"lines\" title=\"Multiple Lines\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid ""
"<i class=\"fa fa-bar-chart\"/>\n"
"                    Graph"
msgstr ""
"<i class=\"fa fa-bar-chart\"/>\n"
"                    Graf"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-bar-chart-o\"/> Graph"
msgstr "<i class=\"fa fa-bar-chart-o\"/> Graf"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-bar-chart-o\"/> Pie Chart"
msgstr "<i class=\"fa fa-bar-chart-o\"/> Kakediagram"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg\"/> svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg\"/> answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg\"/> svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg\"/> svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                                Download certification"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    All Data"
msgstr ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    Alle data"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    Data"
msgstr ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    Data"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-list-alt\"/> Data"
msgstr "<i class=\"fa fa-list-alt\"/> Data"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid ""
"<i class=\"fa fa-list-ol\"/>\n"
"                    Most Common"
msgstr ""
"<i class=\"fa fa-list-ol\"/>\n"
"                    Vanligste"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" "
"title=\"Single Line\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-square-o fa-lg\"/> svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<i class=\"fa fa-times\"/> Clear All Filters"
msgstr "<i class=\"fa fa-times\"/> Tøm alle filtre"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid ""
"<span attrs=\"{'invisible': [('is_attempts_limited', '=', False)]}\"> "
"attempts</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"minutes</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"badge badge-primary only_left_radius filter-all\">All "
"surveys</span><span class=\"badge badge-secondary only_right_radius filter-"
"finished\">Finished surveys</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"badge badge-primary only_left_radius\"><i class=\"fa fa-"
"filter\" role=\"img\" aria-label=\"Filter\" title=\"Filter\"/></span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"badge badge-secondary only_left_radius filter-all\">All "
"surveys</span><span class=\"badge badge-primary only_right_radius filter-"
"finished\">Finished surveys</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"badge badge-secondary only_left_radius\">Average </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"badge badge-secondary only_left_radius\">Maximum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"badge badge-secondary only_left_radius\">Minimum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"badge badge-secondary only_left_radius\">Sum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<span class=\"fa fa-filter\"/>  Filters"
msgstr "<span class=\"fa fa-filter\"/>  Filtre"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Answers</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Success</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<span>Correct answer</span>:"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<span>Correct answers</span>:"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.retake_survey_button
msgid "<span>Number of attemps left</span>:"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_init
msgid "<span>Time limit for this survey: </span>"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "A label must be attached to only one question."
msgstr ""

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "Lengden må være positiv!"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_label__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Positive score indikerer et riktig svar, negativ score eller null indikerer "
"et galt svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "A problem has occured"
msgstr "Et problem oppsto"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "A token must be unique!"
msgstr "Token må være unik!"

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p2
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Tilgangstoken"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevd"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Aktiv"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorering for Aktivitetsunntak"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
msgid "Activity State"
msgstr "Aktivitetsstatus"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Add a list of email of recipients (will not be converted into contacts). "
"Separated by commas, semicolons or newline..."
msgstr ""
"Legg til en liste med e-postadresser til mottakere (som ikke blir lagret som"
" kontakter). Skill dem med komma, semikolon eller linjeskift..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add a new survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a question"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a section"
msgstr "Legg til seksjon"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Legg til eksisterende kontakter..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr ""

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Administrator"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "Enig"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Allow Comments"
msgstr "Tillatt kommentarer"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Answer Choices"
msgstr "Svaralternativer"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Answer Score"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__input_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Svartype"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Answered"
msgstr "Besvart"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Answers"
msgstr "Svar"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__category
msgid "Appears in"
msgstr "Opptrer i"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Arkivert"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Vedlegg"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempt_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
msgid "Attempts"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Attempts Limit"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Forfatter"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__author_id
msgid "Author of the message."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when he succeeds the certification, "
"containing his certification document."
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_ir_autovacuum
msgid "Automatic Vacuum"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score %"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Back Button"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Medalje"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge Description"
msgstr "Medaljebeskrivelse"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge Name"
msgstr "Medaljenavn"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Cancel"
msgstr "Avbryt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Candidates"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__category
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Category"
msgstr "Kategori"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__category
msgid ""
"Category is used to know in which context the survey is used. Various apps "
"may define their own categories when they use survey like jobs recruitment "
"or employee appraisal surveys."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certificate
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Certificate"
msgstr "Sertifikat"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Certification"
msgstr "Sertifisering"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr ""

#. module: survey
#: model:mail.template,report_name:survey.mail_template_certification
msgid "Certification Document"
msgstr ""

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_give_badge_check
msgid "Certification badge must be configured if Give Badge is set."
msgstr ""

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: ${object.survey_id.display_name}"
msgstr ""

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__category__certification
msgid "Certifications"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Certified"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view
msgid "Certifies that"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Chart"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Valg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.simple_choice
msgid "Choose..."
msgstr "Velg..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.back
#: model_terms:ir.ui.view,arch_db:survey.page
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close"
msgstr "Lukk"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__state__closed
msgid "Closed"
msgstr "Avsluttet"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
#: model:survey.label,value:survey.vendor_certification_page_1_question_3_choice_1
msgid "Color"
msgstr "Farge"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Fargeindeks"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Columns of the Matrix"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_comments
msgid "Comment"
msgstr "Kommentar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment Field is an Answer Choice"
msgstr "Kommentarfeltet er et svaralternativ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Kommentarmelding"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Fullført"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Skriv e-postmelding"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "Congratulations, you have passed the test!"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Begrensninger"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Innhold"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
msgid "Correct"
msgstr "Riktig"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_label__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_label__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating test token is not allowed for you."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating token for archived surveys is not allowed."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating token for closed surveys is not allowed."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Dato"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Svarfrist"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view
msgid "Date of Certification:"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view
msgid "Date of Failure:"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Frist"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__category
msgid "Define the visibility of the challenge through menus"
msgstr "Definer hvor synlig utfordringen skal være via menyer"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Delete"
msgstr "Slett"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Description"
msgstr "Beskrivelse"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Design easily your survey, send invitations and analyze answers."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input_line
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "Uenig"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__display_mode
msgid "Display Mode"
msgstr "Visningsmodus"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_label__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Display mode"
msgstr "Visningsmodus"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__display_mode
msgid "Display mode of simple choice questions."
msgstr ""

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_1_question_1
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p2_q3
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns ?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_1_question_5
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__state__draft
msgid "Draft"
msgstr "Utkast"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Dropdown menu"
msgstr "Nedtrekksmeny"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
msgid "E-mail"
msgstr "E-post"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Edit Survey"
msgstr "Rediger undersøkelse"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.back
msgid "Edit Survey."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void
msgid "Edit in backend"
msgstr "Rediger i backend"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "E-post"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Email Template"
msgstr "E-postmal"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__email_from
msgid "Email address of the sender."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Feilmelding"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Except Test Entries"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Fanta"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Filter question"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_channel_ids
msgid "Followers (Channels)"
msgstr "Følgere (kanaler)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Format"
msgstr "Format"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__free_text
msgid "Free Text"
msgstr "Fritekst"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_free_text
msgid "Free Text answer"
msgstr "Fritekstsvar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__email_from
msgid "From"
msgstr "Fra"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Spillifiseringsmedalje"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Spillifiseringsutfordring"

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p1
#: model:survey.question,title:survey.survey_feedback_p1
msgid "General information"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__category__default
msgid "Generic Survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Grupper etter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Høyde"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p1_q3
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online ?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p1_q4
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website ?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_1_question_4
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_2_question_1
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "Jeg har lagt til produkter i min ønskeliste"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_label__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for å indikere aktivitetsunntak."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__token
msgid "Identification token"
msgstr "Identifikasjonstoken"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread
msgid "If checked, new messages require your attention."
msgstr "Hvis huket av, vil nye meldinger kreve din oppmerksomhet."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis huket av, har enkelte meldinger leveringsfeil."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Hvis krysset av, kan brukere gå tilbake til tidligere sider."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,comments_message:survey.survey_feedback_p1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q2
#: model:survey.question,comments_message:survey.survey_feedback_p1_q3
#: model:survey.question,comments_message:survey.survey_feedback_p1_q4
#: model:survey.question,comments_message:survey.survey_feedback_p2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q1
#: model:survey.question,comments_message:survey.survey_feedback_p2_q2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q3
#: model:survey.question,comments_message:survey.vendor_certification_page_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_3
#, python-format
msgid "If other, please specify:"
msgstr "Hvis annet, vennligst spesifiser:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "If you wish, you can"
msgstr "Om du vil, kan du"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__state__open
msgid "In Progress"
msgstr "Under arbeid"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "Feltet må fylles ut med en e-postadresse"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Invalid performance computation"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__input_type__link
msgid "Invitation"
msgstr "Invitasjon"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Invite"
msgstr "Inviter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Bare inviterte folk"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__is_correct
msgid "Is a correct answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_time_limit_reached
msgid "Is time limit reached?"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__sequence
msgid "Label Sequence order"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__labels_ids_2
msgid "Labels used for proposed choices: rows of matrix"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__labels_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite____last_update
#: model:ir.model.fields,field_description:survey.field_survey_label____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question____last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_label__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_label__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Forsinkede aktiviteter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Layout"
msgstr "Layout"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Login Required"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "Login required"
msgstr "Innlogging påkrevd"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hovedvedlegg"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Påkrevd svar"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__input_type__manually
msgid "Manual"
msgstr "Manuell"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Matrise"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Matrisetype"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Matrix:"
msgstr "Matrise:"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "Maksimumsdato kan ikke være mindre enn minimumsdato!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr ""

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "Maksimumslengde kan ikke være kortere enn minimumslengde!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "Maksimumsverdi kan ikke være lavere enn minimumsverdi!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Maksimumsdato"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Maksimal tekstlengde"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "Maksimumsverdi"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.403
msgid "Maybe you were looking for"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
msgid "Message Delivery error"
msgstr "Melding for feil ved levering"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Minimumsdato"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Minimum tekstlengde"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Minimumsverdi"

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "Missed"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__free_text
msgid "Multiple Lines Text Box"
msgstr "Tekstboks med flere linjer"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Flervalg: Flere valg tillatt"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Flervalg: Bare ett valg tillatt"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Flere valg per rad"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Ny"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Neste aktivitetsfrist"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
msgid "Next Activity Summary"
msgstr "Oppsummering av neste aktivitet"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
msgid "Next Activity Type"
msgstr "Neste aktivitetstype"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Next page"
msgstr "Neste side"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Next question"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "Nei"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "No attempts left."
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No questions found"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_label_form
msgid "No survey labels found"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input_line
msgid "No user input lines found"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "Nobody has replied to your surveys yet"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
msgid "Not started yet"
msgstr "Ikke startet enda"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__number
msgid "Number"
msgstr "Nummer"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__column_nb
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Number of columns"
msgstr "Antall kolonner"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antall meldinger som krever handling"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread_counter
msgid "Number of unread messages"
msgstr "Antall uleste meldinger"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Numerisk verdi"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_number
msgid "Numerical answer"
msgstr "Numerisk svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "Occurence"
msgstr "Forekomst"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "En gang i måneden"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "Ett valg per rad"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Options"
msgstr "Valg"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug9
msgid "Other"
msgstr "Annet"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p1_q3_sug5
msgid "Other (answer in comment)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Utgående e-postserver"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Overall Performance %.2f%s"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Side"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Sider"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partially Completed"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__skip
msgid "Partially completed"
msgstr "Delvis utfylt"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to ${object.survey_id.title} survey"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model:ir.ui.menu,name:survey.survey_menu_user_inputs
msgid "Participations"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Partner"

#. module: survey
#: code:addons/survey/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
#, python-format
msgid "Passed"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__passing_score
msgid "Passing score (%)"
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Please enter at least one valid recipient."
msgstr "Legg til minst én gyldig mottaker."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_ids
msgid "Predefined Questions"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Previous page"
msgstr "Forrige side"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Previous question"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_2
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Skriv ut"

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_1
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Produkter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_url
#: model:ir.model.fields,field_description:survey.field_survey_survey__public_url
msgid "Public link"
msgstr "Offentlig link"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__question_id
#: model:ir.model.fields,field_description:survey.field_survey_question__question
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_label_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Question"
msgstr "Spørsmål"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__question_id_2
msgid "Question 2"
msgstr "Spørsmål 2"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
msgid "Question Type"
msgstr "Spørsmålstype"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model:ir.ui.menu,name:survey.survey_menu_questions
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Questions"
msgstr "Spørsmål"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__quizz_passed
msgid "Quizz Passed"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__display_mode__columns
msgid "Radio Buttons"
msgstr "Radioknapper"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "Random questions count"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per section"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Mottakere"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Registered"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruker"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Results Overview"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.retake_survey_button
msgid "Retry"
msgstr "Prøv igjen"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_suggested_row
msgid "Row answer"
msgstr "Radsvar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Rad1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Rad2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Rad3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Rader"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__labels_ids_2
msgid "Rows of the Matrix"
msgstr "Matriserader"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Leveringsfeil"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
msgid "Score"
msgstr "Score"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__quizz_score
msgid "Score (%)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__answer_score
msgid "Score for this choice"
msgstr "Score for dette valget"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Scoring"
msgstr "Scoring"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
msgid "Scoring Type"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers at the end"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_label_search
msgid "Search Label"
msgstr "Søk i feltnavn"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Søk etter spørsmål"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey"
msgstr "Søk etter undersøkelse"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
msgid "Search User input lines"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Seksjon"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "See results"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_1_question_3
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_1_question_2
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_2_question_2
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the the products that sell for 100$ or more"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Selection"
msgstr "Valg"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__display_mode__dropdown
msgid "Selection Box"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Send"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Set to draft"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Share"
msgstr "Del"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Vis kommentarfelt"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr "Vis alle poster som har neste handlingsdato før dagen i dag"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__textbox
msgid "Single Line Text Box"
msgstr "Tekstboks med én linje"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Skipped"
msgstr "Hoppet over"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Some emails you just entered are incorrect: %s"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid ""
"Something went wrong while contacting survey server. <strong class=\"text-"
"danger\">Your answers have probably not been recorded.</strong> Try "
"refreshing."
msgstr ""
"Noe gikk galt under kontakt med serveren. <strong class=\"text-"
"danger\">Svarene dine ble sannsynligvis ikke lagret.</strong> Prøv å laste "
"inn på nytt."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Sorry, No one answered this question."
msgstr "Beklager, ingen har svart på dette spørsmålet."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Sorry, no one answered this survey yet."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_init
msgid "Start Certification"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_init
msgid "Start Survey"
msgstr "Start undersøkelse"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "State"
msgstr "Stat"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Status"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basert på aktiviteter\n"
"Utgått: Fristen er allerede passert\n"
"I dag: Aktiviteten skal gjøres i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Emne"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Emne..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Submit Survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Suksess"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Success rate:"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view
msgid "Successfully achieved"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view
msgid "Successfully failed"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_label_form
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_suggested
msgid "Suggested answer"
msgstr "Foreslått svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__value
msgid "Suggested value"
msgstr "Foreslått verdi"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Forslag"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Undersøkelse"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_tree
msgid "Survey Answer Line"
msgstr "Linje for svar på undersøkelsen"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_label
#: model_terms:ir.ui.view,arch_db:survey.survey_label_tree
msgid "Survey Label"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Spørsmål i undersøkelse"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__state
msgid "Survey Stage"
msgstr "Stadium i undersøkelse"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "Tittel på undersøkelse"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Survey User Input"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid "Survey filter"
msgstr ""

#. module: survey
#: model:ir.actions.server,name:survey.survey_action_server_clean_test_answers
msgid "Survey: Clean test answers"
msgstr "Undersøkelse: Slett testsvar"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Undersøkelser"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Test"
msgstr "Test"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Test Entries"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
msgid "Test Entry"
msgstr "Testoppføring"

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_2
msgid "Test your knowledge of our prices."
msgstr ""

#. module: survey
#: model_terms:survey.question,description:survey.vendor_certification_page_1
msgid "Test your knowledge of your products!"
msgstr ""

#. module: survey
#: model_terms:survey.survey,description:survey.vendor_certification
msgid "Test your vendor skills!"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text
msgid "Text"
msgstr "Tekst"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text
msgid "Text answer"
msgstr "Tekstsvar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "Thank you!"
msgstr "Takk!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__thank_you_message
msgid "Thanks Message"
msgstr "Takkemelding"

#. module: survey
#: code:addons/survey/models/survey_user.py:0
#, python-format
msgid "The answer must be in the right type"
msgstr "Svaret må ha riktig type"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_3
#, python-format
msgid "The answer you entered is not valid."
msgstr ""

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr ""

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey.js:0
#, python-format
msgid "The date you selected is greater than the maximum date: "
msgstr ""

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey.js:0
#, python-format
msgid "The date you selected is lower than the minimum date: "
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following customers have already received an invite"
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following emails have already received an invite"
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.403
msgid "The page you were looking for could not be authorized."
msgstr "Siden du ser etter, kunne ikke autoriseres."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr ""

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr ""

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The user has not succeeded the certification"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__column_nb
msgid ""
"These options refer to col-xx-[12|6|4|3|2] classes in Bootstrap for "
"dropdown-based simple and multiple choice questions."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This answer must be an email address"
msgstr "Svaret må være en e-postadresse"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.back
msgid "This is a test survey."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a date"
msgstr "Dette er ikke en dato"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a number"
msgstr "Dette er ikke et tall"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__emails
msgid ""
"This list of emails of recipients will not be converted in contacts.        "
"Emails must be separated by commas, semicolons or newline."
msgstr ""
"Listen over e-postmottakere vil ikke bli lagret som kontakter. Skill "
"e-postadressene med komma, semikolon eller linjeskift."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__thank_you_message
msgid "This message will be displayed when survey is completed"
msgstr "Denne meldingen vises når undersøkelsen er utfylt"

#. module: survey
#: code:addons/survey/models/survey_user.py:0
#, python-format
msgid "This question cannot be unanswered or skipped."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_3
#, python-format
msgid "This question requires an answer."
msgstr "Dette spørsmålet må besvares."

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p1
msgid ""
"This section is about general informations about you. Answering them helps "
"qualifying your answers."
msgstr ""

#. module: survey
#: model_terms:survey.question,description:survey.survey_feedback_p2
msgid "This section is about our eCommerce experience itself."
msgstr ""

#. module: survey
#: model_terms:survey.survey,description:survey.survey_feedback
msgid ""
"This survey allows you to give a feedback about your experience with our eCommerce solution.\n"
"    Filling it helps us improving your experience."
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_expired
msgid "This survey is now closed. Thank you for your interest !"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "This survey is open only to registered people. Please"
msgstr ""
"Denne undersøkelsen er bare åpen for registrerte deltakere. Vennligst "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Time Limit"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Tittel"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Dagens aktiviteter"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "Helt enig"

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Helt uenig"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Type"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Type of answers"
msgstr "Type svar"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type unntaks-aktivitet på posten."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__labels_ids
msgid "Types of answers"
msgstr "Svartyper"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "Unfortunately, you have failed the test."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread
msgid "Unread Messages"
msgstr "Uleste meldinger"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Teller for uleste meldinger"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Kommende aktiviteter"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Use template"
msgstr "Bruk mal"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid "Use this field to add additional explanations about your question"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr ""

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Bruker"

#. module: survey
#: model:survey.survey,title:survey.survey_feedback
msgid "User Feedback Form"
msgstr "Skjema for kundetilbakemeldinger"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
msgid "User Input"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
#: model_terms:ir.ui.view,arch_db:survey.result_number
#: model_terms:ir.ui.view,arch_db:survey.result_text
msgid "User Responses"
msgstr "Brukersvar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_form
msgid "User input line details"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Brukersvar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Brukere kan gå tilbake"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Valider svar"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error message"
msgstr "Valideringsfeilmelding"

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Vote"
msgstr "Stem"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
msgid "Website Messages"
msgstr "Meldinger fra nettsted"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
msgid "Website communication history"
msgstr "Historikk for kommunikasjon på nettsted"

#. module: survey
#: model:survey.question,question:survey.vendor_certification_page_2_question_3
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p2_q2
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do your think about our new eCommerce ?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p1_q2
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth ?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p1_q1
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live ?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.survey_feedback_p2_q1
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products ?"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Bredde"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "a"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"You can create surveys for different purposes: customer opinion, services "
"feedback, recruitment interviews, employee's periodical evaluations, "
"marketing campaigns, etc."
msgstr ""
"Du kan lage undersøkelser for ulike formål: Kundesynspunkter, "
"tilbakemeldinger på tjenester, rekrutteringsintervjuer, "
"medarbeiderundersøkelser, markedskampanjer, osv."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certificate_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr ""
"Du kan ikke sende invitasjon til en undersøkelse som ikke har noen spørsmål."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr "Du kan ikke sende invitasjoner til avsluttede undersøkelser."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "You received the badge"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "You scored"
msgstr "Du scoret"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "svar"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "e.g. Satisfaction Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "log in"
msgstr "logg inn"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "review your answers"
msgstr "se gjennom svarene dine"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_expired
msgid "survey expired"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void
msgid "survey is void"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.403
msgid "this page"
msgstr ""
