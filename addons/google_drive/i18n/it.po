# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_drive
# 
# Translators:
# <PERSON> <gio<PERSON><PERSON>@perteghella.org>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON>, 2019
# <PERSON>, 2020
# <PERSON> <<EMAIL>>, 2020
# Friederi<PERSON> Fasterling-Nesselbosch, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-07 07:12+0000\n"
"PO-Revision-Date: 2019-08-26 09:10+0000\n"
"Last-Translator: Friederike Fasterling-Nesselbosch, 2021\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"<b>To create a new filter:</b><br/>\n"
"                                - Go to the Odoo document you want to filter. For instance, go to Opportunities and search on Sales Department.<br/>\n"
"                                - In this \"Search\" view, select the option \"Save Current Filter\", enter the name (Ex: Sales Department)<br/>\n"
"                                - If you select \"Share with all users\", link of google document in \"More\" options will appear for all users in opportunities of Sales Department.<br/>\n"
"                                - If you don't select \"Share with all users\", link of google document in \"More\" options will not appear for other users in opportunities of Sales Department.<br/>\n"
"                                - If filter is not specified, link of google document will appear in \"More\" option for all users for all opportunities."
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Reset token"
msgstr "<i class=\"fa fa-arrow-right\"/> Ripristina token"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Set up token"
msgstr "<i class=\"fa fa-arrow-right\"/> Imposta token"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-exclamation-triangle text-warning\"/> &amp;nbsp; No refresh"
" token set"
msgstr ""
"<i class=\"fa fa-exclamation-triangle text-warning\"/> &amp;nbsp; Token di "
"aggiornamento non impostato"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "<i class=\"text-success fa fa-check\"/> &amp;nbsp; Refresh token set"
msgstr ""
"<i class=\"text-success fa fa-check\"/> &amp;nbsp; Token di aggiornamento "
"impostato"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "<span>Get an authorization code and set it in the field below.</span>"
msgstr ""
"<span>Ottieni un codice di autorizzazione da inserire nel campo "
"sottostante.</span>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Active</strong>"
msgstr "<strong>Attivo</strong>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Model</strong>"
msgstr "<strong>Modello</strong>"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Template</strong>"
msgstr "<strong>Modello</strong>"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__active
msgid "Active"
msgstr "Attivo"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid "Add a new template"
msgstr "Aggiungi un nuovo modello"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_config_view_search
msgid "Archived"
msgstr "In archivio"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "At least one key cannot be found in your Google Drive name pattern."
msgstr ""
"Almeno una chiave non può essere trovata nello schema nomi di Google Drive."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__google_drive_authorization_code
msgid "Authorization Code"
msgstr "Codice di autorizzazione"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Cancel"
msgstr "Annulla"

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_google_drive_config__name_template
msgid ""
"Choose how the new google drive will be named, on google side. Eg. "
"gdoc_%(field_name)s"
msgstr ""
"Scegliere come chiamare il nuovo drive su Google, es. gdoc_%(field_name)s"

#. module: google_drive
#: model:ir.model,name:google_drive.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_auth_code_wizard
msgid "Confirm"
msgstr "Conferma"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__create_date
msgid "Created on"
msgstr "Creato il"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Creating google drive may only be done by one at a time."
msgstr ""
"La creazione di un google drive può essere fatta uno documento alla volta."

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:161
#, python-format
msgid ""
"The document filter must not include any 'dynamic' part, so it should not be"
" based on the current time or current user, for example."
msgstr ""
"Il filtro del documento non deve includere alcuna parte \"dinamica\", quindi"
" non dovrebbe essere basato sull'ora corrente o sull'utente corrente, per "
"esempio."

#. module: google_drive
#: model:ir.filters,name:google_drive.filter_partner
msgid "Customer"
msgstr "Cliente"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__filter_id
msgid "Filter"
msgstr "Filtro"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Vai al pannello di configurazione"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_client_id
msgid "Google Client"
msgstr "Client Google"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_tree
msgid "Google Drive Configuration"
msgstr "Configurazione Google Drive"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__name_template
msgid "Google Drive Name Pattern"
msgstr "Schema nome per Google Drive"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "Google Drive Templates"
msgstr "Modelli Google Drive"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Google Drive is not yet configured. Please contact your administrator."
msgstr ""
"Google Drive non è ancora stato configurato. Contattare l'amministratore."

#. module: google_drive
#: model:ir.model,name:google_drive.model_google_drive_config
msgid "Google Drive templates config"
msgstr "Configurazione modelli Google Drive"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__id
msgid "ID"
msgstr "ID"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid ""
"Link your own google drive templates to any record of Odoo. If you have "
"really specific documents you want your collaborator fill in, e.g. Use a "
"spreadsheet to control the quality of your product or review the delivery "
"checklist for each order in a foreign country, ... Its very easy to manage "
"them, link them to Odoo and use them to collaborate with your employees."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__model_id
msgid "Model"
msgstr "Modello"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "Please enter a valid Google Document URL."
msgstr "Inserire l'URL di un documento Google valido."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__is_google_drive_token_generated
msgid "Refresh Token Generated"
msgstr "Token di aggiornamento generato"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__model
msgid "Related Model"
msgstr "Modello collegato"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_resource_id
msgid "Resource Id"
msgstr "ID risorsa"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.google_drive_config_view_search
msgid "Search Google Drive Config"
msgstr "Ricerca configurazione Google Drive"

#. module: google_drive
#: code:addons/google_drive/models/res_config_settings.py:0
#, python-format
msgid "Set up refresh token"
msgstr "Imposta token di aggiornamento"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"Something went wrong during the token generation. Please request again an "
"authorization code ."
msgstr ""
"La generazione del token non è andata a buon fine. Richiedere un nuovo "
"codice di autorizzazione."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__name
msgid "Template Name"
msgstr "Nome modello"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config__google_drive_template_url
msgid "Template URL"
msgstr "URL del modello"

#. module: google_drive
#: model:ir.actions.act_window,name:google_drive.action_google_drive_users_config
msgid "Templates"
msgstr "Modelli"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid "The Google Template cannot be found. Maybe it has been deleted."
msgstr "Impossibile trovare il modello Google, forse è stato eliminato."

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_res_config_settings__google_drive_uri
msgid "The URL to generate the authorization code from Google"
msgstr "L'URL per generare il codice di autorizzazione di Google"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"The name of the attached document can use fixed or variable data. To distinguish between documents in\n"
"                                Google Drive, use fixed words and fields. For instance, in the example above, if you wrote Deco_Addict_%(name)s_Sales\n"
"                                in the Google Drive name field, the document in your Google Drive and in Odoo attachment will be named\n"
"                                'Deco_Addict_SO0001_Sales'."
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"The permission 'reader' for 'anyone with the link' has not been written on "
"the document"
msgstr ""
"Il permesso di lettura per \"chiunque possieda il link\" non è stato "
"applicato al documento"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:0
#, python-format
msgid ""
"There is no refresh code set for Google Drive. You can set it up from the "
"configuration panel."
msgstr ""
"Non è presente alcun codice di aggiornamento per Google Drive. È possibile "
"impostarlo dal pannello di configurazione."

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings__google_drive_uri
msgid "URI"
msgstr "URI"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"https://docs.google.com/document/d/1vOtpJK9scIQz6taD9tJRIETWbEw3fSiaQHArsJYcua4/edit"
msgstr ""
"https://docs.google.com/document/d/1vOtpJK9scIQz6taD9tJRIETWbEw3fSiaQHArsJYcua4/edit"
