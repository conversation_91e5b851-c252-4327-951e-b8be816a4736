# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_adyen
# 
# Translators:
# <PERSON>, 2019
# <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-04-27 09:21+0000\n"
"PO-Revision-Date: 2019-09-09 12:33+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Dutch (https://www.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.pos_config_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_api_key
msgid "Adyen API key"
msgstr "Adyen API sleutel"

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Adyen Error"
msgstr "Adyen fout"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_diagnosis
msgid "Adyen Latest Diagnosis"
msgstr "Adyen laatste diagnose"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid "Adyen Latest Response"
msgstr "Adyen laatste reactie"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "Adyen Terminal Identifier"
msgstr "Adyen terminal ID"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Adyen Test Mode"
msgstr "Adyen test modus"

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "An unexpected error occured. Message from Adyen: %s"
msgstr "Er is een onverwachte fout opgetreden. Bericht van Adyen: %s"

#. module: pos_adyen
#: model:ir.model.fields,field_description:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Ask Customers For Tip"
msgstr "Vraag klanten voor fooi"

#. module: pos_adyen
#: model_terms:ir.ui.view,arch_db:pos_adyen.pos_config_view_form
msgid "Ask customers to tip before paying."
msgstr "Vraag klanten voor fooi voor dat ze betalen."

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Authentication failed. Please check your Adyen credentials."
msgstr "Authenticatie mislukt. Controleer uw Adyen logingegevens."

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid "Message from Adyen: %s"
msgstr "Bericht van Adyen: %s"

#. module: pos_adyen
#: code:addons/pos_adyen/models/pos_config.py:0
#, python-format
msgid ""
"Please configure a tip product for POS %s to support tipping with Adyen."
msgstr ""
"Om fooien met Adyen te ondersteunen, stel een fooiproduct in voor kassa %s "

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Kassa instellingen"

#. module: pos_adyen
#: model:ir.model,name:pos_adyen.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Kassa betaalmethodes"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_config__adyen_ask_customer_for_tip
msgid "Prompt the customer to tip."
msgstr "Vraag de klanten om een fooi te geven."

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_test_mode
msgid "Run transactions in the test environment."
msgstr "Transacties uitvoeren in de testomgeving."

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_latest_response
msgid ""
"Technical field used to buffer the latest asynchronous notification from "
"Adyen."
msgstr ""
"Technisch veld gebruikt om de laatste asynchrone melding van Adyen te "
"bufferen."

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_latest_diagnosis
msgid "Technical field used to determine if the terminal is still connected."
msgstr ""
"Technisch veld gebruikt om te controleren of de terminal nog steeds "
"verbonden is."

#. module: pos_adyen
#: code:addons/pos_adyen/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used on payment method %s."
msgstr "Terminal %s wordt al gebruikt voor de betaalmethode %s."

#. module: pos_adyen
#. openerp-web
#: code:addons/pos_adyen/static/src/js/payment_adyen.js:0
#, python-format
msgid ""
"The connection to your payment terminal failed. Please check if it is still "
"connected to the internet."
msgstr ""
"De connectie met uw betaalterminal is mislukt. Controleren of deze nog "
"steeds verbonden is met het internet."

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_api_key
msgid ""
"Used when connecting to Adyen: https://docs.adyen.com/user-management/how-"
"to-get-the-api-key/#description"
msgstr ""
"Gebruikt bij het verbinden met Adyen: https://docs.adyen.com/user-"
"management/how-to-get-the-api-key/#description"

#. module: pos_adyen
#: model:ir.model.fields,help:pos_adyen.field_pos_payment_method__adyen_terminal_identifier
msgid "[Terminal model]-[Serial number], for example: P400Plus-123456789"
msgstr "[Terminal model]-[Serienummer], bijvoorbeeld: P400Plus-123456789"
