# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED

from odoo.exceptions import UserError

from odoo import models, _


class NuroBloodTransfusionCreditPayment(models.TransientModel):
    _inherit = 'nuro.blood.transfusion.credit.payment'

    def create_credit_payment(self):
        """
        Blood Transfusion Credit Payment Method
        :return:
        """
        if not self.blood_transfusion_id.blood_donor_id:
            raise UserError(_('Please Add Donor name Before Processing It.!!!'))
        current_user = self.env.user
        for rec in self:
            if rec.extend_credit:
                rec.extend_allow_credit_limit()
            if rec.amount <= 0.0:
                raise UserError(_('No Amount for Cash Payment!'))
            if rec.blood_transfusion_id.state not in ('draft', 'request'):
                raise UserError(_('Payment already has been made!!'))
            if not current_user.account_id:
                raise UserError(_('Please define Account for Selected User!'))
            if rec.blood_transfusion_id and rec.bill_to_type in ('patient', 'employee', 'sponsor'):
                if not rec.bill_to_user_id.is_credit_allow:
                    raise UserError(_('Credit is not allowed for %s!!!') % self.bill_to_user_id.name)
                
                rec.bill_to_user_id._check_utilized_limit(credit_amount=rec.amount)
                inv_ids = rec.blood_transfusion_id.create_invoice(
                    amount=rec.amount,
                    discount_amount=rec.discount_amount,
                    partner_id=rec.bill_to_user_id,
                    invoice_type='out_invoice'
                )
                inv_ids.action_post()
            if rec.payment_type == 'expense':
                move_ids = rec.blood_transfusion_id.create_account_move(
                    journal_id=rec.payment_method_id,
                    amount=rec.amount,
                    partner_id=rec.bill_to_user_id,
                    employee_id=rec.employee_id or False
                )
                hs_line_receivable_id = move_ids.line_ids.filtered(
                    lambda x: x.account_internal_type == 'receivable')
                hs_line_receivable_id.write({
                    'partner_id': self.blood_transfusion_id.bill_to_user_id.id,
                    # todo need tofix it
                    # 'account_id': self.blood_transfusion_id.expense_record_id.account_id.id,
                })
                move_ids.post()
                move_ids.line_ids.write({
                    'panel': 'bt'
                })
            rec.blood_transfusion_id.write({
                'payment_method': 'credit',
                'state': 'send_to_lab',
                'discount_amount': self.discount_amount,
                'cashier_user': self.env.user.id,
                'credit_amount': self.amount
            })
            rec.blood_transfusion_id.create_labtest_entry()
            return rec.blood_transfusion_id.print_receipt()
