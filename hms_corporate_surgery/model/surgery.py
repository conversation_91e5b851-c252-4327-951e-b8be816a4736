from odoo import api, fields, models


class NuroSurgery(models.Model):
    _inherit = 'nuro.surgery'

    corporate_id = fields.Many2one('res.partner', string='Corporate Name', domain=[
        ('is_corporate', '=', True),
    ])
    corporate_identification_number = fields.Char('Corporate ID')
    id_number = fields.Char('ID Number')
    employee_name = fields.Char('Employee Name')


class NuroSurgeryEntry(models.Model):
    _inherit = 'nuro.surgery.entry'

    corporate_id = fields.Many2one('res.partner', string='Corporate Name', domain=[
        ('is_corporate', '=', True),
    ])
    corporate_identification_number = fields.Char('Corporate ID')
    id_number = fields.Char('ID Number')
    employee_name = fields.Char('Employee Name')
