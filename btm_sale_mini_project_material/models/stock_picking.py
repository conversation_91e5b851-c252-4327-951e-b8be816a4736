# -*- coding: utf-8 -*-
# Copyright  Nuro Solution Pvt Ltd.
from odoo import fields, models, _, api
from odoo.tools.float_utils import float_compare, float_is_zero, float_round
from odoo.exceptions import UserError
from collections import defaultdict


class Picking(models.Model):
    _inherit = 'stock.picking'

    top_req_id = fields.Many2one('top.material.request', ondelete='cascade')

    def button_validate(self):
        res = super(Picking, self).button_validate()
        if self.move_ids_without_package:
            for mv in self.move_ids_without_package:
                if mv.top_material_line_id:
                    mv.top_material_line_id.transfer_qty += mv.quantity_done if mv.quantity_done > 0.0 else mv.product_uom_qty
            return res
        if any(move.top_material_line_id for move in self.move_lines):
            for move in self.move_lines:
                if move.top_material_line_id:
                    move.top_material_line_id.transfer_qty += move.product_qty
        return res

    def _action_done(self, cancel_backorder=False):
        '''override for update top material line id in stock move when create backorder'''
        self.filtered(lambda move: move.state == 'draft')._action_confirm()  # MRP allows scrapping draft moves
        moves = self.exists().filtered(lambda x: x.state not in ('done', 'cancel'))
        moves_todo = self.env['stock.move']

        # Cancel moves where necessary ; we should do it before creating the extra moves because
        # this operation could trigger a merge of moves.
        for move in moves:
            if move.quantity_done <= 0:
                if float_compare(move.product_uom_qty, 0.0, precision_rounding=move.product_uom.rounding) == 0 or cancel_backorder:
                    move._action_cancel()

        # Create extra moves where necessary
        for move in moves:
            if move.state == 'cancel' or move.quantity_done <= 0:
                continue

            moves_todo |= move._create_extra_move()

        moves_todo._check_company()
        # Split moves where necessary and move quants
        for move in moves_todo:
            # To know whether we need to create a backorder or not, round to the general product's
            # decimal precision and not the product's UOM.
            rounding = self.env['decimal.precision'].precision_get('Product Unit of Measure')
            if float_compare(move.quantity_done, move.product_uom_qty, precision_digits=rounding) < 0:
                # Need to do some kind of conversion here
                qty_split = move.product_uom._compute_quantity(move.product_uom_qty - move.quantity_done, move.product_id.uom_id, rounding_method='HALF-UP')
                new_move = move._split(qty_split)
                move._unreserve_initial_demand(new_move)
                # ==================update line in in move=============
                id = self.env['stock.move'].browse(new_move)
                id.write({'top_material_line_id': move.top_material_line_id.id})
                # ==================================================================================
                if cancel_backorder:
                    self.env['stock.move'].browse(new_move).with_context(moves_todo=moves_todo)._action_cancel()
        moves_todo.mapped('move_line_ids').sorted()._action_done()
        # Check the consistency of the result packages; there should be an unique location across
        # the contained quants.
        for result_package in moves_todo\
                .mapped('move_line_ids.result_package_id')\
                .filtered(lambda p: p.quant_ids and len(p.quant_ids) > 1):
            if len(result_package.quant_ids.filtered(lambda q: not float_is_zero(abs(q.quantity) + abs(q.reserved_quantity), precision_rounding=q.product_uom_id.rounding)).mapped('location_id')) > 1:
                raise UserError(_('You cannot move the same package content more than once in the same transfer or split the same package into two location.'))
        picking = moves_todo.mapped('picking_id')
        moves_todo.write({'state': 'done', 'date': fields.Datetime.now()})

        move_dests_per_company = defaultdict(lambda: self.env['stock.move'])
        for move_dest in moves_todo.move_dest_ids:
            move_dests_per_company[move_dest.company_id.id] |= move_dest
        for company_id, move_dests in move_dests_per_company.items():
            move_dests.sudo().with_context(force_company=company_id)._action_assign()

        # We don't want to create back order for scrap moves
        # Replace by a kwarg in master
        if self.env.context.get('is_scrap'):
            return moves_todo

        if picking and not cancel_backorder:
            picking._create_backorder()
        return moves_todo



class Move(models.Model):
    _inherit = 'stock.move'

    top_material_line_id = fields.Many2one('top.material.line', ondelete='cascade')
    analytic_tag_ids = fields.Many2many('account.analytic.tag', string='Analytic Tags')

    def _prepare_account_move_line(self, qty, cost,
                                   credit_account_id, debit_account_id, description):
        self.ensure_one()
        res = super(Move, self)._prepare_account_move_line(
            qty, cost, credit_account_id, debit_account_id, description)
        # Add analytic account in debit line
        if not self.analytic_account_id or not res:
            return res

        for num in range(0, 2):
            if res[num][2]["account_id"] != self.product_id. \
                    categ_id.property_stock_valuation_account_id.id:
                res[num][2].update({
                    'analytic_account_id': self.analytic_account_id.id,
                })
                res[num][2].update({
                    'analytic_tag_ids': [(6, 0, self.analytic_tag_ids.ids)],
                })
        return res


class StockMoveLine(models.Model):
    _inherit = "stock.move.line"

    analytic_tag_ids = fields.Many2many(
        related='move_id.analytic_tag_ids')

