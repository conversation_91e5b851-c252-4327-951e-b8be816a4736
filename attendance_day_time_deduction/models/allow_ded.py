# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import fields,models,api,_
from odoo.exceptions import UserError
from datetime import datetime, timedelta
import calendar


class AllowDedParent(models.Model):
    _inherit = "allowance.deduction.parent"

    is_attendance_ded = fields.Boolean()

    @api.constrains('type_id')
    def check_attendance_deduction(self):
        if not self.is_attendance_ded:
            if self.type_id.is_attendance_ded:
                raise UserError(_("You can not select Attendance deduction"))

    def get_employee_attendance(self):
        employee_ids = self.env['nuro.employee.attendance'].search([
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to),
            ('name.active', '=', True)])
        if employee_ids:
            employee_ids = employee_ids.filtered(
                    lambda x: x.required_working_hrs != x.total_working_hours)
            if self.department_id:
                employee_ids = employee_ids.filtered(
                    lambda x: x.name.department_id.id == self.department_id.id)
            return employee_ids.mapped('name')

    def onchange_attendance_ded(self):
        line_list = []
        if self.is_attendance_ded and self.type_id and self.type_id.type == 'deduction':
            if self.allowance_deduction_line:
                self.allowance_deduction_line.unlink()
            employee_ids = self.get_employee_attendance()
            if employee_ids:
                for line in employee_ids:
                    vals = self.create_line_vals(line)
                    line_list.append(vals)
                if line_list:
                    allowance_deduction_ids = self.env['nuro.allowances.deduction'].create(line_list)
                    return allowance_deduction_ids

    @api.model
    def create(self, vals):
        res = super().create(vals)
        if res.is_attendance_ded:
            res.onchange_attendance_ded()
        return res

    def write(self, vals):
        """Write Method Super Call"""
        res = super().write(vals)
        if "department_id" in vals or "type_id" in vals or "month" in vals:
            if self.is_attendance_ded:
                self.onchange_attendance_ded()
        return res


class AllowDed(models.Model):
    _inherit = "nuro.allowances.deduction"

    is_absent_deduction = fields.Boolean()
    late_checkin_hrs = fields.Float('Late Checkin Hrs', compute='_compute_late_checkin', store=True)
    early_checkout_hrs = fields.Float('Early Checkout Hrs', compute='_compute_early_checkout',
                                      store=True)
    absent_hrs = fields.Float('Absent Hrs', compute='_compute_absent_hrs', store=True)
    rate_per_minutes = fields.Float(compute='compute_rate_per_minutes',
                                    string='Rate Per Minutes', digits=(12, 5))
    total_deduction_attendance = fields.Float('Total Deduction {Minutes}',
                                              compute='compute_total_ded_attendance', store=True)
    total_attendance_deduction_amount = fields.Float(compute='calculate_total_attendance_amount',
                                          string='Total Deduction Amount', store=True)
    shift_name = fields.Char('Shift', store=True,
                               compute='_compute_attendance_shift')

    @api.depends('total_deduction_attendance', 'rate_per_minutes',
                 'total_deduction_attendance')
    def calculate_total_attendance_amount(self):
        """
        calculate total deduction amount
        """
        for res in self:
            res.total_attendance_deduction_amount = 0.0
            if res.parent_id.is_attendance_ded:

                res.total_attendance_deduction_amount = res.rate_per_minutes * (res.total_deduction_attendance)
                if res.rate_per_minutes * (res.total_deduction_attendance) > 0.0:
                    res.amount = res.rate_per_minutes * (res.total_deduction_attendance)

    @api.depends('late_checkin_hrs', 'early_checkout_hrs', 'absent_hrs')
    def compute_total_ded_attendance(self):
        for rec in self:
            rec.total_deduction_attendance = 0.0
            # float_record_string = '{0:02.0f}:{1:02.0f}'.format(*divmod(rec.absent_hrs *
            #                                                     60,
            #                                                     60))
            # float_record = float((float_record_string.replace(':', '.')))
            rec.total_deduction_attendance = (rec.absent_hrs + rec.late_checkin_hrs + rec.early_checkout_hrs) * 60

    def compute_rate_per_minutes(self):
        """get rate from contract"""
        for rec in self:
            working_hrs = rec.employee_id.company_id.minimum_work_hour_perday
            shift_id = self.env['resource.calendar'].sudo().search([('code', '=', rec.shift_name)])
            if shift_id:
                working_hrs = shift_id.hours_per_day
            rec.rate_per_minutes = 0.0
            contract_id = self.env['hr.contract'].search([
                ('employee_id', '=', rec.employee_id.id),
                ('state', '=', 'open')])
            if contract_id.wage:
                today = fields.Date.context_today(self)
                first_date = datetime.strptime(str(today), '%Y-%m-%d')
                # to get number of day from month
                num_days = calendar.monthrange(first_date.year, first_date.month)
                rec.rate_per_minutes = (contract_id.wage / (num_days[1] * float(working_hrs) if float(working_hrs) > 0.0 else 1)) / 60

    @api.depends('employee_id')
    def _compute_attendance_shift(self):
        for rec in self:
            rec.shift_name = False
            attendance_rec = self.env['nuro.employee.attendance'].search([
                ('name', '=', rec.employee_id.id),
                ('date', '>=', rec.date_from),
                ('date', '<=', rec.date_to),
            ])
            if attendance_rec:
                for atten in attendance_rec.filtered(lambda line: line.shift_name):
                    rec.shift_name = atten.shift_name

    @api.depends('employee_id')
    def _compute_absent_hrs(self):
        for rec in self:
            rec.absent_hrs = 0.0
            attendance_rec = self.env['nuro.employee.attendance'].search([
                ('name', '=', rec.employee_id.id),
                ('date', '>=', rec.date_from),
                ('date', '<=', rec.date_to),
                ('final_status', '=', 'absent'),
            ])
            if attendance_rec:
                rec.absent_hrs = sum(attendance_rec.mapped('absent_hrs'))

    @api.depends('employee_id')
    def _compute_late_checkin(self):
        for rec in self:
            rec.late_checkin_hrs = 0.0
            attendance_rec = self.env['nuro.employee.attendance'].search([
                ('name', '=', rec.employee_id.id),
                ('date', '>=', rec.date_from),
                ('date', '<=', rec.date_to),
                ('late_check_in', '!=', False),
                ('final_status', '=', 'present'),
            ])
            if attendance_rec:
                rec.late_checkin_hrs = sum(attendance_rec.mapped('late_coming_hrs')) - sum(attendance_rec.mapped('late_coming_exemption'))

    @api.depends('employee_id')
    def _compute_early_checkout(self):
        for rec in self:
            rec.early_checkout_hrs = 0.0
            attendance_rec = self.env['nuro.employee.attendance'].search([
                ('name', '=', rec.employee_id.id),
                ('date', '>=', rec.date_from),
                ('date', '<=', rec.date_to),
                ('early_check_out', '!=', False),
                ('final_status', '=', 'present'),
            ])
            if attendance_rec:
                rec.early_checkout_hrs = sum(attendance_rec.mapped('early_coming_hrs')) - sum(attendance_rec.mapped('early_going_exemption'))

    def action_view_attendance_deduction(self):
        frmt = "%Y-%m-%d"
        from_date = datetime.strptime(str(self.date_from), frmt)
        to_date = datetime.strptime(str(self.date_to), frmt)
        tree_view = self.env.ref('btm_attendance_sync_report.view_nuro_attendance_list_day').id
        form_view_id = self.env.ref(
            'btm_attendance_sync_report.view_nuro_attendance_list_day_form').id
        action = self.env.ref('nuro_attendance.action_employee_attendance_module_action').read()[0]
        domain = [('date', '>=', from_date), ('date', '<=', to_date), ('final_status', 'in',
                                                                       ('present', 'absent'))]
        if self.employee_id:
            domain.append(('name', '=', self.employee_id.id))
        action['domain'] = domain
        action['context'] = {'create': False, 'edit': False}
        action['views'] = [(tree_view, 'tree'), (False, 'form')]
        return action
        # attendance_time_obj = self.env['attendance.deduction.time']
        # employee = self.employee_id and self.employee_id.id or False
        # department = self.department_id and self.department_id.id or False
        # attendance_time_obj.unlink()
        # tree_view = self.env.ref('btm_attendance_sync_report.attendance_report_deduction_tree').id
        # action = \
        # self.env.ref('btm_attendance_sync_report.attendance_report_deduction_action').read()[0]
        # attendance_time_obj.get_attendance_deduction_time_report(from_date=self.date_from,
        #                                                          to_date=self.date_to,
        #                                                          department=department,
        #                                                          employee=employee)
        # domain = []
        # if self.employee_id:
        #     domain.append(('employee_id', '=', self.employee_id.id))
        # if self.department_id:
        #     domain.append(('department_id', '=', self.department_id.id))
        # action['domain'] = domain
        # action['context'] = {'create': False, 'edit': False}
        # action['views'] = [(tree_view, 'tree'), (False, 'form')]
        # return action
