from odoo import fields, models


class ResPartner(models.Model):
    """
    Class Docstring
    """
    _inherit = 'res.partner'

    ws_corporate_configuration = fields.One2many('res.partner.corporates.discount', 'partner_id',
                                                 string='WS Corporate Configuration',
                                                 domain=[('panel', '=', 'ws')])


class ResPartnerCorporatesDiscount(models.Model):
    _inherit = 'res.partner.corporates.discount'

    panel = fields.Selection(selection_add=[('ws', 'Ward Service')], string='Panel')
    ws_partner_id = fields.Many2one('res.partner', string="Company")
    ws_master_ids = fields.Many2many('ward.service.product', 'ws_master_partner_rel', 'ws_test_master_id',
                                     'ws_test_panel_conf_id', string='Exclusion')
