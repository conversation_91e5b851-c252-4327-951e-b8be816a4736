# -*- coding: utf-8 -*-
# Part of nurosolution Pvt Ltd.


from odoo import fields, models, api, _
from odoo.exceptions import UserError
from datetime import datetime


class StockMove(models.Model):
    _inherit = 'account.move.line'

    is_inventory_closed = fields.Boolean('Is Inventory Closed', default=False)



class UserInventorySession(models.Model):
    _name = 'user.inventory.sale.session'
    _rec_name = 'user_id'
    _order = 'id desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'User Inventory Sale Session'

    state = fields.Selection(string="State",
                             selection=[('draft', 'Session Open'),
                                        ('Close', 'Session Close'),
                                        ('cancel', 'Cancelled'), ],
                             default='draft', readonly=True,
                             track_visibility="onchange",
                             states={'draft': [('readonly', False)]})
    opening_qty = fields.Float("Opening Qty", compute='_compute_data',
                                  readonly=True, store=True)
    sold_qty = fields.Float(" Total Sale Qty", compute='_compute_data', store=True,
                                      track_visibility="onchange",
                                      readonly=True)
    closing_qty = fields.Float("Closing Qty", required=True,
                                  track_visibility="onchange",
                                  readonly=True, states={'draft': [('readonly', False)]})
    balance_qty = fields.Float("Balance Qty", compute='get_balance_amount',
                                  readonly=True, track_visibility="onchange", store=True)
    date = fields.Date(string="Date", required=True, readonly=True, states={'draft': [('readonly', False)]},
                       track_visibility="onchange",
                       default=fields.Date.context_today, store=True)
    user_id = fields.Many2one('res.users', 'User', required=True, readonly=True,
                              states={'draft': [('readonly', False)]},
                              domain=[('sales_location_id', '!=', False)])
    sales_line_ids = fields.Many2many("sale.order.line", 'user_sale_line_rel',
                                      'inventory_session_id', 'sale_line_id',
                                      string="Cash Entries",
                                      readonly=True)
    stock_move_ids = fields.Many2many("stock.move", 'sale_stockmove_rel',
                                        'session_id', 'stock_move_id',
                                        string="Stock Moves",
                                        readonly=True)

    company_id = fields.Many2one('res.company',
                             string='Company',
                             change_default=True,
                             required=True,
                             readonly=True,
                             default=lambda self: self.env.company)


    remark = fields.Text('Remark', readonly=True, states={'draft': [('readonly', False)]})
    closing_picking_id = fields.Many2one('stock.picing', 'User Sale Inventory Closing', readonly=1)
    product_sales_closing_ids = fields.One2many('user.product.sale.line', 'session_id', states={'draft': [('readonly', False)]})

    def get_incoming_details(self, date):
        where = [tuple(locations.ids), tuple(locations.ids)]
        if date:
            date_str = "move.date::DATE>=%s and move.date::DATE<=%s"
            where.append(tuple([date]))
            where.append(tuple([date]))
        self._cr.execute(
            '''select sum(product_qty), product_id, product_uom 
            from stock_move move
            INNER JOIN stock_picking picking ON (move.picking_id = picking.id)
            INNER JOIN stock_picking_type picking_type ON (picking.picking_type_id = picking_type.id)
            where move.location_id NOT IN %s
            and move.location_dest_id IN %s
            and product_id IN %s and move.state = 'done' 
            and move.picking_id is not null
            and move.inventory_id is null 
            and picking.sale_id is null
            '''
            + (date_str and 'and ' + date_str + ' ' or '') + \
            '''group by product_id, product_uom''', tuple(where))
        results_incoming = self._cr.fetchall()
        for quantity, prod_id, prod_uom in results_incoming:
            incoming += quantity
        return  incoming

    # @api.onchange('user_id', 'date')
    # def _onchange_user_id(self):
    #
    #     if self.date and self.user_id:
    #         if self.user_id.sales_location_id:



class UserProductSalesline(models.Model):
    _name = 'user.product.sale.line'
    _description = 'User products Sale details'


    product_id = fields.Many2one('product.product', 'Product', index=True, copy=False)
    location_id = fields.Many2one('stock.location', 'Location', copy=False)
    user_id = fields.Many2one('res.users', 'Sales Person', index=True, copy=False)
    opening_qty = fields.Float('Opening Qty', copy=False)
    sold_qty = fields.Float('Sold Qty', copy=False)
    balance_qty = fields.Float('balance Qty', copy=False)
    closing_qty = fields.Float('Closing Qty', copy=False)
    session_id = fields.Many2one('user.inventory.sale.session', 'session_id', copy=False)