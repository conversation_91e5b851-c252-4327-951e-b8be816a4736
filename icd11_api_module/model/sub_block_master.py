from odoo import fields, models


class SubBlockMaster(models.Model):
    _name = 'sub.block.master'
    _description = 'Sub Block Master'
    _order = 'id DESC'

    name = fields.Char('Sub Block', index=True, required=True)
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user)
