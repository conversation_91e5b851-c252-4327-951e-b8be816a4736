# -*- coding: utf-8 -*-
# Part of SOFTPRIMECONSULTING PRIVATE LIMITED
from odoo.exceptions import UserError

from odoo import fields, models, api, _


class AppointmentTypeMaster(models.Model):
    _inherit = 'appointment.type.master'

    appointment_type = fields.Selection(selection_add=[('emergency', 'Emergency')])


class NuroAppointment(models.Model):
    _inherit = 'nuro.appointment'

    appointment_type = fields.Selection(selection_add=[('emergency', 'Emergency')])
    emergency_id = fields.Many2one('emergency.admission', 'Emergency')
    reason_emergency_transfer = fields.Char('Reason')
    emergency_payment_method = fields.Selection([('prepaid', 'Pre Paid'), ('postpaid', 'Post Paid')],
                                                string='Payment Method',
                                                default=lambda self: self.env.company.emergency_payment_method)
    free_day_care = fields.Boolean('Free Day Care', default=lambda self: self.env.company.free_day_care)
    eme_account_id = fields.Many2one('account.account', string='Emergency Account')
    eme_product_id = fields.Many2one('product.product', string='Emergency Product')
    emergency_type_id = fields.Many2one('emergency.type', string='Type', domain=lambda self: [
        ('id', 'in', self.env.user.emergency_type_ids.ids)])
    day_care = fields.Boolean('Day Care')
    payment_validation = fields.Boolean('Payment Validation')
    confirmation_validation = fields.Boolean('Confirmation Validation')

    @api.onchange('free_day_care', 'emergency_payment_method', 'appointment_type', 'state')
    def onchange_value_get_payment_validation(self):
        """
        Get Payment Validation
        """
        if self.state == 'draft' and (
                (self.env.company.emergency_payment_method == 'prepaid' and self.appointment_type == 'emergency') or (
                self.appointment_type in ('normal', 'dental'))):
            self.payment_validation = True
        else:
            self.payment_validation = False
        if self.state == 'draft' and (
                (self.env.company.emergency_payment_method == 'postpaid' and self.appointment_type == 'emergency') or (
                self.appointment_type == 'maternity')):
            self.confirmation_validation = True
        else:
            self.confirmation_validation = False

    def get_current_appointment_ref(self):
        """
        Get Current Appointment Ref
        """
        res = super(NuroAppointment, self).get_current_appointment_ref()
        if self.emergency_id:
            res = self.emergency_id.name
        return res

    @api.onchange('emergency_payment_method', 'free_day_care', 'emergency_type_id')
    def onchange_payment_type_view(self):
        """
        Onchange Payment Type
        """
        if self.appointment_type == 'emergency' and self.env.company.emergency_payment_method == 'prepaid' and \
                self.emergency_type_id:
            adult = self.env.ref('nuro_emergency.emergency_adult_admission_charges')
            pediatric = self.env.ref('nuro_emergency.emergency_child_admission_charges')
            product_id = adult
            if self.emergency_type_id.emergency_type == 'pediatric':
                product_id = pediatric
            amount = product_id.list_price
            self.appointment_charges = amount
            self.total_amount = amount
            self.eme_product_id = product_id.id
            self.eme_account_id = product_id.property_account_income_id.id or \
                                  product_id.categ_id.property_account_income_categ_id.id,

    def action_appointment_schedule(self):
        """
        Action Appointment Schedule
        """
        res = super(NuroAppointment, self).action_appointment_schedule()
        self.confirm_appointment_model()
        self.onchange_value_get_payment_validation()
        return res

    def _create_move_lines_appointment(self, amount):
        """
        Create Move Line Appointment
        """
        res = super(NuroAppointment, self)._create_move_lines_appointment(amount=amount)
        if self.appointment_type == 'emergency' and self.emergency_payment_method == 'prepaid' and \
                self.emergency_type_id:
            res.update({
                'product_id': self.eme_product_id.id,
                'account_id': self.eme_account_id.id
            })
        return res

    def _create_credit_move_line_vals(self, partner_id, amount, account_id):
        """
        Create Credit Move Line Vals
        """
        res = super(NuroAppointment, self)._create_credit_move_line_vals(partner_id=partner_id, amount=amount,
                                                                          account_id=account_id)
        if self.appointment_type == 'emergency' and self.emergency_payment_method == 'prepaid' and \
                self.emergency_type_id:
            res.update({
                'product_id': self.eme_product_id.id,
                'account_id': self.eme_account_id.id
            })
        return res

    def print_receipt_emergency(self):
        """
        Appointment Receipt printing
        :return:
        """
        return self.env.ref('nuro_emergency.action_nuro_appointment_emergency_thermal_receipt').report_action(self)

    def create_emergency_record_day_care(self, emergency_type_id, treatment_plan):
        """
        Create Emergency Record
        """
        if self.state not in ('scheduled', 'patient_in', 'waiting_result'):
            raise UserError(_('Record has been Processed already.!!!'))
        vals = {
            'patient_id': self.patient_id.id,
            'identification_code': self.patient_id.identification_code,
            'doctor_id': self.doctor_id.id,
            'branch_id': self.branch_id.id,
            'appointment_id': self.id,
            'emergency_type_id': emergency_type_id and emergency_type_id.id or False,
            'emergency_type': emergency_type_id and emergency_type_id.emergency_type or False,
            'treatment_plan': treatment_plan,
            'day_care': True,
            'state': 'draft'
        }
        emergency_id = self.env['emergency.admission'].create(vals)
        self.emergency_id = emergency_id.id
        self.state = 'completed'

    def create_emergency_record(self):
        """
        Create Emergency Record
        """
        vals = {
            'patient_id': self.patient_id.id,
            'identification_code': self.patient_id.identification_code,
            'appointment_id': self.id,
            'day_care': self.day_care,
            'emergency_type_id': self.emergency_type_id and self.emergency_type_id.id or False,
            'emergency_type': self.emergency_type_id and self.emergency_type_id.emergency_type or False,
            'state': 'draft'
        }
        emergency_id = self.env['emergency.admission'].create(vals)
        self.emergency_id = emergency_id.id
        if not (
                self.appointment_type == 'emergency' and self.emergency_payment_method == 'prepaid' and self.emergency_type_id):
            self.appointment_charges = 0.0
            self.total_amount = 0.0

    def confirm_appointment_model(self):
        """
        Confirm Appointment Super Call
        """
        res = super(NuroAppointment, self).confirm_appointment_model()
        if self.appointment_type == 'emergency':
            if not self.emergency_id:
                self.create_emergency_record()
                self.state = 'completed'
        return res

    def create_appointment_day_care_emergency(self):
        """
        Create Day Care Emergency Admission
        """
        ctx = {'default_appointment_id': self.id}
        action = self.env.ref('nuro_emergency.appointment_day_care_admission_request_action').read()[0]
        action['context'] = ctx
        return action
