<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="emergency_admission_form_view" model="ir.ui.view">
            <field name="name">Emergency Admission</field>
            <field name="model">emergency.admission</field>
            <field name="arch" type="xml">
                <form string="Emergency Admission">
                    <header>
                        <button name="initiate_emergency_process" type="object" string="Initiate" class="oe_highlight"
                                states="draft" groups="nuro_emergency.group_emergency_user"
                                invisible="context.get('eme_inv', False)"/>
                        <button name="create_emergency_appointment_invoice_return_inpatient" type="object"
                                string="Refund" class="oe_highlight" invisible="context.get('eme_inv', False)"
                                states="cashier_request" groups="nuro_emergency.group_emergency_user"/>
                        <button name="action_set_to_done" type="object" string="Discharge" class="oe_highlight"
                                states="initiated" groups="nuro_emergency.group_emergency_user"
                                invisible="context.get('eme_inv', False)"/>
                        <button name="open_payment_process_invoice" string="Payment" type="object" class="oe_highlight"
                                states="cashier_request" groups="nuro_cashier_closing.group_cashier_user"
                                invisible="context.get('eme_inv', False)"/>
                        <!--                        <button name="payment_with_credit" string="Payment with Credit" type="object" class="oe_highlight"-->
                        <!--                                states="cashier_request" groups="nuro_cashier_closing.group_cashier_user"-->
                        <!--                                invisible="context.get('eme_inv', False)"/>-->

                        <button name="doctor_change_wizard" string="Doctor Change" type="object" class="oe_highlight"
                                states="initiated" groups="nuro_emergency.group_emergency_user"
                                invisible="context.get('eme_inv', False)"/>
                        <button name="discharge_now" type="object" string="Discharge Now" class="oe_highlight"
                                groups="nuro_emergency.group_emergency_user"/>
                        <button name="print_discharge_request" type="object" string="Print Discharge"
                                class="oe_highlight" states="done" groups="nuro_emergency.group_emergency_user"/>
                        <button name="print_discharge_letter" type="object" string="Print Discharge Letter"
                                class="oe_highlight" states="done" groups="nuro_emergency.group_emergency_user"/>
                        <button name="print_discharge_card_request" type="object" string="Print Discharge Card"
                                class="oe_highlight" states="done" groups="nuro_emergency.group_emergency_user"/>
                        <button name="reopen_record" type="object" string="Reopen" class="oe_highlight"
                                states="done" groups="nuro_emergency.group_emergency_user"
                                invisible="context.get('eme_inv', False)"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,initiated,done,cancel"/>

                    </header>
                    <sheet>
                        <style>
                            .o_form_view .o_horizontal_separator {
                            color: #174290;
                            font-weight: bold;
                            }
                            a {
                            color: #174290;
                            text-decoration: none;
                            background-color: transparent;
                            }
                            h1 {
                            color: #174290;
                            }
                        </style>
                        <div name="button_box" class="oe_button_box" invisible="context.get('eme_inv', False)">
                            <button string="Surgery" name="action_view_patient_surgery" type="object"
                                    class="oe_stat_button" icon="fa-list"/>

                            <button string="Imaging Result" name="action_imaging_result_view" type="object"
                                    class="oe_stat_button" icon="fa-list"/>

                            <button string="Lab Result" name="action_labetst_result_view" type="object"
                                    class="oe_stat_button" icon="fa-list"/>

                            <button string="Appointments" name="open_patient_appointment_view_records" type="object"
                                    class="oe_stat_button" icon="fa-list">
                                <field name="total_appointment" readonly="1"/>
                            </button>

                            <button string="Prescriptions" name="open_patient_prescription_view_records" type="object"
                                    class="oe_stat_button" icon="fa-list">
                                <field name="total_prescription" readonly="1"/>
                            </button>

                        </div>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name" string="Code #"/>
                                <h1>
                                    <field name="name" class="oe_inline"/>
                                </h1>
                            </div>
                        </div>
                        <field name="invoice_amount" invisible="1"/>
                        <div class="oe_right" style="width: 300px;">
                            <div class="oe_title" style="width: 390px;" attrs="{'invisible': [('invoice_amount', '&lt;=', 0.0)]}">
                                <label for="invoice_amount" string="Balance"/>
                                <h1>
                                    <field name="invoice_amount" class="oe_inline" style="color:brown;"/>
                                </h1>
                            </div>

                            <div class="oe_title" style="width: 390px;" attrs="{'invisible': [('state', 'in', ('draft', 'done', 'cancel'))]}">
                                <label for="total_tat" string="TAT"/>
                                <h1>
                                    <field name="total_tat" class="oe_inline" style="color:black;" widget="float_time"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group>
                                <!--                                attrs="{'readonly': [('state', '!=', 'draft')]}"-->
                                <field name="patient_id" context="{'create': False, 'edit': False}" readonly="1"/>
                                <field name="identification_code" readonly="1"/>
                            </group>
                            <group>
                                <field name="date" readonly="1"/>
                                <field name="doctor_id" attrs="{'readonly': [('state', '!=', 'draft')]}"
                                       options="{'no_open': True, 'no_create': True}" required="1"
                                       domain="[('department', '=', 'emergency')]"/>
                                <field name="user_id" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"
                                       context="{'create': False, 'edit': False}"/>
                                <field name="emergency_payment_method" invisible="1" readonly="1" force_save="1"/>
                                <field name="emergency_type" invisible="1" readonly="1" force_save="1"/>
                                <!--                                <field name="type" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>-->
                                <field name="emergency_type_id" options="{'no_create': True, 'no_open': True}"
                                       attrs="{'readonly': ['|', ('state', '!=', 'draft'), ('emergency_payment_method', '=', 'prepaid')]}"
                                       required="1"/>
                                <field name="status_discharged" readonly="1"
                                       attrs="{'invisible': [('status_discharged', '=', False)]}"/>
                                <field name="free_day_care" invisible="1" readonly="1" force_save="1"/>
                                <field name="day_care" required="1"
                                       attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('free_day_care', '=', False)]}"/>
                                <field name="appointment_id" context="{'create': False, 'edit': False}" readonly="1"
                                       attrs="{'invisible': [('appointment_id', '=', False)]}"/>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="initiated_datetime" readonly="1"
                                       attrs="{'invisible': [('initiated_datetime', '=', False)]}"/>
                                <field name="discharged_datetime" readonly="1"
                                       attrs="{'invisible': [('discharged_datetime', '=', False)]}"/>
                            </group>
                            <group>
                                <group>
                                    <field name="discharge_type" readonly="1"
                                           attrs="{'invisible': [('discharge_type', '=', False)]}"/>
                                    <field name="discharge_with_credit"
                                           attrs="{'readonly': [('state', 'not in', ('cashier_request', 'initiated'))]}"
                                           groups="nuro_cashier_closing.group_cashier_user"/>
                                    <field name="refer_hospital" readonly="1"
                                           attrs="{'invisible': [('refer_hospital', '=', False)]}"/>
                                    <field name="refer_reason" readonly="1"
                                           attrs="{'invisible': [('refer_reason', '=', False)]}"/>
                                </group>
                            </group>
                        </group>
                        <notebook>
                            <page string="History Taking" name="history_tracking_temp" invisible="1">
                                <group>
                                    <group>
                                        <field name="chief_complaint"
                                               attrs="{'readonly': [('state', '!=', 'initiated')]}"/>
                                        <field name="family_history"
                                               attrs="{'readonly': [('state', '!=', 'initiated')]}"/>
                                        <field name="history_taking"
                                               attrs="{'readonly': [('state', '!=', 'initiated')]}"/>
                                    </group>
                                    <group>
                                        <field name="past_medical_history"
                                               attrs="{'readonly': [('state', '!=', 'initiated')]}"/>
                                        <field name="drug_history"
                                               attrs="{'readonly': [('state', '!=', 'initiated')]}"/>
                                        <field name="allergies"
                                               attrs="{'readonly': [('state', '!=', 'initiated')]}"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Treatment Plan" name="treatment_plan">
                                <field name="treatment_plan" attrs="{'readonly': [('state', '!=', 'initiated')]}"/>
                            </page>
                            <page string="Invoices" name="invoices" groups="nuro_cashier_closing.group_cashier_user"
                                  invisible="1">
                                <field name="invoice_line_ids" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="invoice_date_due"/>
                                        <field name="amount_total_signed" sum="Total" force_save="1"/>
                                        <field name="amount_residual_signed" sum="Balance" force_save="1"/>
                                        <field name="state"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Discharge Summary" name="discharge_summery">
                                <group>
                                    <group>
                                        <field name="discharge_type"
                                               attrs="{'readonly': [('state', '=', 'done')]}"/>
                                        <field name="refer_hospital"
                                               attrs="{'invisible': [('discharge_type', '!=', 'refer')], 'required': [('discharge_type', '=', 'refer')], 'readonly': [('state', '=', 'done')]}"/>
                                        <field name="refer_reason"
                                               attrs="{'invisible': [('discharge_type', '!=', 'refer')], 'required': [('discharge_type', '=', 'refer')], 'readonly': [('state', '=', 'done')]}"/>
                                    </group>
                                    <group>
                                        <field name="dod"
                                               attrs="{'invisible': [('discharge_type', '!=', 'died')], 'required': [('discharge_type', '=', 'died')], 'readonly': [('state', '=', 'done')]}"/>
                                        <field name="cod"
                                               attrs="{'invisible': [('discharge_type', '!=', 'died')], 'required': [('discharge_type', '=', 'died')], 'readonly': [('state', '=', 'done')]}"/>
                                    </group>
                                </group>
                                <group>
                                    <group>
                                        <field name="delay_reason_id" options="{'no_open': True, 'no_create': True}"
                                               attrs="{'invisible': [('status_discharged', '!=', 'delayed')], 'required':[('status_discharged', '=', 'delayed')], 'readonly': [('state', 'in', ('done', 'cancel'))]}"/>
                                        <field name="other" invisible="1"/>
                                    </group>
                                    <group>
                                        <field name="delay_reason"
                                               attrs="{'invisible': [('other', '!=', True)], 'required':[('other', '=', True)], 'readonly': [('state', 'in', ('done', 'cancel'))]}"/>
                                    </group>
                                </group>
                                <group>
                                    <group>
                                        <field name="admission_condition"
                                               attrs="{'readonly': [('state', '=', 'done')]}"/>
                                        <field name="followup_advice"
                                               attrs="{'readonly': [('state', '=', 'done')]}"/>
                                    </group>
                                    <group>
                                        <field name="admission_physical_condition"
                                               attrs="{'readonly': [('state', '=', 'done')]}"/>
                                        <field name="condition_on_discharge"
                                               attrs="{'readonly': [('state', '=', 'done')]}"/>
                                    </group>
                                </group>
                                <separator string="Discharge Medication Plan"/>
                                <button name="create_pharmacy_request" type="object" class="oe_highlight"
                                        string="request" attrs="{'invisible': [('state', '=', 'done')]}"/>
                                <field name="discharge_medication_line"
                                       attrs="{'readonly': [('state', '=', 'done')]}">
                                    <tree editable="bottom">
                                        <field name="state" invisible="1" readonly="1"/>
                                        <field name="product_id" required="1"
                                               options="{'no_open': True, 'no_create': True}"
                                               domain="[('is_medicine', '=', True)]"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="default_code" readonly="1"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="description"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="drug_form_id"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="drug_route_id"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="frequency" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="dose_text" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="dose" string="Time To Take"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="duration" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="duration_unit" required="1"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="qty" invisible="1"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="state" readonly="1" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Turn Around Time" name="turn_around_time">
                                <group col="3">
                                    <group>
                                        <field name="create_date" readonly="1" string="Appointment Datetime"/>
                                    </group>
                                    <group>
                                        <field name="initiated_datetime" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="app_in_diff" widget="float_time" readonly="1"/>
                                    </group>
                                </group>
                                <group col="3">
                                    <group>
                                        <field name="initiated_datetime" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="cashier_request_datetime" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="in_cr_diff" widget="float_time" readonly="1"/>
                                    </group>
                                </group>
                                <group col="3">
                                    <group>
                                        <field name="cashier_request_datetime" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="payment_done_datetime" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="cr_pd_diff" widget="float_time" readonly="1"/>
                                    </group>
                                </group>
                                <group col="3">
                                    <group>
                                        <field name="payment_done_datetime" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="discharged_datetime" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="pd_disc_diff" widget="float_time" readonly="1"/>
                                    </group>
                                </group>
                                <group col="3">
                                    <group>
                                    </group>
                                    <group>
                                    </group>
                                    <group>
                                        <field name="total_time_diff" widget="float_time" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="emergency_admission_list_view" model="ir.ui.view">
            <field name="name">Emergency Admission</field>
            <field name="model">emergency.admission</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <tree string="Emergency Admission" decoration-danger="status_discharged == 'delayed'">
                    <field name="name"/>
                    <field name="patient_id"/>
                    <field name="status_discharged" invisible="1"/>
                    <field name="identification_code"/>
                    <field name="date"/>
                    <field name="emergency_type" invisible="1"/>
                    <field name="emergency_type_id" readonly="1"/>
                    <field name="doctor_id" readonly="1"/>
                    <field name="initiated_datetime"/>
                    <field name="discharged_datetime"/>
                    <field name="invoice_amount" string="Balance Amount"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="emergency_admission_list_view_record_report" model="ir.ui.view">
            <field name="name">Emergency Admission</field>
            <field name="model">emergency.admission</field>
            <field name="priority">2</field>
            <field name="arch" type="xml">
                <tree string="Emergency Admission">
                    <field name="name"/>
                    <field name="patient_id"/>
                    <field name="identification_code"/>
                    <field name="doctor_id" readonly="1"/>
                    <field name="create_date" string="Appointment Datetime"/>
                    <field name="initiated_datetime"/>
                    <field name="app_in_diff" widget="float_time"/>
                    <field name="initiated_datetime"/>
                    <field name="cashier_request_datetime"/>
                    <field name="in_cr_diff" widget="float_time"/>
                    <field name="cashier_request_datetime"/>
                    <field name="payment_done_datetime"/>
                    <field name="cr_pd_diff" widget="float_time"/>
                    <field name="payment_done_datetime"/>
                    <field name="pd_disc_diff" widget="float_time"/>
                    <field name="total_time_diff" widget="float_time"/>
                    <field name="status_discharged"/>
                    <field name="delay_reason_id" string="Reason"/>
                    <field name="delay_reason" string="Remark"/>
                </tree>
            </field>
        </record>

        <record id="emergency_admission_search_view" model="ir.ui.view">
            <field name="name">Emergency Admission</field>
            <field name="model">emergency.admission</field>
            <field name="arch" type="xml">
                <search string="Emergency Admissionn">
                    <field name="name"/>
                    <field name="patient_id" string="Patient/ID#"/>
                    <filter name="initiated_admission" string="Initiated" domain="[('state', '=', 'initiated')]"/>
                    <group expand="0" string="Group By..." colspan="11" col="11">
                        <filter name="patient_filter" string="Patient" context="{'group_by':'patient_id'}"/>
                        <filter name="date" string="Date" context="{'group_by':'date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_emergency_admission_views" model="ir.actions.act_window">
            <field name="name">Emergency Admission</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">emergency.admission</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '!=', 'done')]</field>
            <field name="context">{'create': False}</field>
        </record>

        <record id="action_emergency_admission_views_complete" model="ir.actions.act_window">
            <field name="name">Emergency Admission</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">emergency.admission</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'done')]</field>
            <field name="context">{'create': False, 'edit': False}</field>
        </record>

        <record id="action_emergency_admission_views_admitted_cashier" model="ir.actions.act_window">
            <field name="name">Emergency Admission</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">emergency.admission</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'initiated')]</field>
            <field name="context">{'create': False, 'edit': False, 'delete': False}</field>
        </record>

        <record id="action_emergency_admission_views_cashier" model="ir.actions.act_window">
            <field name="name">Emergency Admission</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">emergency.admission</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', 'in', ('cashier_request', 'payment_done'))]</field>
            <field name="context">{'create': False, 'edit': False, 'delete': False}</field>
        </record>

        <record id="action_emergency_admission_views_discharged_cashier" model="ir.actions.act_window">
            <field name="name">Emergency Admission</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">emergency.admission</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'done')]</field>
            <field name="context">{'create': False, 'edit': False, 'delete': False}</field>
        </record>

        <record id="action_emergency_admission_views_discharged_cashier_list_view" model="ir.actions.act_window">
            <field name="name">Emergency Admission</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">emergency.admission</field>
            <field name="view_mode">tree</field>
            <field name="domain">[('state', '=', 'done')]</field>
            <field name="context">{'create': False, 'edit': False, 'delete': False}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_emergency.emergency_admission_list_view_record_report')})]"/>
        </record>

        <record id="action_imaging_test_completed_doctor_emergency" model="ir.actions.act_window">
            <field name="name">Imaging Test</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.imaging.test</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('state', '=', 'completed'), ('emergency_record', '=', True)]</field>
        </record>

        <record id="action_nuro_labtest_doctor_completed_view_action_emergency" model="ir.actions.act_window">
            <field name="name">Labtest</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.medical.labtest.result</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="nuro_labtest.nuro_labtest_result_search_view"/>
            <field name="domain">[('parent_id', '=', False), ('state', 'not in', ('request',
                'sample_collected')),('emergency_record', '=', True)]
            </field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_labtest.nuro_medical_labtest_list_view')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_labtest.nuro_medical_labtest_criteria_form_view')})]"/>
        </record>

    </data>
</odoo>
