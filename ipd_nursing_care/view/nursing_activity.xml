<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="form_view_nursing_activity" model="ir.ui.view">
            <field name="name">Nursing Activity</field>
            <field name="model">nursing.activity</field>
            <field name="arch" type="xml">
                <form string="Nursing Activity">
                    <header>
                        <field name="state" widget="statusbar" class="oe_highlight"
                               statusbar_visible="scheduled,onetime,delayed,skipped,discontinue_doctor"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="reference" placeholder="Referance" required="1" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="name" readonly="1"/>
                                <field name="patient_id" options="{'no_open': True, 'no_create': True}"
                                       readonly="1"/>
                                <field name="product_id" options="{'no_open': True, 'no_create': True}"
                                       readonly="1"/>
                                <field name="description" readonly="1"/>
                                <field name="type" readonly="1"/>
                            </group>
                            <group>
                                <field name="assign_to_user_id" options="{'no_open': True, 'no_create': True}"
                                       required="1" attrs="{'readonly': [('state', '!=', 'scheduled')]}"/>
                                <field name="start_time" readonly="1"/>
                                <field name="end_time" readonly="1"/>
                                <field name="given_time" readonly="1"/>
                                <field name="scheduled_time" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="tree_view_nursing_activity" model="ir.ui.view">
            <field name="name">Nursing Activity</field>
            <field name="model">nursing.activity</field>
            <field name="arch" type="xml">
                <tree string="Nursing Activity">
                    <field name="reference" invisible="1"/>
                    <field name="name"/>
                    <field name="patient_id" invisible="1"/>
                    <field name="description"/>
                    <field name="type"/>
                    <field name="create_date" invisible="1"/>
                    <field name="create_uid"/>
                    <field name="start_time"/>
                    <field name="end_time"/>
                    <field name="given_time"/>
                    <field name="scheduled_time"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="search_view_nursing_activity" model="ir.ui.view">
            <field name="name">Nursing Activity</field>
            <field name="model">nursing.activity</field>
            <field name="arch" type="xml">
                <search string="Nursing Activity">
                    <field name="name" string="Name"/>
                    <field name="patient_id" string="Patient"/>
                    <field name="reference" string="Reference"/>
                    <group expand="0" string="Group By..." colspan="11" col="11">
                        <filter string="Reference" name="reference_group_by" context="{'group_by':'reference'}"/>
                        <filter string="Patient" name="patient_id_group_by" context="{'group_by':'patient_id'}"/>
                        <filter string="Type" name="type_group_by" context="{'group_by':'type'}"/>
                        <filter string="state" name="state_group_by" context="{'group_by':'state'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_action_nursing_activity" model="ir.actions.act_window">
            <field name="name">Nursing Activity</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nursing.activity</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'delete': False, 'create': False}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('tree_view_nursing_activity')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('form_view_nursing_activity')})]"/>
        </record>

    </data>
</odoo>
