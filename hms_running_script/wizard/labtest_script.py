# -*- coding: utf-8 -*-
# Copyright  NUROSOLUTION Pvt Ltd
from datetime import datetime, timedelta

from odoo import models, fields


def str_to_float(str_value):
    float_val = 0.0
    try:
        float_val = float(str_value)
    except ValueError:
        value = ''
        for str_val in str_value:
            if str_val.isdigit():
                value = value + str_val
        if len(value) > 0:
            float_val = float(value)
    return float_val


def date_to_datetime(dt):
    given_dt = str(dt) + " 00:00:00"
    converted_dt = datetime.strptime(given_dt, "%Y-%m-%d %H:%M:%S")
    return converted_dt


class LabtestScript(models.TransientModel):
    _name = 'labtest.script'
    _description = 'Labtest Script'

    start_date = fields.Date('Start Date')
    end_date = fields.Date('End Date')

    def update_labtest_criteria(self):
        """
        Update labtest result criteria
        :return:
        """
        domain = [('outcome_result', '!=', False), ('is_master', '!=', True)]
        if self.start_date:
            start_datetime = date_to_datetime(self.start_date)
            domain.append(('create_date', '>=', start_datetime))
        if self.end_date:
            end_date = date_to_datetime(self.end_date) + timedelta(days=1)
            domain.append(('create_date', '<', end_date))
        criteria_ids = self.env['nuro.medical.lab.result.criteria'].sudo().search(domain)
        for criteria in criteria_ids:
            criteria.result_value = str_to_float(criteria.outcome_result)

    def update_move_payment(self, moves):
        move_line = self.env['account.move.line']
        moves.write({'cash_payment': True})
        aml = move_line.sudo().search([('move_id', 'in', moves.ids),
                                       ('payment_id', '!=', False)])
        payment_ids = aml.mapped('payment_id')
        payment_ids.write({'cash_payment': True})

    def update_appointment_cash_payment(self):
        """
        Update cah payment boolean in appointment
        :return:
        """
        query = """
                select id from nuro_appointment where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and appointment_date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and appointment_date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        appointments_ids = [i[0] for i in result]
        appointments = self.env['nuro.appointment'].sudo().browse(appointments_ids)
        move = self.env['account.move']
        moves = move.sudo().search([
            ('appointment_id', 'in', appointments.ids),
            ('state', '=', 'posted'),
            ('appointment_invoice', '=', True)
        ])
        if moves:
            self.update_move_payment(moves)

    def update_radiology_cash_payment(self):
        """
       Update cah payment boolean in radiology
       :return:
       """
        query = """
                select id from nuro_imaging_entry where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        imaging_ids = [i[0] for i in result]
        entries = self.env['nuro.imaging.entry'].sudo().browse(imaging_ids)
        move = self.env['account.move']
        moves = move.sudo().search([('imaging_entry_id', 'in', entries.ids), ('state', '=', 'posted')])
        if moves:
            self.update_move_payment(moves)

    def update_op_cash_payment(self):
        """
       Update cah payment boolean in other procedure
       :return:
       """
        query = """
                select id from nuro_op_entry where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        op_ids = [i[0] for i in result]
        entries = self.env['nuro.op.entry'].sudo().browse(op_ids)
        move = self.env['account.move']
        moves = move.sudo().search([('op_entry_id', 'in', entries.ids), ('state', '=', 'posted')])
        if moves:
            self.update_move_payment(moves)

    def update_surgery_cash_payment(self):
        """
       Update cah payment boolean in surgery
       :return:
       """
        query = """
                select id from nuro_surgery_entry where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        surgery_ids = [i[0] for i in result]
        entries = self.env['nuro.surgery.entry'].sudo().browse(surgery_ids)
        move = self.env['account.move']
        moves = move.sudo().search([('surgery_entry_id', 'in', entries.ids), ('state', '=', 'posted')])
        if moves:
            self.update_move_payment(moves)

    def update_lab_entry_cash_payment(self):
        """
       Update cah payment boolean in surgery
       :return:
       """
        query = """
                select id from nuro_lab_entry where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        lab_ids = [i[0] for i in result]
        entries = self.env['nuro.lab.entry'].sudo().browse(lab_ids)
        move = self.env['account.move']
        moves = move.sudo().search([('lab_entry_id', 'in', entries.ids), ('state', '=', 'posted')])
        if moves:
            self.update_move_payment(moves)

    def update_blood_transfusion_cash_payment(self):
        """
          Update cah payment boolean in blood transfusion
          :return:
          """
        query = """
                select id from nuro_blood_transfusion where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        bt_ids = [i[0] for i in result]
        entries = self.env['nuro.blood.transfusion'].sudo().browse(bt_ids)
        move = self.env['account.move']
        moves = move.sudo().search([('blood_transfusion_id', 'in', entries.ids), ('state', '=', 'posted')])
        if moves:
            self.update_move_payment(moves)

    def update_medical_test_cash_payment(self):
        """
          Update cah payment boolean in blood transfusion
          :return:
          """
        query = """
                select id from nuro_medical_test where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        mt_ids = [i[0] for i in result]
        entries = self.env['nuro.medical.test'].sudo().browse(mt_ids)
        move = self.env['account.move']
        moves = move.sudo().search([('medical_test_id', 'in', entries.ids), ('state', '=', 'posted')])
        if moves:
            self.update_move_payment(moves)

    def update_vaccination_cash_payment(self):
        """
          Update cah payment boolean in vaccination
          :return:
          """
        query = """
                select id from nuro_cashier_vaccine where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        vaccine_ids = [i[0] for i in result]
        entries = self.env['nuro.cashier.vaccine'].sudo().browse(vaccine_ids)
        move = self.env['account.move']
        moves = move.sudo().search([('vaccine_id', 'in', entries.ids), ('state', '=', 'posted')])
        if moves:
            self.update_move_payment(moves)

    def update_ward_service_cash_payment(self):
        """
          Update cah payment boolean in ward service
          :return:
          """
        query = """
                select id from ward_service_entry where payment_method is not null and payment_method = 'cash'
                """
        if self.start_date:
            query = query + """ and date::timestamp::date >= '%s'""" % self.start_date
        if self.end_date:
            query = query + """ and date::timestamp::date <= '%s'""" % self.end_date
        self.env.cr.execute(query)
        result = self.env.cr.fetchall()
        ws_ids = [i[0] for i in result]
        entries = self.env['ward.service.entry'].sudo().browse(ws_ids)
        move = self.env['account.move']
        moves = move.sudo().search([('ws_entry_id', 'in', entries.ids), ('state', '=', 'posted')])
        if moves:
            self.update_move_payment(moves)
