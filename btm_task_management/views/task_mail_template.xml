<odoo>
    <record id="btm_task_assign_template" model="mail.template">
        <field name="name">New Task Assigned</field>
        <field name="email_from">${(object.create_uid and object.create_uid.partner_id.email) |safe}&gt;</field>
        <field name="email_to">${object.user_id.email|safe}</field>
        <field name="model_id" ref="btm_task_management.model_btm_task"/>
        <field name="subject">New Task ${(object.name != False and object.name)|safe}</field>
        <field name="body_html" type="html">
            <div style="margin: 0px; padding: 0px;">
                <p style="margin: 0px; padding: 0px; font-size: 13px;">
                    Hello ${object.user_id.name},
                    <br/>
                    <br/>
                    A new task is assigned to you. Please review below link-
                    <div style="margin: 16px 0px 16px 0px;">
                        <a href="${object.access_url}"
                           style="background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;">
                            View Task
                        </a>
                        <br/>
                    </div>
                </p>

                <p>
                    Thank you,
                    <br/>
                </p>
            </div>
        </field>
    </record>

    <record id="btm_task_done_template" model="mail.template">
        <field name="name">Task completed</field>
        <field name="email_from">${(object.user_id and object.user_id.partner_id.email) |safe}&gt;</field>
        <field name="email_to">${(object.create_uid.partner_id and object.create_uid.partner_id.email)|safe}</field>
        <field name="model_id" ref="btm_task_management.model_btm_task"/>
        <field name="subject">Task Completed ${(object.name != False and object.name)|safe}</field>
        <field name="body_html" type="html">
            <div style="margin: 0px; padding: 0px;">
                <p style="margin: 0px; padding: 0px; font-size: 13px;">
                    Hello ${object.create_uid.name},
                    <br/>
                    <br/>
                    Task ${object.name|safe} is completed by assigned user. Please review below link-
                    <div style="margin: 16px 0px 16px 0px;">
                        <a href="${object.access_url}"
                           style="background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;">
                            View Task
                        </a>
                        <br/>
                    </div>
                </p>

                <p>
                    Thank you,
                    <br/>
                </p>
            </div>
        </field>
    </record>
</odoo>