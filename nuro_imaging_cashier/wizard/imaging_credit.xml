<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="view_imaging_credit_wizard_form" model="ir.ui.view">
            <field name="name">nuro.imaging.credit.wizard.form</field>
            <field name="model">nuro.imaging.credit.wizard</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <group>
                            <field name="user_id" options="{'no_create': True,'no_open': True}"/>
                            <field name="imaging_entry_id" readonly="1" force_save="1"/>
                            <field name="memo"/>
                        </group>
                        <group>
                            <field name="total_amount" force_save="1"/>
                            <field name="discount_amount"
                                   attrs="{'invisible': [('bill_to_type', 'in', ('hospital', 'hospital_employee'))]}"/>
                            <field name="amount" force_save="1"/>
                            <field name="bill_to_type"/>
                            <field name="employee_id"
                                   attrs="{'invisible':[('bill_to_type', 'in', ('patient', 'company', 'regular', 'hospital'))],
                                   'required':[('bill_to_type','in',('employee', 'hospital_employee'))]}"
                                   options='{"no_open": True, "no_create": True}'/>
                            <field name="description_hospital"
                                   attrs="{'invisible':[('bill_to_type','not in', ('hospital', 'hospital_employee', 'employee', 'regular'))],
                                   'required':[('bill_to_type','in',('hospital', 'hospital_employee', 'employee', 'regular'))]}"/>
                            <field name="bill_to_user_id" options="{'no_create': True,'no_open': True}"
                                   attrs="{'readonly':[('bill_to_type', 'in', ('patient', 'employee', 'company'))],
                                   'invisible':[('bill_to_type', 'in',('employee', 'hospital_employee'))]}"
                                   force_save="1"/>
                        </group>
                    </group>
                    <group string="Expense Information"
                           attrs="{'invisible': [('bill_to_type', '!=', 'hospital_employee')]}">
                        <group>
                            <field name="expense_employee_amount" readonly="1" force_save="1"
                                   attrs="{'invisible': [('expense_employee_amount', '=', 0.0)]}"/>
                            <field name="expense_partner_id" options='{"no_open": True, "no_create": True}'/>
                        </group>
                        <group>
                            <field name="expense_patient_amount" readonly="1" force_save="1"
                                   attrs="{'invisible': [('expense_patient_amount', '=', 0.0)]}"/>
                            <field name="expense_cash_payment"/>
                        </group>
                    </group>
                    <group string="Credit Limit Assign/Revise" groups="nuro_cashier_closing.group_cashier_manager">
                        <group>
                            <field name="extend_credit"/>
                        </group>
                        <group>
                            <field name="is_credit_allow" force_save="1" invisible="1"
                                   attrs="{'invisible': [('extend_credit', '!=', True)]}"/>
                            <field name="current_credit_limit" attrs="{'invisible': [('extend_credit', '!=', True)]}"/>
                            <field name="extended_credit_amount"
                                   attrs="{'invisible': [('extend_credit', '!=', True)]}"/>
                            <field name="responsible_person_id" attrs="{'invisible': [('extend_credit', '!=', True)],
                            'required': [('extend_credit', '=', True)]}"/>
                            <field name="description" attrs="{'invisible': [('extend_credit', '!=', True)]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Imaging Partial Payment Wizard">
                            <field name="credit_payment_line">
                                <tree create="false" edit="false">
                                    <field name="test_type_id" force_save="1"/>
                                    <field name="department_id" force_save="1"/>
                                    <field name="test_charge" force_save="1"/>
                                    <field name="discount_amount" force_save="1"/>
                                    <field name="total_amount" force_save="1"/>
                                </tree>
                                <form>
                                    <group>
                                        <group>
                                            <field name="test_type_id" readonly="1" force_save="1"/>
                                            <field name="department_id" readonly="1" force_save="1"/>
                                            <field name="test_charge" readonly="1" force_save="1"/>
                                        </group>
                                        <group>
                                            <field name="discount_amount" readonly="1" force_save="1"/>
                                            <field name="total_amount" readonly="1" force_save="1"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                    <footer>
                        <button string="Pay" type="object" name="action_imaging_credit" class="oe_highlight"/>
                        <button string="Close" class="btn-default oe_highlight" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_imaging_credit_wizard_appointment" model="ir.actions.act_window">
            <field name="name">Credit Wizard</field>
            <field name="res_model">nuro.imaging.credit.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_imaging_credit_wizard_form"/>
            <field name="target">new</field>
        </record>

    </data>
</odoo>