# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
"""
Module Docstring
"""
from odoo import fields, models


class ResConfigSettings(models.TransientModel):
    """
    Class Docstring
    """
    _inherit = 'res.config.settings'

    vaccine_product_id = fields.Many2one('product.product', string='Vaccine Product',
                                         domain=[('panel_product', '=', True)])
    vaccine_discount_account_id = fields.Many2one('account.account', string='Vaccine Discount Account')

    def get_values(self):
        """
        Get Value for the Imaging Product and account Configuration
        :return:
        """
        res = super(ResConfigSettings, self).get_values()
        res.update(
            vaccine_product_id=int(self.env['ir.config_parameter'].sudo().get_param(
                'vaccine_product_id')),
            vaccine_discount_account_id=int(self.env['ir.config_parameter'].sudo().get_param(
                'vaccine_discount_account_id')),
        )
        return res

    def set_values(self):
        """
        Method to set value for the Imaging product and account to be configured
        :return:
        """
        super(ResConfigSettings, self).set_values()
        self.env['ir.config_parameter'].set_param('vaccine_product_id',
                                                  self.vaccine_product_id.id)
        self.env['ir.config_parameter'].set_param('vaccine_discount_account_id',
                                                  self.vaccine_discount_account_id.id)
