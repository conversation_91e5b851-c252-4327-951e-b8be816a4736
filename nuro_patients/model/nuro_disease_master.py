# -*- coding: utf-8 -*-
# Part of NUROSOLUTION PRIVATE LIMITED
"""
Module Docstring
"""
from odoo import fields, models


class NuroDiseaseMaster(models.Model):
    """
    Class Docstring
    """
    _name = 'nuro.disease.master'
    _description = 'Disease Master'
    _order = 'id DESC'

    name = fields.Char('Name', index=True)
    category_id = fields.Many2one('nuro.disease.category', string='Category')

    _sql_constraints = [
        ('name_category_uniq', 'unique (name,category_id)', 'Disease name And Category Already has been Created !')
    ]
