from odoo import models, fields, api
import xlsxwriter
import base64


class ProductSale(models.TransientModel):
    _name = 'product.sale.wiz'

    start_date = fields.Date(string="Start Date")
    end_date = fields.Date(string="End Date")
    product_id = fields.Many2one('product.product', string="Select Product")
    name = fields.Char("Name", size=256)

    xlsx_output = fields.Binary("Download", readonly=True)

    def get_data(self):
        query = '''SELECT name,product_uom_qty,date FROM stock_move WHERE CAST(date as DATE) BETWEEN %s AND %s AND name=%s;'''
        self._cr.execute(query, (self.start_date, self.end_date, self.product_id.name,))
        data = self._cr.fetchall()
        return data

    def generate_xlsx_report(self):
        f_name = '/tmp/sale_product.xls'
        workbook = xlsxwriter.Workbook(f_name)
        sheet = workbook.add_worksheet('Sales')
        sheet.write('A1', "Product")
        sheet.write('B1', 'Quantity')
        sheet.write('C1', "Date")
        date_format = workbook.add_format({'num_format': 'yyyy/mm/dd'})
        row = 1
        column = 0
        for data in self.get_data():
            print(data)
            sheet.write(row, column, data[0])
            sheet.write(row, column+1, data[1])
            sheet.write(row, column+2, data[2], date_format)
            row +=1

        workbook.close()
        read_file = open(f_name, 'rb')
        data = read_file.read()
        read_file.close()
        name = "Product Sale Report"
        output_xl = self.env['product.sale.wiz'].create(
            {'name': name + '.xlsx', 'xlsx_output': base64.encodebytes(data)})

        return {
            'res_id': output_xl.id,
            'name': 'Sale Order Excel Report',
            'res_model': 'product.sale.wiz',
            'view_mode': 'form',
            'view_type': 'form',
            'view_id': False,
            'target': 'new',
            'type': 'ir.actions.act_window'
        }


