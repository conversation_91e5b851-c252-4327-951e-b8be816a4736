# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class GymMemerAttendance(models.Model):
    _name = 'gym.member.attendance'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'utm.mixin']
    _order = 'id desc'
    _description = 'Attendance'

    name = fields.Char(copy=False, index=True, default=lambda self: _('New'))
    date = fields.Date('Date', tracking=True)
    session_id = fields.Many2one('session.slot', string='Session', tracking=True)
    location_id = fields.Many2one('gym.location', string='Location', tracking=True)
    attendance_member_line = fields.One2many('attendance.member.line', 'attendance_id')
    company_id = fields.Many2one('res.company', string='Company', required=True, readonly=False,
                                 default=lambda self: self.env.company)
    state = fields.Selection([('draft', 'Draft'), ('confirm', 'Confirm'),
                              ('cancel', 'Cancel')],
                             default='draft', tracking=True)

    @api.constrains('date', 'session_id', 'state')
    def check_duplicate_attendance(self):
        old_record = self.env['gym.member.attendance'].search([
            ('date', '=', self.date),
            ('session_id', '=', self.session_id.id),
            ('state', '!=', 'cancel')
        ])
        if old_record and len(old_record) > 1:
            raise ValidationError(_(f'Attendance is already created for session'
                                    f'[{self.session_id.display_name}] '
                                    f'and date [{self.date}]'))

    @api.onchange('session_id', 'date')
    def onchange_session_date(self):
        line_lst = []
        self.attendance_member_line = False
        if self.session_id:
            self.location_id = self.session_id.location_id.id
        if self.session_id and self.date:
            members = self.env['gym.member'].search([
                ('session_id', '=', self.session_id.id),
                ('from_date', '<=', self.date),
                ('to_date', '>=', self.date)
            ])
            if members:
                for mem in members:
                    line_lst.append((0, 0, {
                        'partner_id': mem.partner_id.id,
                        'member_id': mem.id,
                        'plan_id': mem.membership_plan_id.id,
                        'start_date': mem.from_date,
                        'end_date': mem.to_date
                    }))
                self.attendance_member_line = line_lst

    def confirm_member_attendance(self):
        if not self.attendance_member_line:
            raise ValidationError(_('Without member line you can not confirm'))
        if self.attendance_member_line:
            for line in self.attendance_member_line:
                line.state = 'confirm'
            self.state = 'confirm'

    def reset_member_attendance(self):
        if self.attendance_member_line:
            for line in self.attendance_member_line:
                line.state = 'draft'
            self.state = 'draft'

    @api.model
    def create(self, vals):
        """ generate sequence number for buy gym membership """
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('gym.member.attendance.seq') or 'New'
        res = super().create(vals)
        return res


class AttendanceLine(models.Model):
    _name = 'attendance.member.line'
    _description = 'Line'

    attendance_id = fields.Many2one('gym.member.attendance', ondelete='cascade')
    member_id = fields.Many2one('gym.member', 'Member')
    partner_id = fields.Many2one('res.partner', 'Customer')
    plan_id = fields.Many2one('product.product', 'Plan')
    is_attendance = fields.Boolean('Attendance')
    company_id = fields.Many2one('res.company', string='Company', required=True, readonly=False,
                                 default=lambda self: self.env.company)
    start_date = fields.Date('Start Date')
    end_date = fields.Date('End Date')
    state = fields.Selection([('draft', 'Draft'), ('confirm', 'Confirm')],
                             default='draft', tracking=True)
    date = fields.Date('Date', related='attendance_id.date')
    session_id = fields.Many2one('session.slot', string='Session', related='attendance_id.session_id')
    location_id = fields.Many2one('gym.location', string='Location', related='attendance_id.location_id')
