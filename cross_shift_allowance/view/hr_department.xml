<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Inherit Department Form View to Add Cross Shift Allowance Fields -->
    <record id="view_department_form_inherit_cross_shift_allowance" model="ir.ui.view">
        <field name="name">hr.department.form.cross.shift.allowance</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="night_shift_allowance.view_department_form_inherit_night_shift_allowance"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='company_id']" position="before">
                <field name="cross_shift_allowance" invisible="0"/>
            </xpath>
            <xpath expr="//field[@name='ns_allowance_amount']" position="after">
                <field name="cross_shift_allowance" invisible="1"/>
                <field name="cs_allowance_amount" attrs="{'invisible': [('cross_shift_allowance', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <!-- Inherit Contract Form View to Add Cross Shift Fields -->
    <record id="hr_contract_view_form_inherit_record_cross_shift_allowance" model="ir.ui.view">
        <field name="name">hr.contract.form.cross.shift.allowance</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='advantages']" position="after">
                <field name="cross_shift_allowance" invisible="1"/>
                <field name="cs_allowance_amount" attrs="{'invisible': [('cross_shift_allowance', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <!-- Cross Shift Allowance Form View -->
    <record id="cross_shift_allowance_view_form" model="ir.ui.view">
        <field name="name">allowance.deduction.parent.form.cross.shift</field>
        <field name="model">allowance.deduction.parent</field>
        <field name="arch" type="xml">
            <form string="Deduction">
                <header>
                    <button name="action_approved" type="object" string="Approve" class="oe_highlight"
                            states="to_approve" groups="hr.group_hr_manager"/>
                    <button name="submit_for_approval" type="object" string="Submit" class="oe_highlight"
                            states="draft" groups="hr.group_hr_user,hr.group_hr_manager"/>
                    <button name="action_cancel" type="object" string="Cancel" class="oe_highlight"
                            states="to_approve,approved" groups="hr.group_hr_user,hr.group_hr_manager"/>
                    <button name="reset_draft" type="object" string="Reset" class="oe_highlight"
                            states="cancel" groups="hr.group_hr_user,hr.group_hr_manager"/>
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,to_approve,approved,canceled"
                           statusbar_colors="{'approved':'blue'}"/>
                </header>
                <sheet>
                    <div class="oe_edit_only">
                        <label for="name" class="oe_inline"/>
                    </div>
                    <h1>
                        <field name="name" class="oe_inline" readonly="1"/>
                    </h1>
                    <group>
                        <group>
                            <field name="department_id" attrs="{'readonly': [('state', '!=', 'draft')]}"
                                   options="{'no_open': True, 'no_create': True}"/>
                            <field name="created_by"/>
                            <field name="approver_name" attrs="{'readonly': [('state', '!=', 'draft')]}"
                                   options="{'no_open': True, 'no_create': True}"/>
                            <field name="date" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="month" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="date_from" readonly="1" force_save="1"/>
                            <field name="date_to" readonly="1" force_save="1"/>
                            <field name="create_date" readonly="1"/>
                        </group>
                        <group>
                            <field name="type_id" readonly="1" required="1" force_save="1"
                                   options="{'no_open': True, 'no_create': True}"/>
                            <field name="type" invisible="1"/>
                            <field name="is_cross_shift_allowance" readonly="1" force_save="1" invisible="1"/>
                            <field name="ad_compute_types" invisible="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page name="allowance_deduction_lines" string="Allowance &amp; Deduction Lines">
                            <field name="allowance_deduction_line"
                                   attrs="{'readonly': [('state', '!=', 'draft')]}"
                                   context="{
                                       'default_date': date,
                                       'default_type_id': type_id,
                                       'default_type': type,
                                       'default_ad_compute_types': ad_compute_types,
                                       'default_created_by': created_by,
                                       'default_approver_name': approver_name
                                   }">
                                <tree editable="bottom" create="false" decoration-danger="amount &lt;= 0.0">
                                    <field name="barcode" readonly="1" invisible="1"/>
                                    <field name="employee_id" readonly="1"/>
                                    <field name="job_id" readonly="1"/>
                                    <field name="department_id" readonly="1"/>
                                    <field name="basic_salary" readonly="1" optional="hide"
                                           attrs="{'column_invisible': [('parent.type', '!=', 'deduction')]}"/>
                                    <field name="allowance" readonly="1" optional="hide"
                                           attrs="{'column_invisible': [('parent.type', '!=', 'deduction')]}"/>
                                    <field name="deduction" readonly="1" optional="hide"
                                           attrs="{'column_invisible': [('parent.type', '!=', 'deduction')]}"/>
                                    <field name="net_salary" readonly="1" invisible="1"/>
                                    <field name="cross_shift_count_total" readonly="1"/>
                                    <field name="cross_shift_count" readonly="1"/>
                                    <field name="remaining_cross_shifts" readonly="1"/>
                                    <field name="carried_from_last_month" readonly="1"/>
                                    <field name="present_last_month" readonly="1"/>
                                    <field name="rate_per_shift_cross" readonly="1"/>
                                    <field name="amount" string="Allowance Amount" readonly="1"/>
                                    <field name="state" readonly="1"/>
                                    <button name="open_record_attendance_record_cross_shift" class="oe_highlight"
                                            type="object" string="View Details"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Action for Cross Shift Allowance -->
    <record id="view_cross_shift_allowance_allow_ded_action" model="ir.actions.act_window">
        <field name="name">Cross Shift Allowance</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">allowance.deduction.parent</field>
        <field name="view_mode">tree,form</field>
        <field name="view_ids" eval="[
            (5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_allowances_deduction.allowance_deduction_parent_list_view')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('cross_shift_allowance.cross_shift_allowance_view_form')})
        ]"/>
        <field name="domain">[('is_cross_shift_allowance', '=', True)]</field>
        <field name="context">{'default_is_cross_shift_allowance': True}</field>
    </record>

    <!-- Menu Item for Cross Shift Allowance -->
    <menuitem id="cross_shift_allow_ded_menu"
              parent="nuro_allowances_deduction.alw_ded_main_menu"
              sequence="700"
              name="Cross Shift Allowance Batch"
              action="view_cross_shift_allowance_allow_ded_action"
              groups="hr.group_hr_manager,hr_payroll.group_hr_payroll_manager,hr_payroll.group_hr_payroll_user"/>

</odoo>
