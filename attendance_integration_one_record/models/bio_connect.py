# -*- coding: utf-8 -*-
# Copyright  Softprime Consulting Pvt Ltd
import logging
from datetime import timedelta,datetime
from zk import ZK
import pytz
import math
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo import models, fields, api, tools, _
_logger = logging.getLogger(__name__)
global _zk_lib_warning
_zk_lib_warning = False
try:
    import ZK
    _zk_lib_imported = True
except ImportError:
    if not _zk_lib_warning:
        _logger.warning(
            "The `ZK` Python module is not installed, Try: pip install -U pyzk."
        )
        _zk_lib_warning = True


class NuroBioConnect(models.Model):
    _inherit = 'nuro.biometric.connect'

    def _get_shift_name(self, emp, date):
        """
        check employee shift from shift planing line
        """
        resource_id = False
        shift_planning_line = self.env['nuro.shift.management.line']
        shift_line_id = shift_planning_line.search([
            ('employee_id', '=', emp.id),
            ('nuro_shift_management_id.from_date', '<=', date),
            ('nuro_shift_management_id.to_date', '>=', date),
            ('nuro_shift_management_id.state', '=', 'approved')
        ])
        today_date = fields.Date.context_today(self)
        today_date = date
        fields_name = 'day_' + str(today_date.day)
        if shift_line_id:
            shift_name = shift_line_id.read()[0][fields_name]
            if shift_name:
                resource_id = self.env['resource.calendar'].sudo().search([
                    ('code', '=', shift_name)], limit=1)
            else:
                resource_id = self.env['resource.calendar'].sudo().search([
                    ('default_apply', '=', True)], limit=1)
        else:
            resource_id = self.env['resource.calendar'].sudo().search([
                ('default_apply', '=', True)], limit=1)
        return resource_id

    def update_create_old_record(self, attendance_without_checkout,
                                 search_empl, utc_diff, timestamp):
        """
        to update old record if they have not checkout yet
        """
        user_tz = self.env.user.tz or pytz.utc
        local = pytz.timezone(user_tz)
        att_obj = self.env['hr.attendance']
        return_dict = {}
        out_time = attendance_without_checkout.check_in + timedelta(seconds=2)
        next_shift = self._get_shift_name(search_empl,
                                          (timestamp + utc_diff).date())
        shift_id = self.env['resource.calendar'].sudo().search([
            ('code', '=', attendance_without_checkout.shift_name)], limit=1)
        if not shift_id:
            attendance_without_checkout.update({'check_out': (timestamp + utc_diff),
                                                'attendance_remark': 'correct'})
            return_dict.update({'not_create': True})
            return return_dict
        if shift_id.is_cross_shift:
            attendance_without_checkout.update({'check_out': (timestamp + utc_diff),
                                                'attendance_remark': 'correct'})
            return_dict.update({'not_create': True})
            return return_dict
        if shift_id.is_night:
            attendance_without_checkout.update({'check_out': (timestamp + utc_diff),
                                                'attendance_remark': 'correct'})
            return_dict.update({'not_create': True})
            return return_dict
        if attendance_without_checkout.shift_name and next_shift:
            if next_shift.code != attendance_without_checkout.shift_name:
                shift_st = '{0:02.0f}:{1:02.0f}'.format(*divmod(shift_id.start_time * 60, 60))
                shift_end = '{0:02.0f}:{1:02.0f}'.format(*divmod(shift_id.end_time * 60, 60))
                FMT = '%H:%M'
                tdelta = datetime.strptime(str(shift_end), FMT) - datetime.strptime(str(shift_st), FMT)
                shift_hrs = abs(tdelta.seconds / 3600)
                checkout_checkin_hrs = (timestamp + utc_diff) - attendance_without_checkout.check_in
                if (shift_hrs + 1) <= checkout_checkin_hrs.seconds / 3600:
                    modf = math.modf(shift_hrs)
                    different_time = '{0:02.0f}:{1:02.0f}'.format(*divmod(modf[0] * 60, 60))
                    update_checkout = (attendance_without_checkout.check_in) + timedelta(hours=int(modf[1]),
                                                                                         minutes=int(
                                                                                             different_time[-2:]))
                    attendance_without_checkout.update({'check_out': update_checkout,
                                                        'attendance_remark': 'correct'})
                    new_checking_att_id = att_obj.create({
                        'employee_id': search_empl.id,
                        'check_in': (update_checkout) + timedelta(minutes=1),
                        'check_out': timestamp + utc_diff,
                        'type': 'checkin',
                        'attendance_remark': 'correct'
                    })
                    new_checking_att_id.check_assign_shift()
                    date_attendance = datetime.strftime(
                        pytz.utc.localize(datetime.strptime(str(new_checking_att_id.check_in),
                                                            DEFAULT_SERVER_DATETIME_FORMAT)
                                          ).astimezone(local), "%Y-%m-%d %H:%M:%S")
                    new_checking_att_id.checkin_date = date_attendance
                    return_dict.update({'not_create': True})
                    return return_dict
                else:
                    # if employee checkout and checkin diff from shift working hrs * 1.2
                    # then its means some not correct entry in this case need to update
                    # with add some second
                    attendance_without_checkout.update({'check_out': out_time,
                                                        'attendance_remark': 'checkout'})
                    return_dict.update({'vew_create': True})
                    return return_dict
            else:
                if out_time > attendance_without_checkout.check_in:
                    attendance_without_checkout.update({'check_out': out_time,
                                                        'attendance_remark': 'checkout'})
                    return_dict.update({'vew_create': True})
                    return return_dict
        else:
            if out_time > attendance_without_checkout.check_in:
                attendance_without_checkout.update({'check_out': out_time,
                                                    'attendance_remark': 'checkout'})
                return_dict.update({'vew_create': True})
                return return_dict

    def download_attendance(self):
        # override for create attendance without check checkin and checkout
        """
        Function to fetch get the machine attendance and update into the system and also delete from the machine
        :return:
        """
        att_obj = self.env['hr.attendance']
        emp_obj = self.env['hr.employee']
        utc_diff = timedelta(hours=self.utc_hour, minutes=self.utc_minute)
        zk = ZK(str(self.name), port=self.port_no, timeout=10, password=0, force_udp=False, ommit_ping=False)
        conn = zk.connect()
        zk.enable_device()
        return_dict = {}
        conn.set_time(datetime.today() - utc_diff)
        if conn:
            att = conn.get_attendance()
            if att:
                for at in att:
                    search_empl = emp_obj.search([('barcode', '=', str(at.user_id)),
                                                  ('active', '=', True)])
                    if search_empl:
                        user_tz = self.env.user.tz or pytz.utc
                        local = pytz.timezone(user_tz)
                        current_dt = fields.Date.context_today(self)
                        attendance_without_checkout = self.env['hr.attendance'].search([('employee_id', '=', search_empl.id),
                                                                                        ('date_today', '<', current_dt),
                                                                                        ('check_in', '!=', False),
                                                                                        ('check_out', '=', False)
                                                                                        ])
                        if attendance_without_checkout:
                            return_data = self.update_create_old_record(attendance_without_checkout=attendance_without_checkout,
                                                          search_empl=search_empl, utc_diff=utc_diff, timestamp=at.timestamp)
                            if return_data.get('not_create') and return_data.get('not_create') == True:
                                continue
                        attendance_id = self.env['hr.attendance'].search([('employee_id', '=', search_empl.id),
                                                                            ('date_today', '=', current_dt),
                                                                            ('check_in', '!=', False),
                                                                            ], order='id desc', limit=1)
                        if attendance_id:
                            attendance_id.update({'check_out': (at.timestamp + utc_diff),
                                                  'attendance_remark': 'checkin'})
                        else:
                            try:
                                checking_att_id = att_obj.create({
                                    'employee_id': search_empl.id,
                                    'check_in': (at.timestamp + utc_diff),
                                    'type': 'checkin',
                                    'attendance_remark': 'correct'
                                })
                                if checking_att_id:
                                    date_attendance = datetime.strftime(
                                        pytz.utc.localize(datetime.strptime(str(checking_att_id.check_in),
                                                                            DEFAULT_SERVER_DATETIME_FORMAT)
                                                          ).astimezone(local), "%Y-%m-%d %H:%M:%S")
                                    checking_att_id.checkin_date = date_attendance
                                    checking_att_id.check_assign_shift()
                            except Exception:
                                pass
            conn.clear_attendance()
