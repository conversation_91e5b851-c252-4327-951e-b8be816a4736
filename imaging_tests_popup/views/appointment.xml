<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="nuro_appointment_form_view_sheet_doctor_panel_view_imaging_add" model="ir.ui.view">
            <field name="name">Appointment</field>
            <field name="model">nuro.appointment</field>
            <field name="inherit_id" ref="nuro_imaging_appointment.appointment_doctor_form_inherit"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='imaging_button_group']" position="inside">
                    <button string="Add Imaging" id="oe_btn_imaging_tests" class="btn-primary"
                            attrs="{'invisible': [('state', 'in', ('draft', 'completed', 'cancelled', 'scheduled'))]}"/>
                </xpath>
            </field>
        </record>

        <!-- Inherit Form View to Modify it -->
        <record id="nuro_department_form_view_imaging_popup_sequence" model="ir.ui.view">
            <field name="name">Imaging Department</field>
            <field name="model">nuro.imaging.department</field>
            <field name="inherit_id" ref="nuro_imaging.nuro_department_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='authorized']" position="before">
                    <field name="sequence"/>
                </xpath>

            </field>
        </record>

        <!-- Inherit Form View to Add Sequence Field to Imaging Test Type -->
        <record id="nuro_imaging_test_type_form_view_sequence" model="ir.ui.view">
            <field name="name">Imaging Test Type Sequence</field>
            <field name="model">nuro.imaging.test.type</field>
            <field name="inherit_id" ref="nuro_imaging.imaging_test_type_form_view"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='code']" position="after">
                    <field name="sequence"/>
                </xpath>

            </field>
        </record>

    </data>
</odoo>