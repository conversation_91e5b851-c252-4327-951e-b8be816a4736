# -*- coding: utf-8 -*-
# Copyright  Nurosolution Pvt Ltd
{
    'name': 'Blood Bank Management',
    'category': 'Custom',
    'version': '13.0.1',
    'summary': 'Nuro Blood Bank Management',
    'description': """
        This Module allow to keep track of blood available in hospital and also blood donation process
    """,
    'author': 'Nurosolution Pvt Ltd',
    'website': 'http://www.nurosolution.com',
    'company': 'Nurosolution Pvt Ltd',
    'license': 'OPL-1',
    'depends': ['base', 'mail', 'nuro_address_master', 'nuro_labtest'],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',

        'data/cross_mathing_data.xml',
        'data/blood_groups_data.xml',
        'data/location_data.xml',
        'data/sequence_view.xml',

        'report/bg_label.xml',
        'report/bt_form.xml',
        'report/report_action.xml',

        'views/failed_reason_master.xml',
        'views/questionnaire.xml',
        'views/blood_transfusion_instruction.xml',
        'views/cross_math_method.xml',
        'views/blood_collection_bag.xml',
        'views/blood_group_view.xml',
        'views/nuro_patient.xml',
        'views/blood_location_view.xml',
        'views/blood_donor_view.xml',
        'views/blood_donation_view.xml',
        'views/blood_request.xml',
        'views/blood_group_move_view.xml',

        'wizard/print_label_wizard.xml',
        'wizard/cross_matching.xml',
        'wizard/donor_move_wizard.xml',
        'wizard/update_donation_qty.xml',
        'wizard/donation_screening_result.xml',
        'wizard/select_donor_wizard.xml',
        'wizard/donation_failed.xml',

        'menu/menu.xml',
    ],

    'installable': True,
    'application': True,
}
