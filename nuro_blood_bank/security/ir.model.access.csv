id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_blood_group_user,blood.group.user,nuro_blood_bank.model_blood_group,nuro_blood_bank.blood_bank_user,1,0,0,0
access_blood_group_manager,blood.group.manager,nuro_blood_bank.model_blood_group,nuro_blood_bank.blood_bank_manager,1,1,0,0

access_blood_donor_user,blood.donor.user,nuro_blood_bank.model_blood_donor,nuro_blood_bank.blood_bank_user,1,1,1,1
access_blood_donor_manager,blood.donor.manager,nuro_blood_bank.model_blood_donor,nuro_blood_bank.blood_bank_manager,1,1,1,1

access_blood_location_user,blood.location.user,nuro_blood_bank.model_blood_location,nuro_blood_bank.blood_bank_user,1,0,0,0
access_blood_location_manager,blood.location.manager,nuro_blood_bank.model_blood_location,nuro_blood_bank.blood_bank_manager,1,1,1,1

access_blood_donation_user,blood.donation.user,nuro_blood_bank.model_blood_donation,nuro_blood_bank.blood_bank_user,1,1,1,1
access_blood_donation_manager,blood.donation.manager,nuro_blood_bank.model_blood_donation,nuro_blood_bank.blood_bank_manager,1,1,1,1

access_blood_request_user_internal,blood.donation.user,nuro_blood_bank.model_blood_request,base.group_user,1,1,1,0
access_blood_request_user,blood.donation.user,nuro_blood_bank.model_blood_request,nuro_blood_bank.blood_bank_user,1,1,1,1
access_blood_request_manager,blood.donation.manager,nuro_blood_bank.model_blood_request,nuro_blood_bank.blood_bank_manager,1,1,1,1

access_blood_group_move_user,blood.group.move.user,nuro_blood_bank.model_blood_group_move,nuro_blood_bank.blood_bank_user,1,1,1,0
access_blood_group_move_manager,blood.group.move.manager,nuro_blood_bank.model_blood_group_move,nuro_blood_bank.blood_bank_manager,1,1,1,1
access_donor_request_line,donor.request.line,nuro_blood_bank.model_donor_request_line,base.group_user,1,1,1,1
access_blood_collection_bag,blood.collection.bag,nuro_blood_bank.model_blood_collection_bag,base.group_user,1,1,1,0
access_blood_transfusion_instruction,blood.transfusion.instruction,nuro_blood_bank.model_blood_transfusion_instruction,base.group_user,1,1,1,1
access_questionnaire_master,questionnaire.master,nuro_blood_bank.model_questionnaire_master,base.group_user,1,1,1,1
access_questionnaire_selection,questionnaire.selection,nuro_blood_bank.model_questionnaire_selection,base.group_user,1,1,1,1
access_question_relation_lines,question.relation.lines,nuro_blood_bank.model_question_relation_lines,base.group_user,1,1,1,1
access_cross_matching_method,cross.matching.method,nuro_blood_bank.model_cross_matching_method,base.group_user,1,1,1,1
access_failed_reason_master,failed.reason.master,nuro_blood_bank.model_failed_reason_master,base.group_user,1,1,1,1
