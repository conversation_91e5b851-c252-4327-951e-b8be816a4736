# -*- coding: utf-8 -*-
# Part of nurosolution Pvt Ltd.
from odoo.exceptions import UserError

from odoo import models, fields, api, _

STATUS = [
    ('draft', 'Draft'),
    ('waiting', 'Waiting Availability'),
    ('ready', 'Ready'),
    ('reserved', 'Reserved'),
    ('done', 'Done'),
    ('cancel', 'Cancel'),
]


class BloodGroupMove(models.Model):
    _name = 'blood.group.move'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    name = fields.Char(string='Ref', index=True, copy=False, readonly=True,
                       default='New')
    blood_bag_number = fields.Char(string='Blood Bag Number')
    donor_id = fields.Many2one('blood.donor', string='Donor Name')
    patient_id = fields.Many2one('nuro.patient', string='Patient Name', domain=[('deceased', '!=', True)])
    object_name = fields.Char(string='Object Name')
    object_id = fields.Integer(string='Object ID')
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user)
    blood_group_id = fields.Many2one('blood.group', string='Blood Group', index=True)
    type = fields.Selection([('IN', 'RECEIVE'), ('OUT', 'ISSUE'), ('INT', 'Internal')], required=True,
                            tracking=True, readonly=True, states={'draft': [('readonly', False)]})
    location_id = fields.Many2one('blood.location', string='Source Location', required=True,
                                  tracking=True, readonly=True, states={'draft': [('readonly', False)]})
    dest_location_id = fields.Many2one('blood.location', string='Destination Location', required=True,
                                       tracking=True, readonly=True,
                                       states={'draft': [('readonly', False)]})
    quantity = fields.Integer(string='No. Of Units', required=True,
                              tracking=True, readonly=True, states={'draft': [('readonly', False)]})
    state = fields.Selection(STATUS, string='State', copy=False, default='draft',
                             tracking=True, readonly=True, states={'draft': [('readonly', False)]})
    expiry_date = fields.Date('Expiry Date', copy=False, tracking=True,
                              readonly=True, states={'draft': [('readonly', False)]})
    requested_by = fields.Many2one('res.users', string='Requested By', default=lambda self: self.env.user)
    done_by = fields.Many2one('res.users', string='Done By', readonly=1)
    done_date = fields.Date(string='Date', default=fields.Date.context_today)
    request_date = fields.Date(string='Request Date', default=fields.Date.context_today)
    blood_collection_bag_id = fields.Many2one('blood.collection.bag', string='Blood Collection Bag')
    in_br_id = fields.Many2one('blood.request', string='Blood Request')
    out_br_id = fields.Many2one('blood.request', string='Blood Request')
    parent_id = fields.Many2one('blood.group.move', string='Parent ID')
    child_ids = fields.One2many('blood.group.move', 'parent_id', string='Child Lines', domain=[('type', '=', 'OUT')])
    used_qty = fields.Integer('Used Qty', compute='get_available_used_qty', store=True)
    available_qty = fields.Integer('Available', compute='get_available_used_qty', store=True)
    visible_qty = fields.Integer('Unit', compute='get_available_used_qty', store=True)

    def print_receipt(self):
        """
        Vaccine Receipt printing
        :return:
        """
        return self.env.ref('nuro_blood_bank.action_bg_move_label_printing').report_action(self)

    def update_blood_bank_move(self):
        """
        Update Blood Bank Move
        """
        current_date = fields.Date.context_today(self)
        get_expired_move_ids = self.search([('expiry_date', '<', current_date)])
        for line in get_expired_move_ids:
            line.get_available_used_qty()

    @api.depends('used_qty', 'expiry_date')
    def get_available_used_qty(self):
        """
        Get Available Qty
        """
        for rec in self:
            rec.used_qty = sum(rec.child_ids.mapped('quantity'))
            current_date = fields.Date.context_today(self)
            if rec.expiry_date and rec.expiry_date < current_date:
                rec.available_qty = 0
                rec.visible_qty = 0
            elif rec.type != 'IN':
                rec.available_qty = 0
                rec.visible_qty = -1 * rec.quantity
            else:
                rec.available_qty = rec.quantity - rec.used_qty
                rec.visible_qty = rec.quantity

    @api.onchange('type')
    def onchange_blood_move_type(self):
        """
        Onchange Blood Bank Move
        :return:
        """
        domain_from = []
        domain_to = []
        if self.type == 'IN':
            self.location_id = False
            self.dest_location_id = False
            domain_from.append(('type', '=', 'supply'))
            domain_to.append(('type', '=', 'internal'))
        if self.type == 'OUT':
            self.location_id = False
            self.dest_location_id = False
            domain_from.append(('type', '=', 'internal'))
            domain_to.append(('type', '=', 'consume'))
        if self.type == 'INT':
            self.location_id = False
            self.dest_location_id = False
            domain_from.append(('type', '=', 'internal'))
            domain_to.append(('type', '=', 'internal'))
        return {'domain': {'location_id': domain_from, 'dest_location_id': domain_to}}

    @api.constrains('location_id', 'dest_location_id')
    def check_location(self):
        for move in self:
            if move.location_id and move.dest_location_id and (move.location_id.id == move.dest_location_id.id):
                raise UserError(_("Source and destination location can not be same !"))

    def action_confirm(self):
        '''
        This moethod change move state in ready
        :return:
        '''
        for blood_move in self:
            sequence_obj = self.env['ir.sequence']
            sequence = sequence_obj.next_by_code('blood.move')
            sequence = blood_move.type + sequence
            blood_move.state = 'ready'
            blood_move.name = sequence
            if blood_move.type == 'IN':
                bag_number = sequence_obj.next_by_code('blood.move.bag')
                blood_move.blood_bag_number = bag_number

    def action_done(self, force_done=False):
        """
        This Method change move state in done
        :return:
        """
        for blood_move in self:
            available_qty = self.blood_group_id.available_qty - self.blood_group_id.reserve_qty
            if (available_qty <= 0 or available_qty < self.quantity) and self.type == 'OUT' and not force_done:
                raise UserError(_('Negative Quantity Is not Allowed.!!!'))
            blood_move.state = 'done'
            blood_move.done_by = self.env.user.id
            blood_move.done_date = fields.Date.context_today(self)

    def action_un_reserved(self):
        """
        Action Do Unreserved
        """
        if self.state != 'reserved':
            raise UserError(_('Record Processed Already.!!!'))
        self.state = 'ready'
