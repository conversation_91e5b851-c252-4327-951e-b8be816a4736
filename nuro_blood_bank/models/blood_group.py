# -*- coding: utf-8 -*-
# Part of nurosolution Pvt Ltd.
from odoo import models, fields


class BloodGroup(models.Model):
    _name = 'blood.group'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name, id'

    name = fields.Char('Name', Copy=False, index=True, required=True)
    company_id = fields.Many2one('res.company', 'Company', required=True,
                                 default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True,
                              default=lambda self: self.env.user)
    active = fields.Boolean('Active', default=True,
                            help="If unchecked, it will allow you to hide the product without removing it.")
    total_qty = fields.Integer('Total Available Units', compute='get_available_bank')
    available_qty = fields.Integer('Unreserved Units', compute='get_available_bank')
    reserve_qty = fields.Integer('Reserved Units', compute='get_available_bank')

    def get_available_bank(self):
        '''
        This method return available unit of blood group
        :return:
        '''
        blood_move = self.env['blood.group.move']
        for blood_group in self:
            in_avl_total_dict = blood_move.read_group([
                ('blood_group_id', '=', blood_group.id),
                ('state', '=', 'done'),
                ('type', '=', 'IN')
            ], fields=['available_qty'], groupby=['blood_group_id'])

            in_rsrv_total_dict = blood_move.read_group([
                ('blood_group_id', '=', blood_group.id),
                ('state', '=', 'reserved'),
                ('type', '=', 'IN')
            ], fields=['available_qty'], groupby=['blood_group_id'])

            in_rsrv_total_avlbl = blood_move.read_group([
                ('blood_group_id', '=', blood_group.id),
                ('state', 'in', ('done', 'reserved')),
                ('type', '=', 'IN')
            ], fields=['available_qty'], groupby=['blood_group_id'])

            in_total_avlbl_qty = in_rsrv_total_avlbl and in_rsrv_total_avlbl[0].get('available_qty') or 0
            blood_group.total_qty = in_total_avlbl_qty
            in_qty = in_avl_total_dict and in_avl_total_dict[0].get('available_qty') or 0
            blood_group.available_qty = in_qty
            in_rsrv_qty = in_rsrv_total_dict and in_rsrv_total_dict[0].get('available_qty') or 0
            blood_group.reserve_qty = in_rsrv_qty

    def get_traceability(self):
        """
        Get Traceability
        """
        action = self.env.ref('nuro_blood_bank.action_main_nuro_blood_group_move').read()[0]
        action['domain'] = [('blood_group_id', '=', self.id)]
        action['context'] = {'create': False, 'delete': False, 'edit': False, 'search_default_blood_group_group_by': 1,
                             'search_default_type_name': 1}
        return action
