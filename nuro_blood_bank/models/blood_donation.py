# -*- coding: utf-8 -*-
# Part of nurosolution Pvt Ltd.
from dateutil.relativedelta import relativedelta
from odoo.exceptions import UserError

from odoo import models, fields, api, _

STATE = [
    ('draft', 'Draft'),
    ('in_process', 'In Process'),
    ('assessment_done', 'Assessment Done'),
    ('done', 'Done'),
    ('underweight', 'Underweight'),
    ('questionnaire', 'Questionnaire'),
    ('labtest', 'Labtest'),
    ('labtest_requested', 'Labtest Requested'),
    ('screening_completed', 'Screening Completed'),
    ('ineligible', 'Ineligible'),
    ('ready', 'Ready'),
    ('donated', 'Donated'),
    ('reject', 'Reject'),
    ('failed', 'Failed'),
]


class BloodDonation(models.Model):
    _name = 'blood.donation'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    GENDER = [
        ('male', 'Male'),
        ('female', 'Female'),
    ]

    SCREENING = [('negative', 'Negative'), ('positive', 'Positive')]

    name = fields.Char('Ref', default='New')
    company_id = fields.Many2one('res.company', 'Company', required=True, default=lambda self: self.env.company)
    user_id = fields.Many2one('res.users', 'User', required=True, default=lambda self: self.env.user)
    doctor_id = fields.Many2one("nuro.doctor", string="Doctor")
    patient_id = fields.Many2one('nuro.patient', string='Patient', domain=[('deceased', '!=', True)])
    identification_code = fields.Char(string='ID#')
    gender = fields.Selection(GENDER, string='Gender', store=True)
    age = fields.Char('Patient Age')
    mobile = fields.Char('Mobile')
    donor_id = fields.Many2one('blood.donor', 'Donor', required=True)
    donor_age = fields.Integer(string='Age', related="donor_id.age", store=True)
    donor_gender = fields.Selection(GENDER, string='Gender', related="donor_id.gender", store=True)
    contact_number = fields.Char(string='Contact Number', related="donor_id.contact_number", store=True)
    address = fields.Char(string='Address', copy=False, related="donor_id.address", store=True)
    area_id = fields.Many2one('res.area', string='City', related="donor_id.area_id", store=True)
    district_id = fields.Many2one('res.district', string='District', related="donor_id.district_id", store=True)
    region_id = fields.Many2one('res.region', string='Region', related="donor_id.region_id", store=True)
    state_id = fields.Many2one('res.country.state', string='State', related="donor_id.state_id", store=True)
    zip = fields.Char(string='Zip', related="donor_id.zip", store=True)
    country_id = fields.Many2one('res.country', string='Country', related="donor_id.country_id", store=True)
    blood_group_id = fields.Many2one('blood.group', 'Blood Group', related="donor_id.blood_group_id", store=True)
    state = fields.Selection(STATE, string='Status', default='draft', readonly=True)
    blood_location_id = fields.Many2one('blood.location', string='Location', required=1, readonly=True,
                                        states={'draft': [('readonly', False)]},
                                        default=lambda self: self.env.ref('nuro_blood_bank.blood_location_internal'))
    donation_qty = fields.Integer('No. Of Units')
    date = fields.Date(string='Donation Date', readonly=True, states={'draft': [('readonly', False)]},
                       default=fields.Datetime.now)
    expiry_date = fields.Date(string='Expiry Date')
    bg_move_id = fields.Many2one('blood.group.move', 'Move Id')
    bg_in_move_ids = fields.Many2many('blood.group.move', 'bg_move_donation_rel', string='BG Move')
    blood_collection_bag_id = fields.Many2one('blood.collection.bag', string='Blood Bag')
    donor_request_id = fields.Many2one('donor.request.line', string="Donor Request ID")
    weight = fields.Float('Weight (Kg)', default=50.0, required=True)
    question_relation_lines = fields.One2many('question.relation.lines', 'donation_id', string='Questionnaire')
    assessment_relation_lines = fields.One2many('question.relation.lines', 'assessment_donation_id',
                                                string='Health Assessment')
    bt_product_ids = fields.Many2many('product.product', string="Blood Product")
    labtest_master_ids = fields.Many2many('nuro.labtest.master', string="Labtest Master", readonly=True)
    labtest_id = fields.Many2one('nuro.medical.labtest.result', 'Labtest ID')
    labtest_result = fields.Selection(SCREENING, string='Screening Result')
    result_details = fields.Selection([
        ('positive', 'Positive'),
        ('negative', 'Negative')], string="Result")
    sr_update_reason = fields.Char("Reason to Update")
    failed_reason_id = fields.Many2one('failed.reason.master', string='Failed Reason')
    remark_failed = fields.Char(string='Failed Remark')

    @api.model
    def create(self, vals):
        """Create Method Vals"""
        res = super().create(vals)
        for rec in res:
            sequence = self.env['ir.sequence'].next_by_code('blood.donation')
            rec.name = sequence
        return res

    def failed_donation_request_view(self):
        """Failed Donation Request"""
        if self.state != 'ready':
            raise UserError(_('Record has been processed already.!!!'))
        action = self.env.ref('nuro_blood_bank.action_update_donation_failed_remark').read()[0]
        action['context'] = {'default_donation_id': self.id}
        return action

    def change_donation_failed_status(self):
        """Failed Status Request Donation"""
        if self.state != 'failed':
            raise UserError(_('Record has been processed already.!!!'))
        self.state = 'ready'


    def update_donation_request_view(self):
        """Update Donation Request View"""
        if self.state != 'ready':
            raise UserError(_('Record has been processed already.!!'))
        action = self.env.ref('nuro_blood_bank.action_blood_donation_update_wizard').read()[0]
        action['context'] = {'default_donation_id': self.id}
        return action

    def update_donation_screening_result(self):
        """Update Donation Request View"""
        if self.state not in ('screening_completed', 'ready', 'ineligible'):
            raise UserError(_('Record has been processed already.!!'))
        action = self.env.ref('nuro_blood_bank.action_update_donation_screening_result').read()[0]
        ctx = {'default_donation_id': self.id}
        if self.state in ('ready', 'ineligible'):
            ctx.update({
                "default_result_details": self.result_details,
                "default_update": True
            })
        action['context'] = ctx
        return action

    @api.onchange('bt_product_ids')
    def onchange_bt_product_ids(self):
        """Onchange Blood Product IDS"""
        if self.bt_product_ids:
            labtest_ids = set(self.bt_product_ids.mapped('labtest_line').mapped('labtest_master_id').ids)
            self.labtest_master_ids = [(6, 0, labtest_ids)]

    def create_parent_labtest_result_vals(self):
        """
        Def Create parent Labtest Vals
        :return:
        """
        sequence = self.env['ir.sequence'].next_by_code('nuro.labtest.sequence')
        patient_id = self.donor_id.patient_id
        recipient_patient_id = self.patient_id and self.patient_id.id or False
        val = {
            'name': sequence,
            'patient_id': patient_id.id,
            'identification_code': patient_id.identification_code,
            'age': patient_id.age,
            'mobile': patient_id.mobile,
            'gender': patient_id.gender,
            'date': fields.Datetime.now(),
            'state': 'request',
            'branch_id': 1,
            'sample_collection_state': 'request',
            'blood_donation_id': self.id,
            'blood_donor_id': self.donor_id and self.donor_id.id or False,
            'blood_group_id': self.donor_id.blood_group_id and self.donor_id.blood_group_id.id or False,
            'recipient_patient_id': recipient_patient_id,
            'recipient_identification_code': recipient_patient_id and recipient_patient_id.identification_code or False,
            'recipient_age': recipient_patient_id and recipient_patient_id.age or False,
            'recipient_mobile': recipient_patient_id and recipient_patient_id.mobile or False,
            'recipient_gender': recipient_patient_id and recipient_patient_id.gender or False,
        }
        return val

    def create_child_labtest_result_vals(self, labtest_id, line):
        """
        Child labtest Result
        :return:
        """
        val = {
            'name': labtest_id.name,
            'labtest_master_id': line.id,
            'department_id': line.department_id.id,
            'sample_parent_id': labtest_id.id,
            'patient_id': labtest_id.patient_id.id,
            'identification_code': labtest_id.patient_id.identification_code,
            'age': labtest_id.patient_id.age,
            'mobile': labtest_id.patient_id.mobile,
            'gender': labtest_id.patient_id.gender,
            'state': 'request',
            'branch_id': 1,
            'sample_collection_state': 'request',
            'blood_donation_id': self.id,
            'blood_donor_id': labtest_id.blood_donor_id and labtest_id.blood_donor_id.id or False,
            'blood_group_id': labtest_id.blood_group_id and labtest_id.blood_group_id.id or False,
            'recipient_patient_id': labtest_id.recipient_patient_id and labtest_id.recipient_patient_id.id or False,
            'recipient_identification_code': labtest_id.recipient_patient_id and labtest_id.recipient_identification_code or False,
            'recipient_age': labtest_id.recipient_patient_id and labtest_id.recipient_age or False,
            'recipient_mobile': labtest_id.recipient_patient_id and labtest_id.recipient_mobile or False,
            'recipient_gender': labtest_id.recipient_patient_id and labtest_id.recipient_gender or False
        }
        return val

    def create_labtest_request(self):
        """Create Labtest Request"""
        if not self.donor_id.patient_id:
            self.donor_id.create_patient_record()
        if not self.labtest_id:
            parent_vals = self.create_parent_labtest_result_vals()
            labtest_parent_id = self.env['nuro.medical.labtest.result'].create(parent_vals)
            master_ids = set(self.bt_product_ids.mapped('labtest_line').mapped('labtest_master_id').ids)
            if not master_ids:
                raise UserError(_('There no Labtest configured in the BT Type.!!!'))
            labtest_master_ids = self.env['nuro.labtest.master'].browse(master_ids)
            for line in labtest_master_ids:
                vals_line = self.create_child_labtest_result_vals(labtest_id=labtest_parent_id, line=line)
                self.env['nuro.medical.labtest.result'].create(vals_line)
            self.labtest_id = labtest_parent_id.id
            self.state = 'labtest_requested'
        else:
            raise UserError(_('Labtest Request Has been created Already.!!!'))

    def update_fitness_fit(self):
        """Update Fitness Evaluation"""
        if self.state != 'questionnaire':
            raise UserError(_('Record has been processed already.!!!'))
        self.state = 'labtest'

    def update_fitness_unfit(self):
        """Update Fitness Evaluation"""
        if self.state != 'questionnaire':
            raise UserError(_('Record has been processed already.!!!'))
        self.state = 'ineligible'

    def update_fitness_state_change(self):
        """Update Fitness State Change"""
        if self.state != 'ineligible':
            raise UserError(_('Record has been processed already.!!!'))
        self.state = 'questionnaire'

    def create_options_lines(self):
        """Create Options Lines"""
        questionnaire_obj = self.env['question.relation.lines']
        get_question_line = self.env['questionnaire.master'].search([('type', '=', 'questionnaire')])
        if get_question_line:
            for line in get_question_line:
                if line.specific_to == 'both':
                    questionnaire_obj.create({
                        'question_master_id': line.id,
                        'donation_id': self.id
                    })
                if line.specific_to == 'gender_specific' and line.gender == self.donor_id.gender:
                    questionnaire_obj.create({
                        'question_master_id': line.id,
                        'donation_id': self.id
                    })

    def create_options_lines_assessment(self):
        """Create Options Lines"""
        questionnaire_obj = self.env['question.relation.lines']
        get_question_line = self.env['questionnaire.master'].search([('type', '=', 'health_assessment')])
        if get_question_line:
            for line in get_question_line:
                if line.specific_to == 'both':
                    questionnaire_obj.create({
                        'question_master_id': line.id,
                        'assessment_donation_id': self.id
                    })
                if line.specific_to == 'gender_specific' and line.gender == self.donor_id.gender:
                    questionnaire_obj.create({
                        'question_master_id': line.id,
                        'assessment_donation_id': self.id
                    })

    def update_start_questionnaire(self):
        """Update Start Questionnaire"""
        if self.weight <= 49.9:
            raise UserError(_("Weight is not valid, It can not be less than 50!!!"))
        if self.state != "draft":
            raise UserError(_("Donation has been processed already!!!"))
        if not self.question_relation_lines:
            self.create_options_lines()
        if not self.assessment_relation_lines:
            self.create_options_lines_assessment()
        self.state = "questionnaire"

    def reject_donation_request(self):
        """Reject Donation Request"""
        if self.state != "draft":
            raise UserError(_("Donation has been processed already!!!"))
        self.state = "reject"
        if self.weight < 50.0:
            self.state = 'underweight'

    def create_blood_assessment_line_vals(self):
        """Create Blood Assessment Line Vals"""
        self.donor_id.create_patient_record()
        vals = {
            'donor_id': self.donor_id.id,
            'blood_group_id': self.blood_group_id.id,
            'patient_id': self.donor_id.patient_id.id,
            'identification_code': self.donor_id.patient_id.identification_code,
            'gender': self.donor_id.patient_id.gender,
            'age': self.donor_id.patient_id.age,
            'mobile': self.donor_id.patient_id.mobile,
            'blood_donation_id': self.id,
            'br_blood_group_id': self.blood_group_id.id
        }
        return vals

    def create_blood_assessment(self):
        """Create Blood assessment"""
        object_donor_assessment = self.env['donor.request.line']
        if not self.donor_request_id:
            vals = self.create_blood_assessment_line_vals()
            donor_id = object_donor_assessment.create(vals)
            self.donor_request_id = donor_id.id
        view_id = self.env.ref('nuro_blood_bank.blood_request_donor_assessment_form_donation_form').id
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'donor.request.line',
            'view_mode': 'form',
            'res_id': self.donor_request_id.id,
            'views': [[view_id, 'form']],
        }

    @api.onchange('blood_collection_bag_id')
    def onchange_update_expiry_date(self):
        """Update Expiry Date"""
        if self.blood_collection_bag_id:
            current_date = fields.Date.context_today(self)
            self.expiry_date = current_date + relativedelta(days=self.blood_collection_bag_id.expire_after)
        else:
            self.expiry_date = False

    def _prepare_bg_move_vals(self, qty, location_id, dest_location_id):
        """blood group move create vals dictionary"""
        return dict(blood_group_id=self.blood_group_id.id,
                    blood_collection_bag_id=self.blood_collection_bag_id and self.blood_collection_bag_id.id or False,
                    type='IN',
                    location_id=location_id.id,
                    dest_location_id=dest_location_id.id,
                    quantity=qty,
                    state='draft',
                    expiry_date=self.expiry_date,
                    )

    def create_brood_group_move(self, reserve):
        """This method create blood group move entry"""
        # self.donor_id.create_patient_record()
        if self.donation_qty <= 0:
            raise UserError(_('You can not create Blood Donation without Zero Quantity.!!!'))
        bg_move_env = self.env['blood.group.move']
        location_id = self.env.ref('nuro_blood_bank.blood_location_supply')
        if not location_id:
            raise UserError(_('There is not any location define for blood supply. Please create blood supply location'))
        move_ids = []
        for line in range(0, self.donation_qty):
            bg_move_vals = self._prepare_bg_move_vals(qty=1, location_id=location_id,
                                                      dest_location_id=self.blood_location_id)
            bg_move_vals.update({
                'donor_id': self.donor_id.id,
                'patient_id': self.patient_id and self.patient_id.id,
            })
            bg_move_id = bg_move_env.create(bg_move_vals)
            move_ids.append(bg_move_id.id)
        return move_ids

    def action_confirm(self):
        """This method update status in process"""
        if not self.donor_request_id:
            raise UserError(_('Please Create Donor Assessment form before processing further.!!!'))
        if self.donor_request_id and self.donor_request_id.assessment_result != 'fit':
            raise UserError(_('Donation Can not be done as donor is not Fit for donation.!!!'))
        if self.donor_request_id and not self.donor_request_id.labtest_id:
            raise UserError(_('Please Create Labtest from Assessment Form before processing further.!!!'))
        if self.donation_qty <= 0:
            raise UserError(_('You can not create Blood Donation without Zero Quantity.!!!'))
        for donation in self:
            if not donation.date:
                raise UserError(_('Please Fill The Donation Date !'))
            sequence = self.env['ir.sequence'].next_by_code('blood.donation')
            donation.name = sequence
            donation.state = 'in_process'

    def update_expiry_date(self):
        """Update Expiry Date"""
        if self.blood_collection_bag_id:
            current_date = fields.Date.context_today(self)
            self.expiry_date = current_date + relativedelta(days=self.blood_collection_bag_id.expire_after)

    def action_done(self, reserve=False):
        """This method to done donation"""
        blood_group_move = self.env['blood.group.move']
        for donation in self:
            donation.update_expiry_date()
            if not donation.expiry_date:
                raise UserError(_('Please Fill The Expiry Date !'))
            if donation.donation_qty <= 0:
                raise UserError(_('Donation unit should be greater than 0.0 !'))
            move_ids = donation.create_brood_group_move(reserve=reserve)
            for rec in move_ids:
                move_id = blood_group_move.browse(rec)
                if not move_id.parent_id:
                    move_id.action_confirm()
                    move_id.action_done()
                    donation.state = 'done'
                    if reserve:
                        move_id.state = 'reserved'
            donation.bg_in_move_ids = [(6, 0, move_ids)]

    def print_labtest_result(self):
        """Print Labtest Result"""
        labtest_ids = self.labtest_id.child_lines.filtered(
            lambda x: not x.labtest_master_id.do_not_print and x.state == 'completed')
        department_ids = labtest_ids.mapped('department_id').filtered(lambda x: not x.do_not_print)
        vals = {
            "labtest_id": self.labtest_id.id,
            "department_ids": [(6, 0, department_ids.ids)],
            "lab_department_ids": [(6, 0, department_ids.ids)]
        }
        ctx = {
            'active_id': self.labtest_id.id,
            'active_model': self.labtest_id._name
        }
        lab_wiz_id = self.env['hms.lab.print.report'].with_context(ctx).create(vals)
        lab_wiz_id.lab_department_ids = [(6, 0, lab_wiz_id.department_ids.ids)]
        return lab_wiz_id.print_lab()


class NuroMedicalLabtestResult(models.Model):
    _inherit = 'nuro.medical.labtest.result'

    blood_donation_id = fields.Many2one('blood.donation', string='Blood Donation')


