<!--<?xml version="1.0" encoding="UTF-8" ?>-->
<odoo>
    <data>

        <menuitem id="main_menu_blood_bank" name="Blood Bank"
                  web_icon="nuro_blood_bank,static/description/icon.png"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_request" name="Blood Request" parent="main_menu_blood_bank"
                  action="nuro_blood_bank.action_main_nuro_blood_request" sequence="1"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_donation" name="Blood Donation" parent="main_menu_blood_bank"
                  action="nuro_blood_bank.action_main_nuro_blood_donation" sequence="3"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_main_reporting_view" name="Reports" parent="main_menu_blood_bank" sequence="8"/>

        <menuitem id="menu_blood_group_reporting_view" name="Summery Inventory Report" parent="menu_main_reporting_view"
                  action="nuro_blood_bank.action_blood_group_action_view_list" sequence="1"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_moves" name="Detail Inventory Report" parent="menu_main_reporting_view"
                  action="nuro_blood_bank.action_main_nuro_blood_group_move" sequence="2"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_donor" name="Donors" parent="main_menu_blood_bank"
                  action="nuro_blood_bank.action_blood_donor_action_view" sequence="9"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_configuration" name="Configuration" parent="main_menu_blood_bank"
                  sequence="11" groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="blood_transfusion_questionnaire" name="BT Questionnaire" parent="menu_blood_configuration"
                  action="nuro_blood_bank.action_questionnaire_master_view" sequence="-2"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="blood_transfusion_questionnaire_selection" name="Questionnaire Selection"
                  parent="menu_blood_configuration"
                  action="nuro_blood_bank.action_questionnaire_selection_view" sequence="-1"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="blood_transfusion_process_instruction" name="BT Instruction" parent="menu_blood_configuration"
                  action="nuro_blood_bank.action_blood_transfusion_instruction_view" sequence="0"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_group" name="Blood Groups" parent="menu_blood_configuration"
                  action="nuro_blood_bank.action_blood_group_action_view" sequence="1"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_location" name="Blood Location" parent="menu_blood_configuration"
                  action="nuro_blood_bank.action_blood_location_action_view" sequence="2"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_collection_bag" name="Blood Collection Bag" parent="menu_blood_configuration"
                  action="nuro_blood_bank.action_blood_collection_bag" sequence="3"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <menuitem id="menu_blood_collection_cross_mathing_method" name="Cross Mathing" parent="menu_blood_configuration"
                  action="nuro_blood_bank.view_action_cross_matching_method" sequence="4"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="menu_failed_reason_blood_donation" name="Failed Reason" parent="menu_blood_configuration"
                  action="nuro_blood_bank.failed_reason_master_view_action" sequence="5"
                  groups="nuro_blood_bank.blood_bank_user,nuro_blood_bank.blood_bank_manager"/>


    </data>
</odoo>
