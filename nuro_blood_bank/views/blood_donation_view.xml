<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <!--================Blood Donation Form view====================-->
        <record id="nuro_blood_donation_form_view_sheet" model="ir.ui.view">
            <field name="name">Blood Donation</field>
            <field name="model">blood.donation</field>
            <field name="arch" type="xml">
                <form string="Blood Donation">
                    <header>
                        <field name="weight" invisible="1"/>
                        <field name="labtest_id" invisible="1"/>
                        <button name="reject_donation_request" type="object" class="oe_highlight" string="Reject"
                                attrs="{'invisible': ['|', ('weight', '&gt;', 49.9), ('state', '!=', 'draft')]}"/>
                        <button name="update_start_questionnaire" type="object" class="oe_highlight"
                                string="Start Questionnaire"
                                attrs="{'invisible': ['|', ('weight', '&lt;=', 49.9), ('state', '!=', 'draft')]}"/>
                        <button name="update_fitness_fit" type="object" class="oe_highlight" string="Fit"
                                states="questionnaire"/>
                        <button name="failed_donation_request_view" type="object" class="oe_highlight" string="Fail"
                                states="ready"/>
                        <button name="update_donation_screening_result" type="object" class="oe_highlight"
                                string="Screening Result" states="screening_completed"/>
                        <button name="update_donation_screening_result" type="object" class="oe_highlight"
                                string="Update Screening Result" attrs="{'invisible': ['|', ('state', '!=', 'ineligible'),('labtest_id', '=', False)]}"/>
                        <button name="change_donation_failed_status" type="object" class="oe_highlight"
                                string="Revoke Failed Status" states="failed"/>
                        <button name="update_fitness_unfit" type="object" class="oe_highlight" string="Not Fit"
                                states="questionnaire"/>
                        <button name="update_fitness_state_change" type="object" class="oe_highlight"
                                string="Update Fitness" attrs="{'invisible': ['|', ('state', '!=', 'ineligible'),('labtest_id', '!=', False)]}"/>
                        <button name="create_labtest_request" type="object" class="oe_highlight"
                                string="Request Labtest"
                                attrs="{'invisible': ['|', ('state', '!=', 'labtest'), ('labtest_id', '!=', False)]}"/>
                        <button name="update_donation_request_view" type="object" class="oe_highlight" string="Donate"
                                attrs="{'invisible': [('state', '!=', 'ready')]}"/>
                        <button name="print_labtest_result" type="object" class="oe_highlight" string="Print Labtest"
                                attrs="{'invisible': [('labtest_id', '=', False)]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,donated,reject"/>
                    </header>
                    <sheet string="Blood Donation">
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name" string="Donation #"/>
                                <h1>
                                    <field name="name" class="oe_inline" default_focus="1" readonly="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group string="Donation Info" name="donation_info">
                                <field name="date"/>
                                <field name="donor_id" options="{'no_quick_create': True}"
                                       attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="donor_gender" required="1"/>
                                <field name="donor_age" required="1"/>
                                <field name="contact_number" required="1"/>
                                <label for="" name="address_name">
                                    <b>Address</b>
                                </label>
                                <div class="o_address_format">
                                    <field name="district_id" placeholder="District..." class="o_address_street"
                                           readonly="1" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="area_id" placeholder="Area..." class="o_address_street" readonly="1"/>
                                    <field name="region_id" placeholder="Region" class="o_address_city" readonly="1"
                                           options="{'no_create': True, 'no_open': True}"
                                           domain="[('id', '=', region_id)]"/>
                                    <field name="state_id" class="o_address_state" placeholder="State" invisible="1"
                                           options="{'no_open': True}"
                                           context="{'country_id': country_id, 'zip': zip}"/>
                                    <field name="zip" placeholder="ZIP" class="o_address_zip" readonly="1"/>
                                    <field name="country_id" placeholder="Country" class="o_address_country"
                                           options="{'no_open': True, 'no_create': True}" readonly="1"/>
                                </div>
                                <field name="blood_group_id" options="{'no_create': True, 'no_open': True}"
                                       widget="selection"/>
                            </group>
                            <group>
                                <field name="weight" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="failed_reason_id" options="{'no_create': True, 'no_open': True}"
                                       attrs="{'invisible': [('failed_reason_id', '=', False)]}" readonly="1"/>
                                <field name="remark_failed" attrs="{'invisible': [('remark_failed', '=', False)]}"
                                       readonly="1"/>
                                <field name="bt_product_ids" widget="many2many_tags"
                                       attrs="{'readonly': ['|', ('state', '!=', 'labtest'), ('labtest_id', '!=', False)]}"
                                       options="{'no_open': True, 'no_create': True}"/>
                                <field name="labtest_master_ids" invisible="1" force_save="1" widget="many2many_tags"/>
                                <field name="labtest_result" readonly="1"
                                       attrs="{'invisible': [('labtest_result', '=', False)]}"/>
                                <field name="result_details" readonly="1"
                                       attrs="{'invisible': [('result_details', '=', False)]}"/>
                                <field name="sr_update_reason" readonly="1"
                                       attrs="{'invisible': [('sr_update_reason', '=', False)]}"/>
                                <field name="donation_qty" attrs="{'invisible': [('donation_qty', '&lt;=', 0.0)]}"
                                       readonly="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Details"
                                  attrs="{'invisible': [('state', 'in', ('draft', 'underweight', 'reject'))]}">
                                <separator string="Questionnaire" attrs="{'invisible': [('state', '=', 'new')]}"/>
                                <field name="question_relation_lines"
                                       attrs="{'readonly': [('state', '!=', 'questionnaire')], 'invisible': [('state', 'in', ('draft', 'underweight', 'reject'))]}">
                                    <tree create="false" editable="bottom" delete="false">
                                        <field name="question_master_id" readonly="1"/>
                                        <field name="selection_ids" widget="many2many_tags" readonly="1" invisible="1"/>
                                        <field name="comment_readonly" invisible="1"/>
                                        <field name="response_id" domain="[('id', 'in', selection_ids)]"
                                               options="{'no_open': True, 'no_create': True}"
                                               attrs="{'readonly': [('comment_readonly', '!=', True)]}"/>
                                        <field name="comment" attrs="{'readonly': [('comment_readonly', '=', True)]}"/>
                                    </tree>
                                </field>
                                <field name="assessment_relation_lines"
                                       attrs="{'readonly': [('state', '!=', 'questionnaire')], 'invisible': [('state', 'in', ('draft', 'underweight', 'reject'))]}">
                                    <tree create="false" editable="bottom" delete="false">
                                        <field name="question_master_id" readonly="1"/>
                                        <field name="selection_ids" widget="many2many_tags" readonly="1" invisible="1"/>
                                        <field name="comment_readonly" invisible="1"/>
                                        <field name="response_id" domain="[('id', 'in', selection_ids)]"
                                               options="{'no_open': True, 'no_create': True}"
                                               attrs="{'readonly': [('comment_readonly', '!=', True)]}"/>
                                        <field name="comment" attrs="{'readonly': [('comment_readonly', '=', True)]}"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!--==============Blood Donation Tree view===================-->
        <record id="nuro_blood_donation_tree_view_sheet" model="ir.ui.view">
            <field name="name">Blood Donation</field>
            <field name="model">blood.donation</field>
            <field name="arch" type="xml">
                <tree string="Blood Donation">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="donor_id"/>
                    <field name="blood_group_id"/>
                    <field name="donation_qty"/>
                    <field name="blood_location_id"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <!--=================Blood Donation Search===================-->
        <record id="nuro_blood_donation_search_view" model="ir.ui.view">
            <field name="name">Blood Donation Search</field>
            <field name="model">blood.donation</field>
            <field name="arch" type="xml">
                <search string="Blood Donation Search">
                    <field name="name"/>
                    <field name="donor_id"/>
                    <field name="blood_group_id"/>
                    <field name="blood_location_id"/>
                    <separator/>
                    <filter string="Today's Donation" name="today_donation"
                            domain="[('date', '=', current_date),
                            ('date', '&gt;=', current_date)]" context="{}"/>
                    <separator/>
                    <group expand="0" string="Group By..." colspan="11" col="11">
                        <filter string="Blood Group" name="blood_group_group_by"
                                context="{'group_by':'blood_group_id'}"/>
                        <filter string="Donor" name="donor_group_by" context="{'group_by':'donor_id'}"/>
                        <filter string="Blood Location" name="blood_location_group_by"
                                context="{'group_by':'blood_location_id'}"/>
                        <filter string="State" name="state_group_by" context="{'group_by':'state'}"/>
                        <separator/>
                        <filter string="Date" name="date_group_by" context="{'group_by':'date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--===================action for Blood Donation=======================-->
        <record id="action_main_nuro_blood_donation" model="ir.actions.act_window">
            <field name="name">Blood Donation</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">blood.donation</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'delete': False}</field>
        </record>

    </data>
</odoo>