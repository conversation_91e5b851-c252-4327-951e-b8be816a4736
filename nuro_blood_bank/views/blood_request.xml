<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <!--================Blood Request Form view====================-->
        <record id="nuro_blood_request_form_view_sheet" model="ir.ui.view">
            <field name="name">Blood Request</field>
            <field name="model">blood.request</field>
            <field name="arch" type="xml">
                <form string="Blood Request">
                    <header>
                        <field name="patient_id" invisible="1"/>
                        <field name="qty_donor_number" invisible="1"/>
                        <field name="bdl_validation" invisible="1"/>
                        <button name="create_donor_line_addition_wizard" type="object" class="oe_highlight"
                                string="Add Donor" states="draft"/>
                        <button name="update_cross_matching_result" type="object" class="oe_highlight"
                                string="Cross Match" attrs="{'invisible': [('bdl_validation', '=', False)]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,in_process,done"/>
                    </header>
                    <sheet string="Blood Request">
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name" string="Request #"/>
                                <h1>
                                    <field name="name" class="oe_inline" default_focus="1" readonly="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group string="Request Info" name="request_info">
                                <field name="date" readonly="1" required="1" force_save="1"/>
                                <field name="patient_id" attrs="{'readonly': [('state', '!=', 'draft')]}" required="1"/>
                                <field name="identification_code" readonly="1" force_save="1"/>
                                <field name="gender" readonly="1" force_save="1"/>
                                <field name="blood_group_id" readonly="1" required="1"/>
                                <field name="age" readonly="1" force_save="1"/>
                                <field name="mobile" readonly="1" force_save="1"/>
                            </group>
                            <group>
                                <field name="qty" readonly="1" force_save="1"/>
                                <field name="blood_location_id" domain="[('type', '=', 'internal')]"
                                       options="{'no_create': True, 'no_open': True}" invisible="1"/>
                                <field name="other_hospital" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="other_hospital_id" options="{'no_create': True, 'no_open': True}"
                                       attrs="{'invisible': [('other_hospital', '=', False)], 'required': [('other_hospital', '=', True)]}"/>
                                <field name="doctor_id" options="{'no_create': True, 'no_open': True}"
                                       attrs="{'readonly': [('state', '!=', 'draft')], 'required': [('other_hospital', '=', False)], 'invisible': [('other_hospital', '=', True)]}"
                                       force_save="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Donor Lines" name="donor_line">
                                <field name="donor_request_lines" readonly="1">
                                    <tree editable="bottom">
                                        <field name="cross_match_user_id" invisible="1"/>
                                        <field name="blood_donation_id" invisible="1"/>
                                        <field name="labtest_completed" invisible="1"/>
                                        <field name="donor_id" required="1"/>
                                        <field name="bt_product_id" domain="[('is_bt', '=', True)]"
                                               options="{'no_create': True, 'no_open': True}" invisible="1"/>
                                        <field name="blood_group_id" readonly="1"/>
                                        <field name="cross_match_status" readonly="1" force_save="1"/>
                                        <field name="cross_match" readonly="1" force_save="1"/>
                                        <field name="qty" readonly="1"/>
                                        <field name="issued_qty" readonly="1"/>
                                        <button name="update_cross_matching_result" type="object" class="oe_highlight"
                                                string="Change" attrs="{'invisible': ['|', ('issued_qty', '!=', 0), ('cross_match_user_id', '=', False)]}"/>
                                        <button name="print_receipt_donor_form" type="object" string="BT Form"
                                                class="oe_highlight" attrs="{'invisible': [('cross_match_user_id', '=', False)]}"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!--==============Blood Request Tree view===================-->
        <record id="nuro_blood_request_tree_view_sheet" model="ir.ui.view">
            <field name="name">Blood Request</field>
            <field name="model">blood.request</field>
            <field name="arch" type="xml">
                <tree string="Blood Request">
                    <field name="date"/>
                    <field name="name"/>
                    <field name="identification_code"/>
                    <field name="patient_id"/>
                    <field name="blood_group_id"/>
                    <field name="qty"/>
                    <field name="doctor_id"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <!--=================Blood Request Search===================-->
        <record id="nuro_blood_request_search_view" model="ir.ui.view">
            <field name="name">Blood Request Search</field>
            <field name="model">blood.request</field>
            <field name="arch" type="xml">
                <search string="Blood Request Search">
                    <field name="name"/>
                    <field name="blood_group_id"/>
                    <field name="blood_location_id"/>
                    <separator/>
                    <filter string="Today's Request" name="today_request"
                            domain="[('date', '=', current_date),
                            ('date', '&gt;=', current_date)]" context="{}"/>
                    <separator/>
                    <group expand="0" string="Group By..." colspan="11" col="11">
                        <filter string="Blood Group" name="blood_group_group_by"
                                context="{'group_by':'blood_group_id'}"/>
                        <filter string="Blood Location" name="blood_location_group_by"
                                context="{'group_by':'blood_location_id'}"/>
                        <filter string="State" name="state_group_by" context="{'group_by':'state'}"/>
                        <separator/>
                        <filter string="Date" name="date_group_by" context="{'group_by':'date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--===================action for Blood Request=======================-->
        <record id="action_main_nuro_blood_request" model="ir.actions.act_window">
            <field name="name">Blood Request</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">blood.request</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'delete': False}</field>
        </record>

        <record id="blood_request_donor_assessment_form" model="ir.ui.view">
            <field name="name">Donor Assessment</field>
            <field name="model">donor.request.line</field>
            <field name="priority">2</field>
            <field name="arch" type="xml">
                <form string="Blood Donor Assessment">
                    <header>
                        <field name="donation_receiving" invisible="1"/>
                        <field name="cross_match_user_id" invisible="1"/>
                        <field name="blood_donation_id" invisible="1"/>
                        <field name="cross_match_status" invisible="1"/>
                        <field name="assessment_result" invisible="1"/>
                        <field name="questionnaire_result" invisible="1"/>
                        <field name="labtest_completed" invisible="1"/>
                        <field name="health_assessment_result" invisible="1"/>
                        <field name="labtest_id" invisible="1"/>
                        <!--                        <button name="receive_blood_and_reserve" type="object" string="Collect"-->
                        <!--                                attrs="{'invisible': [('donation_receiving', '=', True)]}"-->
                        <!--                                class="oe_highlight"/>-->
                        <!--                        <button name="print_blood_received_label" type="object" string="Print Label"-->
                        <!--                                attrs="{'invisible': [('blood_donation_id', '=', False)]}"-->
                        <!--                                class="oe_highlight"/>-->
                        <!--                        <button name="print_receipt" type="object" string="BT Form" class="oe_highlight"-->
                        <!--                                attrs="{'invisible': [('cross_match_user_id', '=', False)]}"/>-->
                        <!--                        <button name="update_cross_matching_result" type="object" class="oe_highlight"-->
                        <!--                                string="Cross Match"-->
                        <!--                                attrs="{'invisible': ['|', '|', ('labtest_completed', '=', False), ('assessment_result', '!=', 'fit'), ('cross_match_user_id', '!=', False)]}"/>-->
                        <!--                        <button name="start_questionnaire" type="object" class="oe_highlight"-->
                        <!--                                string="Start Questionnaire" states="new"/>-->
                        <!--                        <button name="start_health_assessment" type="object" class="oe_highlight"-->
                        <!--                                string="Start Health Assessment"-->
                        <!--                                attrs="{'invisible': ['|', ('state', '!=', 'questionnaire'), ('questionnaire_result', '!=', 'fit')]}"/>-->
                        <!--                        <button name="complete_assessment" type="object" class="oe_highlight" string="Complete"-->
                        <!--                                attrs="{'invisible': ['|', ('state', '!=', 'health_assessment'), ('assessment_result', '!=', 'fit')]}"/>-->
                        <!--                        <button name="fit_questionnaire" type="object" class="oe_highlight" string="FIT"-->
                        <!--                                attrs="{'invisible': ['|', ('state', '!=', 'questionnaire'), ('questionnaire_result', '!=', False)]}"/>-->
                        <!--                        <button name="not_fit_questionnaire" type="object" class="oe_highlight" string="NOT FIT"-->
                        <!--                                attrs="{'invisible': ['|', ('state', '!=', 'questionnaire'), ('questionnaire_result', '!=', False)]}"/>-->
                        <!--                        <button name="fit_assessment" type="object" class="oe_highlight" string="FIT"-->
                        <!--                                attrs="{'invisible': ['|', ('state', '!=', 'health_assessment'), ('health_assessment_result', '!=', False)]}"/>-->
                        <!--                        <button name="not_fit_assessment" type="object" class="oe_highlight" string="NOT FIT"-->
                        <!--                                attrs="{'invisible': ['|', ('state', '!=', 'health_assessment'), ('health_assessment_result', '!=', False)]}"/>-->
                        <!--                        <button name="create_labtest_request" type="object" string="Lab Request"-->
                        <!--                                attrs="{'invisible': ['|', '|', ('state', '!=', 'completed'),  ('assessment_result', '!=', 'fit'), ('labtest_id', '!=', False)]}"-->
                        <!--                                class="oe_highlight"/>-->
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <field name="labtest_id" invisible="1"/>
                        <widget name="web_ribbon" title="Fit" bg_color="bg-success"
                                attrs="{'invisible': [('assessment_result', '!=', 'fit')]}"/>
                        <widget name="web_ribbon" title="Not Fit" bg_color="bg-danger"
                                attrs="{'invisible': [('assessment_result', '!=', 'not_fit')]}"/>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_labtest_result_create" type="object" string="Lab Result" icon="fa-list"
                                    attrs="{'invisible': [('labtest_id', '=', False)]}"/>
                        </div>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name"/>
                                <h1>
                                    <field name="name" class="oe_inline" readonly="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group>
                                <field name="date" readonly="1"/>
                                <field name="donor_id" readonly="1"/>
                                <field name="contact_number" readonly="1"/>
                                <field name="blood_group_id" readonly="1"/>
                                <field name="qty" readonly="1"/>
                                <field name="company_id" invisible="1"/>
                                <field name="user_id" invisible="1"/>
                                <field name="model_name" invisible="1"/>
                                <field name="model_id" invisible="1"/>
                                <field name="doctor_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="patient_id" readonly="1"/>
                                <field name="identification_code" readonly="1"/>
                                <field name="gender" readonly="1"/>
                                <field name="age" readonly="1"/>
                                <field name="mobile" readonly="1"/>
                                <field name="assessment_result" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Details">
                                <!--                                <separator string="Questionnaire" attrs="{'invisible': [('state', '=', 'new')]}"/>-->
                                <!--                                <field name="question_relation_lines"-->
                                <!--                                       attrs="{'readonly': [('state', '!=', 'questionnaire')]}">-->
                                <!--                                    <tree create="false" editable="bottom" delete="false">-->
                                <!--                                        <field name="question_master_id" readonly="1"/>-->
                                <!--                                        <field name="selection_ids" widget="many2many_tags" readonly="1" invisible="1"/>-->
                                <!--                                        <field name="comment_readonly" invisible="1"/>-->
                                <!--                                        <field name="response_id" domain="[('id', 'in', selection_ids)]"-->
                                <!--                                               options="{'no_open': True, 'no_create': True}"-->
                                <!--                                               attrs="{'readonly': [('comment_readonly', '!=', True)]}"/>-->
                                <!--                                        <field name="comment" attrs="{'readonly': [('comment_readonly', '=', True)]}"/>-->
                                <!--                                    </tree>-->
                                <!--                                </field>-->
                                <!--                                <group>-->
                                <!--                                    <group>-->
                                <!--                                        <field name="questionnaire_result" readonly="1"/>-->
                                <!--                                    </group>-->
                                <!--                                </group>-->
                                <!--                                <separator string="Health Assessment"-->
                                <!--                                           attrs="{'invisible': [('state', 'in', ('new', 'questionnaire'))]}"/>-->
                                <!--                                <field name="assessment_relation_lines"-->
                                <!--                                       attrs="{'readonly': [('state', '!=', 'health_assessment')], 'invisible': [('state', 'in', ('new', 'questionnaire'))]}">-->
                                <!--                                    <tree create="false" editable="bottom" delete="false">-->
                                <!--                                        <field name="question_master_id" readonly="1"/>-->
                                <!--                                        <field name="selection_ids" widget="many2many_tags" readonly="1" invisible="1"/>-->
                                <!--                                        <field name="comment_readonly" invisible="1"/>-->
                                <!--                                        <field name="response_id" domain="[('id', 'in', selection_ids)]"-->
                                <!--                                               options="{'no_open': True, 'no_create': True}"-->
                                <!--                                               attrs="{'readonly': [('comment_readonly', '!=', True)]}"/>-->
                                <!--                                        <field name="comment" attrs="{'readonly': [('comment_readonly', '=', True)]}"/>-->
                                <!--                                    </tree>-->
                                <!--                                </field>-->
                                <!--                                <group>-->
                                <!--                                    <group>-->
                                <!--                                        <field name="health_assessment_result" readonly="1"-->
                                <!--                                               attrs="{'invisible': [('state', 'in', ('new', 'questionnaire'))]}"/>-->
                                <!--                                    </group>-->
                                <!--                                </group>-->

                                <separator string="Cross Matching"
                                           attrs="{'invisible': [('cross_match_status', '=', False)]}"/>
                                <group attrs="{'invisible': [('cross_match_status', '=', False)]}">
                                    <group>
                                        <field name="cross_match_method" invisible="1"/>
                                        <field name="cross_match_method_ids" readonly="1" widget="many2many_tags"/>
                                        <field name="cross_match_status" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="cross_match_date" readonly="1"/>
                                        <field name="cross_match_user_id" readonly="1"
                                               options="{'no_open': True, 'no_create': True}"/>
                                        <field name="issued_user_id" readonly="1"
                                               options="{'no_open': True, 'no_create': True}"/>
                                        <field name="issue_date" readonly="1"
                                               options="{'no_open': True, 'no_create': True}"/>
                                        <field name="taken_by" readonly="1"/>
                                        <field name="taken_by_user_id" readonly="1"
                                               options="{'no_create': True, 'no_open': True}"
                                               attrs="{'invisible': [('taken_by', '!=', 'nurse')]}"/>
                                        <field name="taken_by_family_member" readonly="1"
                                               attrs="{'invisible': [('taken_by', '!=', 'family_member')]}"/>
                                        <field name="taken_date"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="blood_request_donor_assessment_form_donation_form" model="ir.ui.view">
            <field name="name">Donor Assessment</field>
            <field name="model">donor.request.line</field>
            <field name="priority">4</field>
            <field name="arch" type="xml">
                <form string="Blood Donor Assessment">
                    <header>
                        <field name="donation_receiving" invisible="1"/>
                        <field name="cross_match_user_id" invisible="1"/>
                        <field name="blood_donation_id" invisible="1"/>
                        <field name="cross_match_status" invisible="1"/>
                        <field name="assessment_result" invisible="1"/>
                        <field name="questionnaire_result" invisible="1"/>
                        <field name="labtest_completed" invisible="1"/>
                        <field name="health_assessment_result" invisible="1"/>
                        <field name="labtest_id" invisible="1"/>
                        <button name="create_labtest_print_labtest_sample_view" type="object" string="Print Label"
                                class="oe_highlight"
                                attrs="{'invisible': ['|', '|', ('state', '!=', 'completed'),  ('assessment_result', '!=', 'fit'), ('labtest_id', '=', False)]}"/>
                        <button name="start_questionnaire" type="object" class="oe_highlight"
                                string="Start Questionnaire" states="new"/>
                        <button name="start_health_assessment" type="object" class="oe_highlight"
                                string="Start Health Assessment"
                                attrs="{'invisible': ['|', ('state', '!=', 'questionnaire'), ('questionnaire_result', '!=', 'fit')]}"/>
                        <button name="complete_assessment" type="object" class="oe_highlight" string="Complete"
                                attrs="{'invisible': ['|', ('state', '!=', 'health_assessment'), ('assessment_result', '!=', 'fit')]}"/>
                        <button name="fit_questionnaire" type="object" class="oe_highlight" string="FIT"
                                attrs="{'invisible': ['|', ('state', '!=', 'questionnaire'), ('questionnaire_result', '!=', False)]}"/>
                        <button name="not_fit_questionnaire" type="object" class="oe_highlight" string="NOT FIT"
                                attrs="{'invisible': ['|', ('state', '!=', 'questionnaire'), ('questionnaire_result', '!=', False)]}"/>
                        <button name="fit_assessment" type="object" class="oe_highlight" string="FIT"
                                attrs="{'invisible': ['|', ('state', '!=', 'health_assessment'), ('health_assessment_result', '!=', False)]}"/>
                        <button name="not_fit_assessment" type="object" class="oe_highlight" string="NOT FIT"
                                attrs="{'invisible': ['|', ('state', '!=', 'health_assessment'), ('health_assessment_result', '!=', False)]}"/>
                        <button name="create_labtest_request_donation" type="object" string="Lab Request"
                                attrs="{'invisible': ['|', '|', ('state', '!=', 'completed'),  ('assessment_result', '!=', 'fit'), ('labtest_id', '!=', False)]}"
                                class="oe_highlight"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <field name="labtest_id" invisible="1"/>
                        <widget name="web_ribbon" title="Fit" bg_color="bg-success"
                                attrs="{'invisible': [('assessment_result', '!=', 'fit')]}"/>
                        <widget name="web_ribbon" title="Not Fit" bg_color="bg-danger"
                                attrs="{'invisible': [('assessment_result', '!=', 'not_fit')]}"/>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_labtest_result_create" type="object" string="Lab Result" icon="fa-list"
                                    attrs="{'invisible': [('labtest_id', '=', False)]}"/>
                        </div>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="name"/>
                                <h1>
                                    <field name="name" class="oe_inline" readonly="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group>
                                <field name="date" readonly="1"/>
                                <field name="donor_id" readonly="1"/>
                                <field name="contact_number" readonly="1"/>
                                <field name="blood_group_id" readonly="1"/>
                                <field name="qty" readonly="1"/>
                                <field name="company_id" invisible="1"/>
                                <field name="user_id" invisible="1"/>
                                <field name="model_name" invisible="1"/>
                                <field name="model_id" invisible="1"/>
                                <field name="doctor_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="patient_id" readonly="1"/>
                                <field name="identification_code" readonly="1"/>
                                <field name="gender" readonly="1"/>
                                <field name="age" readonly="1"/>
                                <field name="mobile" readonly="1"/>
                                <field name="assessment_result" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Details" attrs="{'invisible': [('state', '=', 'new')]}">
                                <separator string="Questionnaire" attrs="{'invisible': [('state', '=', 'new')]}"/>
                                <field name="question_relation_lines"
                                       attrs="{'readonly': [('state', '!=', 'questionnaire')]}">
                                    <tree create="false" editable="bottom" delete="false">
                                        <field name="question_master_id" readonly="1"/>
                                        <field name="selection_ids" widget="many2many_tags" readonly="1" invisible="1"/>
                                        <field name="comment_readonly" invisible="1"/>
                                        <field name="response_id" domain="[('id', 'in', selection_ids)]"
                                               options="{'no_open': True, 'no_create': True}"
                                               attrs="{'readonly': [('comment_readonly', '!=', True)]}"/>
                                        <field name="comment" attrs="{'readonly': [('comment_readonly', '=', True)]}"/>
                                    </tree>
                                </field>
                                <group>
                                    <group>
                                        <field name="questionnaire_result" readonly="1"/>
                                    </group>
                                </group>
                                <separator string="Health Assessment"
                                           attrs="{'invisible': [('state', 'in', ('new', 'questionnaire'))]}"/>
                                <field name="assessment_relation_lines"
                                       attrs="{'readonly': [('state', '!=', 'health_assessment')], 'invisible': [('state', 'in', ('new', 'questionnaire'))]}">
                                    <tree create="false" editable="bottom" delete="false">
                                        <field name="question_master_id" readonly="1"/>
                                        <field name="selection_ids" widget="many2many_tags" readonly="1" invisible="1"/>
                                        <field name="comment_readonly" invisible="1"/>
                                        <field name="response_id" domain="[('id', 'in', selection_ids)]"
                                               options="{'no_open': True, 'no_create': True}"
                                               attrs="{'readonly': [('comment_readonly', '!=', True)]}"/>
                                        <field name="comment" attrs="{'readonly': [('comment_readonly', '=', True)]}"/>
                                    </tree>
                                </field>
                                <group>
                                    <group>
                                        <field name="health_assessment_result" readonly="1"
                                               attrs="{'invisible': [('state', 'in', ('new', 'questionnaire'))]}"/>
                                    </group>
                                </group>

                                <separator string="Cross Matching"
                                           attrs="{'invisible': [('cross_match_status', '=', False)]}"/>
                                <group attrs="{'invisible': [('cross_match_status', '=', False)]}">
                                    <group>
                                        <field name="cross_match_method" invisible="1"/>
                                        <field name="cross_match_method_ids" readonly="1" widget="many2many_tags"/>
                                        <field name="cross_match_status" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="cross_match_date" readonly="1"/>
                                        <field name="cross_match_user_id" readonly="1"
                                               options="{'no_open': True, 'no_create': True}"/>
                                        <field name="issued_user_id" readonly="1"
                                               options="{'no_open': True, 'no_create': True}"/>
                                        <field name="issue_date" readonly="1"
                                               options="{'no_open': True, 'no_create': True}"/>
                                        <field name="taken_by" readonly="1"/>
                                        <field name="taken_by_user_id" readonly="1"
                                               options="{'no_create': True, 'no_open': True}"
                                               attrs="{'invisible': [('taken_by', '!=', 'nurse')]}"/>
                                        <field name="taken_by_family_member" readonly="1"
                                               attrs="{'invisible': [('taken_by', '!=', 'family_member')]}"/>
                                        <field name="taken_date"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="blood_request_donor_assessment_tree" model="ir.ui.view">
            <field name="name">Donor Assessment</field>
            <field name="model">donor.request.line</field>
            <field name="arch" type="xml">
                <tree string="Blood Donor Assessment">
                    <field name="name" readonly="1"/>
                    <field name="date" readonly="1"/>
                    <field name="donor_id" readonly="1"/>
                    <field name="contact_number" readonly="1"/>
                    <field name="blood_group_id" readonly="1"/>
                    <field name="qty" readonly="1"/>
                    <field name="state" readonly="1"/>
                </tree>
            </field>
        </record>

        <record id="action_blood_request_donor_assessment" model="ir.actions.act_window">
            <field name="name">Donor Assessment</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">donor.request.line</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'delete': False}</field>
        </record>

    </data>
</odoo>