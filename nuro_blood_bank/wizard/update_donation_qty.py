from dateutil.relativedelta import relativedelta
from odoo.exceptions import UserError

from odoo import api, fields, models, _


class BloodDonationUpdateWizard(models.TransientModel):
    _name = 'blood.donation.update.wizard'
    _description = 'Blood Donation Update Wizard'

    donation_id = fields.Many2one('blood.donation', string='Donation')
    donation_qty = fields.Integer('No. Of Units', default=1, readonly=True)
    blood_collection_bag_id = fields.Many2one('blood.collection.bag', string='Blood Bag')
    expiry_date = fields.Date("Expiry Date")
    reserve = fields.Boolean('Reserve')
    patient_id = fields.Many2one('nuro.patient', string='Patient', domain=[('deceased', '!=', True)])

    @api.onchange("blood_collection_bag_id")
    def onchange_blood_collection_bag_id(self):
        """Onchange Blood Collection Bag"""
        if self.blood_collection_bag_id:
            current_date = fields.Date.context_today(self)
            self.expiry_date = current_date + relativedelta(days=self.blood_collection_bag_id.expire_after)
        else:
            self.expiry_date = False

    def update_donation_qty_details(self):
        """Update Donation Qty Details"""
        if not self.donation_id:
            raise UserError(_('Donation Details is not updated.!!!'))
        self.donation_id.write({
            "donation_qty": self.donation_qty,
            "blood_collection_bag_id": self.blood_collection_bag_id.id,
        })
        if self.patient_id:
            self.donation_id.write({
                "patient_id": self.patient_id.id,
                "identification_code": self.patient_id.identification_code,
                "gender": self.patient_id.gender,
                "age": self.patient_id.age,
                "mobile": self.patient_id.mobile,
            })
        self.donation_id.action_done(reserve=self.reserve)
        self.donation_id.state = "donated"
