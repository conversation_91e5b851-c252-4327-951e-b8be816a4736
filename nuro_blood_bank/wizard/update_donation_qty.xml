<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="blood_donation_update_wizard_form" model="ir.ui.view">
            <field name="name">Blood Donation Update</field>
            <field name="model">blood.donation.update.wizard</field>
            <field name="arch" type="xml">
                <form string="Blood Donation Update">
                    <sheet>
                        <group>
                            <field name="donation_id" invisible="1"/>
                            <field name="donation_qty" required="1"/>
                            <field name="blood_collection_bag_id" required="1"
                                   options="{'no_open': True, 'no_create': True}"/>
                            <field name="expiry_date" readonly="1" force_save="1"/>
                            <field name="reserve"/>
                            <field name="patient_id" options="{'no_open': True, 'no_create': True}"
                                   attrs="{'invisible': [('reserve', '!=', True)], 'required': [('reserve', '=', True)]}"/>
                        </group>
                    </sheet>
                    <footer>
                        <button name="update_donation_qty_details" class="oe_highlight" type="object" string="Donate"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_blood_donation_update_wizard" model="ir.actions.act_window">
            <field name="name">Blood Donation Update</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">blood.donation.update.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="blood_donation_update_wizard_form"/>
            <field name="target">new</field>
        </record>

    </data>
</odoo>