<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="view_business_unit_form_lab_form_inherit" model="ir.ui.view">
            <field name="name">business.unit.form</field>
            <field name="model">business.unit</field>
            <field name="inherit_id" ref="base_business_unit.view_business_unit_form"/>
            <field name="arch" type="xml">

                <xpath expr="//page[@name='lines']" position="inside">
                    <separator string="Lab Test"/>
                    <field name="lab_hms_ids" invisible="1"/>
                    <group>
                        <group>
                            <field name="include_all_lab"/>
                        </group>
                    </group>
                    <field name="lab_line_ids" context="{'default_branch_ids': branch_ids, 'default_panel': 'lab'}"
                           attrs="{'invisible': [('include_all_lab', '=', True)]}">
                        <tree editable="bottom">
                            <field name="panel" invisible="1"/>
                            <field name="master_id" required="1" options="{'no_create': True, 'no_create_edit': True}"
                                   domain="[('panel', '=', 'lab'), ('id', 'not in', parent.lab_hms_ids)]"/>
                            <field name="name" force_save="1" invisible="1"/>
                            <field name="branch_ids" options="{'no_create': True, 'no_create_edit': True}"
                                   widget="many2many_tags" required="1" domain="[('id', 'in', parent.branch_ids)]"/>
                        </tree>
                    </field>
                </xpath>

            </field>
        </record>

    </data>
</odoo>