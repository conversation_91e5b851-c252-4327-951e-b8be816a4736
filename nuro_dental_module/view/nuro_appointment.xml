<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="nuro_appointment_main_form_inherit_doctor_form" model="ir.ui.view">
            <field name="name">Nuro Appointment</field>
            <field name="model">nuro.appointment</field>
            <field name="inherit_id"
                   ref="nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                     <field name="rescheduled_dental" force_save="1" invisible="1"/>
                    <button name="dental_appointment_rescheduled" type="object" class="oe_highlight" string="Reschedule"
                            attrs="{'invisible': ['|', ('appointment_type', '!=', 'dental'), ('state', 'not in',
                        ('patient_in','waiting_result','in_review','result_completed'))]}" invisible="context.get('app_hide', False)"/>
                    <button name="update_appointment_reschedule_record" type="object" class="oe_highlight" string="Update" attrs="{'invisible': ['|', '|', ('appointment_type', '!=', 'dental'), ('state', '!=', 'draft'), ('rescheduled_dental', '!=', True)]}" invisible="context.get('app_hide', False)"/>
                </xpath>
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="action_appointment_view_dental_parent" type="object" string="Dental Appointment"
                            icon="fa-list"
                            attrs="{'invisible': [('appointment_type', '!=', 'dental'), ('state', 'not in', ('patient_in','waiting_result','in_review','result_completed'))]}">
                    </button>
                </xpath>

                <xpath expr="//sheet/div[1]" position="after">
                    <div class="oe_right" style="width: 300px;"
                         attrs="{'invisible': [('appointment_type', '!=', 'dental')]}">
                        <div class="oe_title" style="width: 390px;">
                            <label for="total_credit_patient_balance" string="Receivable Amount"/>
                            <h1>
                                <field name="total_credit_patient_balance" class="oe_inline" style="color:brown;"/>
                            </h1>
                        </div>
                    </div>
                </xpath>
                <xpath expr="//page[@name='previous_appointment_history']" position="after">
                    <page name="dental_service_line_page" string="Dental Services"
                          groups="nuro_dental_module.group_dental_doctor_user">
                        <button name="print_dental_receipt_receipt" type="object" string="Print/Send Dental Request"
                                class="oe_highlight" invisible="context.get('app_hide', False)"/>
                        <field name="dental_service_line"
                               attrs="{'readonly': [('state', 'not in', ('patient_in', 'in_review'))]}">
                            <tree editable="bottom">
                                <field name="dental_state" readonly="1" force_save="1" invisible="1"/>
                                <field name="dental_master_id" required="1"
                                       options="{'no_open': True, 'no_create': True}" force_save="1"
                                       attrs="{'readonly': [('dental_state', '!=', 'draft')]}"/>
                                <field name="description" required="1" force_save="1"
                                       attrs="{'readonly': [('dental_state', '!=', 'draft')]}"/>
                                <field name="quantity" force_save="1"
                                       attrs="{'readonly': [('dental_state', '!=', 'draft')]}"/>
                                <field name="charge" force_save="1" readonly="1"/>
                                <field name="total_amount" readonly="1" force_save="1"/>
                                <field name="payment_state" readonly="1" force_save="1" invisible="1"/>
                            </tree>
                            <form>
                                <group>
                                    <group>
                                        <field name="dental_master_id" readonly="1"/>
                                    </group>
                                    <group>
                                        <field name="dental_state" readonly="1"/>
                                    </group>
                                </group>
                            </form>
                        </field>
                    </page>
                    <!--                    <page name="dental_payment_tracking" string="Dental Payment Tracking"-->
                    <!--                          attrs="{'invisible': [('appointment_type', '!=', 'dental')]}">-->
                    <!--                        <field name="payment_tracking_line" readonly="1">-->
                    <!--                            <tree>-->
                    <!--                                <field name="date"/>-->
                    <!--                                <field name="dental_master_id"/>-->
                    <!--                                <field name="description"/>-->
                    <!--                                <field name="amount" sum="Total"/>-->
                    <!--                                <field name="discount" sum="Total"/>-->
                    <!--                                <field name="remark"/>-->
                    <!--                            </tree>-->
                    <!--                        </field>-->
                    <!--                    </page>-->
                </xpath>
            </field>
        </record>

        <record id="nuro_appointment_main_form_inherit_cashier_form_dental" model="ir.ui.view">
            <field name="name">Nuro Appointment</field>
            <field name="model">nuro.appointment</field>
            <field name="inherit_id" ref="nuro_appointment_cashier.nuro_appointment_form_view_sheet_cashier"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <field name="rescheduled_dental" force_save="1" invisible="1"/>
                    <button name="update_send_to_dental_doctor" class="oe_highlight" type="object"
                            string="Send To Doctor"
                            attrs="{'invisible': ['|', '|', '|', ('state', '!=', 'draft'), ('rescheduled_dental', '!=', True), ('appointment_type', '!=', 'dental'), ('type', '!=', 'follow_up')]}"/>
                </xpath>
<!--                <xpath expr="//separator[@name='app_physician_string']" position="attributes">-->
<!--                    <attribute name="attrs">{'invisible': [('appointment_type', 'not in', ('normal', 'dental'))]}-->
<!--                    </attribute>-->
<!--                </xpath>-->

                <!--                <xpath expr="//field[@name='speciality_id']" position="attributes">-->
                <!--                    <attribute name="attrs">{'readonly': [('state', '!=', 'draft')],-->
                <!--                        'required': [('appointment_type', 'in', ('normal', 'emergency', 'dental'))],-->
                <!--                        'invisible': [('appointment_type', 'not in', ('normal', 'emergency', 'dental'))]}-->
                <!--                    </attribute>-->
                <!--                </xpath>-->

                <!--                <xpath expr="//field[@name='doctor_id']" position="attributes">-->
                <!--                    <attribute name="attrs">{'readonly': [('state', '!=', 'draft')],-->
                <!--                        'required': [('appointment_type', 'in', ('normal', 'emergency', 'dental'))],-->
                <!--                        'invisible': [('appointment_type', 'not in', ('normal', 'emergency', 'dental'))]}-->
                <!--                    </attribute>-->
                <!--                </xpath>-->
            </field>
        </record>

        <record id="action_dental_doctor_appointment_menu_new_appointment_type" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', '=', 'scheduled'),
                ('type', '=', 'new'),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_appointment_menu_follow_up_appointment_type" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', '=', 'scheduled'),
                ('type', '=', 'follow_up'),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_appointment_menu_scheduled" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', '=', 'scheduled'),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_all_appointments" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', 'not in', ('draft','cancelled')),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>


        <record id="action_dental_doctor_appointment_menu_patient_in" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', '=', 'patient_in'),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_appointment_menu_waiting_result" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', '=', 'waiting_result'),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_appointment_menu_in_review" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', '=', 'in_review'),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_appointment_menu_result_completed" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', '=', 'result_completed'),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_appointment_menu_completed" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('state', '=', 'completed'),
                ('appointment_type', '=', 'dental'),
                ('doctor_id.user_ids', '=', uid)]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_appointment_menu_transfer_by_me" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('transfer_by_doctor_id.user_id', '=', uid),
                ('appointment_type', '=', 'dental'),
                ('transfer_by_doctor_id', '!=', False)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="action_dental_doctor_appointment_menu_transfer_to_me" model="ir.actions.act_window">
            <field name="name">Doctor's Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('doctor_id.user_ids', '=', uid),
                ('appointment_type', '=', 'dental'),
                ('transfer_by_doctor_id', '!=', False)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'search_default_appointment_date_doctor': 1}</field>
        </record>

        <record id="appointment_dental_rescheduled_dental_appointment_view" model="ir.actions.act_window">
            <field name="name">Rescheduled Appointment</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('rescheduled_dental', '=', True),
                ('appointment_type', '=', 'dental'),
                ('state', 'not in', ('completed', 'cancelled', 'draft')),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="search_view_id" ref="nuro_appointment_doctor.nuro_appointment_search_view_doctor_panel"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_tree_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
        </record>

        <record id="dental_payment_tree_view_cashier_main" model="ir.ui.view">
            <field name="name">Appointment</field>
            <field name="model">nuro.appointment</field>
            <field name="priority">255</field>
            <field name="arch" type="xml">
                <tree string="Dental Payment">
                    <field name="queue" optional="show"/>
                    <field name="name" optional="show"/>
                    <field name="patient_id" optional="show"/>
                    <field name="identification_code" optional="show"/>
                    <field name="mobile" optional="hide"/>
                    <field name="gender" optional="hide"/>
                    <field name="type" optional="show"/>
                    <field name="appointment_type" optional="show"/>
                    <field name="appointment_date" optional="show"/>
                    <field name="doctor_id" optional="show"/>
                    <field name="state" optional="show"/>
                </tree>
            </field>
        </record>

        <record id="action_cashier_appointment_dental_module" model="ir.actions.act_window">
            <field name="name">Dental Cashier</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[('appointment_type', '=', 'dental')]</field>
            <field name="context">{'appointment_type': 'dental', 'create': False, 'edit': False}</field>
            <field name="search_view_id" ref="nuro_appointment_cashier.nuro_appointment_search_view_cashier"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('dental_payment_tree_view_cashier_main')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_cashier.nuro_appointment_graph_view_sheet_for_cashier')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_cashier.nuro_appointment_pivot_view_sheet_for_cashier')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_cashier.nuro_appointment_form_view_sheet_cashier')})]"/>
            <field name="context">{'search_default_appointment_date_cashier': 1}</field>
        </record>


        <!--=====================================Appointment Reschedule================================================-->
        <record id="rescheduled_dental_appointment_tree_view" model="ir.ui.view">
            <field name="name">Dental Appointment</field>
            <field name="model">nuro.appointment</field>
            <field name="priority">251</field>
            <field name="arch" type="xml">
                <tree string="Dental Appointment">
                    <field name="identification_code" optional="show"/>
                    <field name="patient_id" optional="show"/>
                    <field name="mobile" optional="show"/>
                    <field name="appointment_date" optional="show"/>
                    <field name="state" optional="show"/>
                </tree>
            </field>
        </record>


        <record id="dental_appointment_rescheduled_search_view" model="ir.ui.view">
            <field name="name">Dental Appointment</field>
            <field name="model">nuro.appointment</field>
            <field name="priority">251</field>
            <field name="arch" type="xml">
                <search string="Dental Appointment">
                    <field name="patient_id" string="Patient"/>
                    <field name="mobile" string="Mobile"/>
                    <field name="name"/>
                    <field name="doctor_id" string="Doctor"/>
                    <field name="transfer_by_doctor_id"/>
                    <field name="user_id"/>
                    <field name="name"/>
                    <field name="appointment_date"/>
                    <separator/>
                    <filter string="Today" name="Today"
                            domain="[('appointment_date', '&gt;=', current_date), ('appointment_date', '&lt;=', current_date)]"
                            context="{}"/>
                    <filter string="Yesterday" name="yesterday_appointment"
                            domain="[('appointment_date', '=', (context_today()-datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                    <filter string="Tomorrow" name="tomorrow_appointment"
                            domain="[('appointment_date', '=', (context_today()+datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                    <filter string="Day After Tomorrow" name="day_after_tomorrow_appointment"
                            domain="[('appointment_date', '=', (context_today()+datetime.timedelta(days=2)).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Week" name="creation_date_filter"
                            domain="[
                            ('appointment_date', '>=', (datetime.datetime.combine(context_today() + relativedelta(weeks=-1,days=1,weekday=0), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S')),
                            ('appointment_date', '&lt;', (datetime.datetime.combine(context_today() + relativedelta(days=1,weekday=0), datetime.time(0,0,0)).to_utc()).strftime('%Y-%m-%d %H:%M:%S'))
                            ]"/>
                    <filter string="Last Week" name="start"
                            domain="[
                            ('appointment_date', '&gt;=', (context_today()-datetime.timedelta(days=7)).strftime('%Y-%m-%d')),
                            ('appointment_date', '&lt;', (context_today()+datetime.timedelta(days=1)).strftime('%Y-%m-%d'))
                            ]"/>
                    <filter string="This Month" name="current_month"
                            domain="[
                            ('appointment_date','&gt;=',context_today().strftime('%%Y-%%m-01')),
                            ('appointment_date','&lt;',(context_today()+relativedelta(months=1)).strftime('%%Y-%%m-01'))
                            ]"/>
                    <filter string="Last Month" name="start"
                            domain="[
                            ('appointment_date', '&lt;=', (context_today().replace(day=1)-datetime.timedelta(days=1)).strftime('%Y-%m-%d')),
                            ('appointment_date', '&gt;=', (context_today().replace(day=1)-datetime.timedelta(days=1)).replace(day=1).strftime('%Y-%m-%d'))
                            ]"/>
                    <filter string="Last Quarter" name="start"
                            domain="[
                            ('appointment_date', '&lt;=', (context_today().replace(day=1)-datetime.timedelta(days=1)).strftime('%Y-%m-%d')),
                            ('appointment_date', '&gt;=', (((context_today().replace(day=1)-datetime.timedelta(days=1)).replace(day=1)-datetime.timedelta(days=1)).replace(day=1)-datetime.timedelta(days=1)).replace(day=1).strftime('%Y-%m-%d'))
                            ]"/>
                    <filter string="This Year" name="thisyear_appointment"
                            domain="['|',
                            ('appointment_date', '=', False), '&amp;',('appointment_date','&lt;=', time.strftime('%%Y-12-31')),
                            ('appointment_date','&gt;=',time.strftime('%%Y-01-01'))
                            ]"/>
                    <separator/>
                    <filter string="Cash" name="mode_cash" domain="[('payment_method', '=', 'cash')]" context="{}"/>
                    <separator/>
                    <filter string="Credit" name="mode_credit" domain="[('payment_method', '=', 'credit')]"
                            context="{}"/>
                    <filter string="Transferred By Me" name="transfer_by"
                            domain="[('transfer_by_doctor_id.user_id', '=', uid)]" context="{}"/>
                    <filter string="Transferred To Me" name="transfer_to"
                            domain="[('doctor_id.user_id', '=', uid)]" context="{}"/>
                    <group expand="0" string="Group By..." colspan="11" col="11">
                        <filter string="Patient" name="patient_group_by" context="{'group_by':'patient_id'}"/>
                        <filter string="Doctor" name="doctor_group_by" context="{'group_by':'doctor_id'}"/>
                        <filter string="Transfer By" name="transfer_by_group_by"
                                context="{'group_by':'transfer_by_doctor_id'}"/>
                        <filter string="State" name="state_group_by" context="{'group_by':'state'}"/>
                        <filter string="Payment Mode" name="payment_mode_group_by"
                                context="{'group_by':'payment_method'}"/>
                        <filter string="User/Responsible" name="user_group_by" context="{'group_by':'user_id'}"/>
                        <separator/>
                        <filter string="Date" name="date_group_by" context="{'group_by':'appointment_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_doctor_appointment_appointment_dental_reschedule_module" model="ir.actions.act_window">
            <field name="name">Rescheduled Appointments</field>
            <field name="res_model">nuro.appointment</field>
            <field name="view_mode">tree,graph,pivot,form</field>
            <field name="domain">[
                ('appointment_type', '=', 'dental'),
                ('type', '=', 'follow_up'),
                ('rescheduled_dental', '=', True),
                ('doctor_id.user_ids', '=', uid)
                ]
            </field>
            <field name="context">{'appointment_type': 'dental', 'create': False, 'edit': False}</field>
            <field name="search_view_id" ref="dental_appointment_rescheduled_search_view"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('rescheduled_dental_appointment_tree_view')}),
                (0, 0, {'view_mode': 'graph', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_graph_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'pivot', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_pivot_view_sheet_doctor_panel')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_appointment_doctor.nuro_appointment_form_view_sheet_doctor_panel')})]"/>
            <field name="context">{'delete': False, 'create': False}</field>
        </record>
    </data>
</odoo>
