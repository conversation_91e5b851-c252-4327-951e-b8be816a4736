from odoo import api, models, _, fields
from odoo.exceptions import ValidationError, UserError, Warning


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    finance_user_id = fields.Many2one('res.users', 'Finance User Approved by')

    def button_approve(self, force=False):
        result = super(PurchaseOrder, self).button_approve(force=False)
        self.finance_user_id = self.env.user.id
        return result

    def button_confirm(self):
        for order in self:
            if order.state not in ['draft', 'sent']:
                continue
            if order.user_has_groups('nuro_purchase_order_approval.group_purchase_order_approval'):
                order._add_supplier_to_product()
                # Deal with double validation process
                if order.company_id.po_double_validation == 'one_step'\
                        or (order.company_id.po_double_validation == 'two_step'\
                            and order.amount_total < self.env.company.currency_id._convert(
                                order.company_id.po_double_validation_amount, order.currency_id, order.company_id, order.date_order or fields.Date.today()))\
                        or order.user_has_groups('purchase.group_purchase_manager'):
                    order.button_approve()
                else:
                    order.write({'state': 'to approve'})
            else:
                order.write({'state': 'to approve'})
        return True
