# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import fields, models, api, _


class SplitMethod(models.Model):
    _name = 'landed.split.method'
    _desc = 'Split Method'

    name = fields.Char('Split')
    landed_split_method = fields.Selection([
        ('equal', 'Equal'),
        ('by_quantity', 'By Quantity'),
        ('by_current_cost_price', 'By Current Cost'),
        ('by_weight', 'By Weight'),
        ('by_volume', 'By Volume'),
    ])
    company_id = fields.Many2one('res.company',
                                 string='Company', readonly=True)
