<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="oeh_medical_labtest_batch_receiving_form_view" model="ir.ui.view">
            <field name="name">lab.test.batch.sample.receive.form</field>
            <field name="model">lab.test.batch.sample.receive</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="collect_sample_labtest" type="object" string="Receive Sample" states="draft"
                                class="oe_highlight"/>
                        <button name="print_labtest_report" type="object" string="Print Receive Sample Report"
                                states="received" class="oe_highlight"/>
                        <field name="state" widget="statusbar" statusbar_colors='{"draft":"blue"}'/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                            </group>
                            <group colspan="4">
                                <field name="deliver_by" required="1" options="{'no_create': True,'no_open': True}"/>
                                <field name="received_date"/>
                                <field name="received_by"/>
                                <field name="user_id" readonly="1" force_save="1" invisible="1"/>
                            </group>
                            <field name="lab_test_line" nolabel="1" domain="[
                            ('state', '=', 'sample_collected'),
                            ('sample_parent_id', '!=', False)
                            ]" options="{'no_create': True}">
                                <tree>
                                    <field name="state" invisible="1"/>
                                    <field name="name"/>
                                    <field name="patient_id"/>
                                    <field name="department_id"/>
                                    <field name="labtest_master_id"/>
                                </tree>
                                <form>
                                    <group>
                                        <group>
                                            <field name="state" invisible="1"/>
                                            <field name="name"/>
                                            <field name="patient_id"/>
                                        </group>
                                        <group>
                                            <field name="department_id"/>
                                            <field name="labtest_master_id"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="oeh_medical_labtest_batch_receiving_tree_view" model="ir.ui.view">
            <field name="name">lab.test.batch.sample.receive.tree</field>
            <field name="model">lab.test.batch.sample.receive</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="received_date"/>
                    <field name="received_by"/>
                </tree>
            </field>
        </record>

        <record id="oeh_medical_labtest_batch_receiving_search_view" model="ir.ui.view">
            <field name="name">lab.test.batch.sample.receive.search</field>
            <field name="model">lab.test.batch.sample.receive</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name"/>
                    <field name="received_by"/>
                    <group expand="0" string="Group By...">
                        <filter string="Received By" name="received_by" context="{'group_by':'received_by'}"/>
                    </group>
                </search>
            </field>
        </record>


        <record id="action_hms_labtest_batch_sample_received" model="ir.actions.act_window">
            <field name="name">Labtest Sample Received</field>
            <field name="res_model">lab.test.batch.sample.receive</field>
            <field name="view_mode">tree,form</field>
        </record>
    </data>
</odoo>