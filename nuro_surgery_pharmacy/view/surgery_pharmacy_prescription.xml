<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>

        <record id="nuro_medical_prescription_form_view_surgery_view" model="ir.ui.view">
            <field name="name">Prescription</field>
            <field name="model">nuro.prescription.request</field>
            <field name="priority">201</field>
            <field name="arch" type="xml">
                <form string="Prescription Sale">
                    <header>
                        <field name="sale_done" invisible="1"/>
                        <button name="action_create_sale_order" type="object" string="Request"
                                class="oe_highlight" attrs="{'readonly': [('sale_done', '=', True)]}"/>
                        <button name="print_receipt" type="object" string="Print"
                                class="oe_highlight" states="send_to_pharmacy"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,send_to_pharmacy"
                               statusbar_colors="{'draft':'blue','send_to_pharmacy':'grey'}"/>
                    </header>
                    <sheet>
                        <div class="oe_left" style="width: 500px;">
                            <div class="oe_title" style="width: 390px;">
                                <label class="oe_edit_only" for="sale_id" string="Prescription #"/>
                                <h1>
                                    <field name="sale_id" class="oe_inline" default_focus="1" readonly="1"/>
                                </h1>
                            </div>
                        </div>
                        <group>
                            <group>
                                <field name="patient_id" attrs="{'readonly': [('state', '!=', 'draft')]}" required="1"/>
                                <field name="patient_parent_id" readonly="1" force_save="1"
                                       attrs="{'invisible': [('patient_parent_id', '=', False)]}"/>
                                <field name="identification_code" readonly="1" force_save="1"/>
                                <field name="gender" readonly="1" force_save="1"/>
                                <field name="age" readonly="1" force_save="1"/>
                                <field name="mobile" readonly="1" force_save="1"/>
                            </group>
                            <group>
                                <field name="doctor_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="date" readonly="1"/>
                                <field name="product_prescription_ids" widget="many2many_tags" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page name="prescription_line_details" string="Prescription Line">
                                <field name="prescription_line" attrs="{'readonly': [('sale_done', '=', True)]}">
                                    <tree editable="bottom">
                                        <field name="product_id"
                                               domain="[('type', '=', 'product'),('id', 'not in', parent.product_prescription_ids)]"
                                               options="{'no_create': True,'no_open': True}"/>
                                        <field name="default_code"/>
                                        <field name="available_qty"/>
                                        <field name="description"/>
                                        <field name="drug_form_id"/>
                                        <field name="drug_route_id"/>
                                        <field name="frequency" required="1"/>
                                        <field name="dose" string="Time To Take"/>
                                        <field name="duration"/>
                                        <field name="duration_unit"/>
                                        <field name="qty"/>
                                    </tree>
                                </field>
                            </page>
                            <page>
                                <group>
                                    <field name="info" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="nuro_medical_prescription_list_view_surgery" model="ir.ui.view">
            <field name="name">Prescription</field>
            <field name="model">nuro.prescription.request</field>
            <field name="priority">201</field>
            <field name="arch" type="xml">
                <tree string="Prescriptions">
                    <field name="sale_id"/>
                    <field name="patient_id"/>
                    <field name="identification_code"/>
                    <field name="date"/>
                    <field name="doctor_id"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="action_medical_sale_prescription_surgery" model="ir.actions.act_window">
            <field name="name">Prescription</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">nuro.prescription.request</field>
            <field name="view_mode">tree,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('nuro_medical_prescription_list_view_surgery')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('nuro_medical_prescription_form_view_surgery_view')})]"/>
        </record>

    </data>
</odoo>