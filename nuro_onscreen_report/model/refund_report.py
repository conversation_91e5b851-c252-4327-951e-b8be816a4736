# -*- coding: utf-8 -*-
# Copyright  Nuro Solution Pvt Ltd
from odoo import fields, models, tools


class RefundReportView(models.Model):
    _name = 'refund.cashier.report.view'
    _description = "Refund Reports"
    _auto = False
    _order = 'id desc'

    user_id = fields.Many2one('res.users', 'Cashier', readonly=True)
    free_patient = fields.Boolean('Free Patient', default=False)
    invoice_date = fields.Date('Date', readonly=True)
    identification_code = fields.Char(string='ID#', readonly=True)
    patient_id = fields.Many2one('nuro.patient', 'Patient', readonly=True)
    panel = fields.Selection([
        ('appointment', 'Appointment'),
        ('op', 'Other Procedure'),
        ('lab', 'Lab Entry'),
        ('imaging', 'Imaging'),
        ('ws', 'Ward Service'),
        ('inpatient', 'Inpatient'),
        ('surgery', 'Surgery'),
        ('medical_check_up', 'Medical Check Up'),
        ('blood_transfusion', 'Blood Transfusion'),
        ('pharmacy', 'Pharmacy'),
    ], string='Panel', readonly=True)
    reference = fields.Char(string='Ref.', readonly=True)
    doctor_id = fields.Many2one('nuro.doctor', string='Doctor', readonly=True)
    appointment_id = fields.Many2one('nuro.appointment', string='Appointment', readonly=True)
    payment_method = fields.Selection([('cash', 'Cash'), ('credit', 'Credit')], string='Payment Type', readonly=True)
    amount_residual = fields.Monetary(string='Total', readonly=True)
    discount_amount = fields.Float(string='Discount', readonly=True)
    amount_total = fields.Monetary(string='Total', readonly=True)
    net_amount = fields.Monetary(string='Net Amount', readonly=True)
    currency_id = fields.Many2one('res.currency', string='Currency', readonly=True)
    responsible_person_id = fields.Many2one('nuro.responsible.person', string='Responsible Person', readonly=True)

    def init(self):
        """
        Create View Only table
        :return:
        """
        tools.drop_view_if_exists(self._cr, 'refund_cashier_report_view')
        self._cr.execute("""
                create view refund_cashier_report_view as (
                with                 
                table_refund as (
                select 
                        ai.id as id, 
                        ai.date as invoice_date, 
                        ai.patient_id as patient_id,  
                        ai.appointment_id as appointment_id,  
                        rp.identification_code as identification_code, 
                        ai.amount_residual as amount_residual,
                        (CASE 
                        WHEN ai.appointment_id is not null THEN 'appointment'
                        WHEN ai.op_entry_id is not null THEN 'op'
                        WHEN ai.lab_entry_id is not null THEN 'lab'
                        WHEN ai.imaging_entry_id is not null THEN 'imaging'
                        WHEN ai.surgery_entry_id is not null THEN 'surgery'
                        WHEN ai.ws_entry_id is not null THEN 'ws'
                        WHEN ai.medical_test_id is not null THEN 'medical_check_up'
                        WHEN ai.blood_transfusion_id is not null THEN 'blood_transfusion'
                        WHEN ai.sale_id is not null THEN 'pharmacy'
                        WHEN ai.inpatient_id is not null and ai.bed_invoice = True THEN 'inpatient'
                        ELSE NULL END) as panel,
                        (CASE 
                        WHEN ai.appointment_id is not null THEN (select name from nuro_appointment where id = ai.appointment_id)
                        WHEN ai.op_entry_id is not null THEN (select name from nuro_op_entry where id = ai.op_entry_id)
                        WHEN ai.lab_entry_id is not null THEN (select name from nuro_lab_entry where id = ai.lab_entry_id)
                        WHEN ai.imaging_entry_id is not null THEN (select name from nuro_imaging_entry where id = ai.imaging_entry_id)
                        WHEN ai.surgery_entry_id is not null THEN (select name from nuro_surgery_entry where id = ai.surgery_entry_id)
                        WHEN ai.ws_entry_id is not null THEN (select name from ward_service_entry where id = ai.ws_entry_id)
                        WHEN ai.medical_test_id is not null THEN (select name from nuro_medical_test where id = ai.medical_test_id)
                        WHEN ai.blood_transfusion_id is not null THEN (select name from nuro_blood_transfusion where id = ai.blood_transfusion_id)
                        WHEN ai.sale_id is not null THEN (select name from sale_order where id = ai.sale_id)
                        WHEN ai.inpatient_id is not null and ai.bed_invoice = True THEN (select name from nuro_inpatient where id = ai.inpatient_id)
                        ELSE NULL END) as reference,
                        (CASE 
                        WHEN ai.appointment_id is not null THEN (select free_patient from nuro_appointment where id = ai.appointment_id)
                        WHEN ai.op_entry_id is not null THEN (select free_patient from nuro_op_entry where id = ai.op_entry_id)
                        WHEN ai.lab_entry_id is not null THEN (select free_patient from nuro_lab_entry where id = ai.lab_entry_id)
                        WHEN ai.imaging_entry_id is not null THEN (select free_patient from nuro_imaging_entry where id = ai.imaging_entry_id)
                        WHEN ai.surgery_entry_id is not null THEN (select free_patient from nuro_surgery_entry where id = ai.surgery_entry_id)
                        WHEN ai.ws_entry_id is not null THEN False
                        WHEN ai.medical_test_id is not null THEN (select free_patient from nuro_medical_test where id = ai.medical_test_id)
                        WHEN ai.blood_transfusion_id is not null THEN (select free_patient from nuro_blood_transfusion where id = ai.blood_transfusion_id)
                        WHEN ai.sale_id is not null THEN (select free_patient from sale_order where id = ai.sale_id)
                        WHEN ai.inpatient_id is not null and ai.bed_invoice = True THEN (select free_patient from nuro_inpatient where id = ai.inpatient_id)
                        ELSE NULL END) as free_patient,
                        (CASE 
                        WHEN ai.appointment_id is not null THEN (select doctor_id from nuro_appointment where id = ai.appointment_id)
                        WHEN ai.op_entry_id is not null THEN (select doctor_id from nuro_op_entry where id = ai.op_entry_id)
                        WHEN ai.lab_entry_id is not null THEN (select doctor_id from nuro_lab_entry where id = ai.lab_entry_id)
                        WHEN ai.imaging_entry_id is not null THEN (select doctor_id from nuro_imaging_entry where id = ai.imaging_entry_id)
                        WHEN ai.surgery_entry_id is not null THEN (select doctor_id from nuro_surgery_entry where id = ai.surgery_entry_id)
                        WHEN ai.ws_entry_id is not null THEN (select doctor_id from ward_service_entry where id = ai.ws_entry_id)
                        WHEN ai.blood_transfusion_id is not null THEN (select doctor_id from nuro_blood_transfusion where id = ai.blood_transfusion_id)
                        WHEN ai.sale_id is not null THEN (select doctor_id from sale_order where id = ai.sale_id)
                        WHEN ai.inpatient_id is not null and ai.bed_invoice = True THEN (select doctor_id from nuro_inpatient where id = ai.inpatient_id)
                        ELSE NULL END) as doctor_id,
                        (CASE
                        WHEN ai.amount_residual > 0.0 THEN 'credit'
                        WHEN ai.amount_residual = 0.0 THEN 'cash'
                        ELSE NULL END) as payment_method,
                        (select sum(price_unit)*-1 from account_move_line where move_id = ai.id and price_total < 0.0 and not exclude_from_invoice_tab)*-1 as discount_amount, 
                        (CASE
                        WHEN (select sum(price_unit)*-1 from account_move_line where move_id = ai.id and price_total < 0.0 and not exclude_from_invoice_tab) > 0.0 THEN ai.amount_total + (select sum(price_unit)*-1 from account_move_line where move_id = ai.id and price_total < 0.0 and not exclude_from_invoice_tab)
                        ELSE ai.amount_total END)*-1 as amount_total,
                        ai.create_uid as user_id,
                        ai.currency_id as currency_id,
                        ail.responsible_person_id as responsible_person_id
                        from account_move as ai 
                            left join nuro_patient as np on (ai.patient_id = np.id) 
                            left join res_partner as rp on (np.partner_id = rp.id) 
                            left join account_move_line as ail on (ai.id = ail.move_id) 
                            left join account_account as ac on (ac.id=ail.account_id) 
                            left join res_users as ru on (ru.id=ai.create_uid)
                        where 
                            ai.state not in ('draft', 'cancel') 
                            and ac.internal_group in ('expense', 'asset')
                            and (ru.cashier = True or ru.pharmacy_user = True) 
                            and ai.type in ('out_refund', 'entry')
                            and ai.patient_id is not null and 
                            (CASE 
                                WHEN ai.appointment_id is not null THEN 'appointment'
                                WHEN ai.op_entry_id is not null THEN 'op'
                                WHEN ai.lab_entry_id is not null THEN 'lab'
                                WHEN ai.imaging_entry_id is not null THEN 'imaging'
                                WHEN ai.surgery_entry_id is not null THEN 'surgery'
                                WHEN ai.ws_entry_id is not null THEN 'ws'
                                WHEN ai.medical_test_id is not null THEN 'medical_check_up'
                                WHEN ai.blood_transfusion_id is not null THEN 'blood_transfusion'
                                WHEN ai.sale_id is not null THEN 'pharmacy'
                                WHEN ai.inpatient_id is not null and ai.bed_invoice = True THEN 'inpatient'
                                ELSE NULL END) is not null and 
                            (CASE
                                WHEN ai.amount_residual > 0.0 THEN 'credit'
                                WHEN ai.amount_residual = 0.0 THEN 'cash'
                                ELSE NULL END) is not null
                        group by 
                        ai.id, 
                        ai.date, 
                        ai.patient_id, 
                        ai.appointment_id, 
                        ai.create_uid, 
                        ai.amount_total,
                        rp.identification_code,
                        ail.responsible_person_id
                ),
                data as (
                        select * from table_refund
                        )
                select 
                    id, invoice_date, patient_id, identification_code, amount_residual, panel, reference, free_patient, doctor_id,
                    payment_method, discount_amount, amount_total, user_id, appointment_id, currency_id,responsible_person_id, 
                    (case when discount_amount is not null then (amount_total-discount_amount)
                    else amount_total end) as net_amount
                    from data) """)
