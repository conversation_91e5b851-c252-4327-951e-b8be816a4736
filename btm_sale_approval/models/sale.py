from odoo import fields, models, api, _
from odoo.exceptions import ValidationError

class Sale(models.Model):
    _inherit = 'sale.order'

    state = fields.Selection([
        ('draft', 'Quotation'),
        ('sent', 'Quotation Sent'),
        ('waiting', 'Waiting For Approval'),
        ('approved', 'Approved'),
        ('sale', 'Sales Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ], string='Status', readonly=True, copy=False, index=True, tracking=True, default='draft')
    req_user_id = fields.Many2one('res.users', 'Request For Approve')
    approve_user_id = fields.Many2one('res.users', 'Approved By')
    approve_date = fields.Date('Approve Date')

    def action_approve_sale(self):
        # if self.create_uid == self.env.user:
        #     raise ValidationError(_('You can not approve Own Sale order'))
        self.approve_user_id = self.env.user.id
        self.approve_date = str(fields.Date.context_today(self))
        self.write({'state': 'approved'})
