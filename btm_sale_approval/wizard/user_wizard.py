# -*- coding: utf-8 -*-
# Copyright  Nuro Solution Pvt Ltd
from odoo import fields, models, api, _


class SaleApproval(models.TransientModel):
    _name = "sale.approve.wiz"

    user_id = fields.Many2one('res.users', 'Users')
    sale_id = fields.Many2one('sale.order')

    @api.model
    def default_get(self, fields):
        res = super(SaleApproval, self).default_get(fields)
        sale_id = self.env['sale.order'].search([('id', '=', self._context.get('active_ids'))])
        if sale_id:
            res['sale_id'] = sale_id.id
        self.onchange_user()
        return res

    @api.onchange('sale_id')
    def onchange_user(self):
        user_lst = []
        if self.sale_id:
            approval_group = self.env.ref('sales_team.group_sale_manager')
            if approval_group:
                for user in approval_group.users:
                    user_lst.append(user.id)
            return {'domain': {'user_id': [('id', 'in', user_lst)]}}
        return {'domain': {'user_id': [('id', 'in', user_lst)]}}

    def action_confirm(self):
        if self.sale_id:
            self.sale_id.state = 'waiting'
            self.sale_id.req_user_id = self.user_id.id
